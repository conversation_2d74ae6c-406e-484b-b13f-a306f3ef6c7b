com\chatbootmcp\chatmcp\entity\DetailVente.class
com\chatbootmcp\chatmcp\service\AIService.class
com\chatbootmcp\chatmcp\repository\FactureAchatRepository.class
com\chatbootmcp\chatmcp\ChatMcpApplication.class
com\chatbootmcp\chatmcp\entity\Fournisseur.class
com\chatbootmcp\chatmcp\service\UserDetailsServiceImpl.class
com\chatbootmcp\chatmcp\entity\Beneficiaire.class
com\chatbootmcp\chatmcp\repository\ConversationRepository.class
com\chatbootmcp\chatmcp\exception\UnauthorizedException.class
com\chatbootmcp\chatmcp\entity\Client.class
com\chatbootmcp\chatmcp\repository\ClientRepository.class
com\chatbootmcp\chatmcp\entity\Message$MessageRole.class
com\chatbootmcp\chatmcp\entity\User.class
com\chatbootmcp\chatmcp\entity\DetailFactureAchat.class
com\chatbootmcp\chatmcp\repository\UserRepository.class
com\chatbootmcp\chatmcp\dto\request\ConversationRequest.class
com\chatbootmcp\chatmcp\dto\request\TestLoginRequest.class
com\chatbootmcp\chatmcp\util\MockDataGenerator.class
com\chatbootmcp\chatmcp\entity\Message.class
com\chatbootmcp\chatmcp\exception\ResourceNotFoundException.class
com\chatbootmcp\chatmcp\dto\request\LoginRequest.class
com\chatbootmcp\chatmcp\controller\RealDataSimulationWinplus.class
com\chatbootmcp\chatmcp\entity\Conversation.class
com\chatbootmcp\chatmcp\config\DataInitializer.class
com\chatbootmcp\chatmcp\security\JwtAuthenticationFilter.class
com\chatbootmcp\chatmcp\repository\EnteteVenteRepository.class
com\chatbootmcp\chatmcp\dto\response\ConversationResponse.class
com\chatbootmcp\chatmcp\dto\response\AuthResponse.class
com\chatbootmcp\chatmcp\entity\FactureAchat.class
com\chatbootmcp\chatmcp\dto\response\MessageResponse.class
com\chatbootmcp\chatmcp\entity\Produit.class
com\chatbootmcp\chatmcp\service\ConversationService.class
com\chatbootmcp\chatmcp\config\SecurityConfig.class
com\chatbootmcp\chatmcp\entity\EnteteVente.class
com\chatbootmcp\chatmcp\config\CorsConfig.class
com\chatbootmcp\chatmcp\service\MessageService.class
com\chatbootmcp\chatmcp\util\JwtUtil.class
com\chatbootmcp\chatmcp\controller\MessageController.class
com\chatbootmcp\chatmcp\controller\AuthController.class
com\chatbootmcp\chatmcp\exception\GlobalExceptionHandler.class
com\chatbootmcp\chatmcp\repository\MessageRepository.class
com\chatbootmcp\chatmcp\controller\ConversationController.class
com\chatbootmcp\chatmcp\dto\request\MessageRequest.class
com\chatbootmcp\chatmcp\config\WebSocketConfig.class
com\chatbootmcp\chatmcp\repository\ProduitRepository.class
com\chatbootmcp\chatmcp\service\AuthService.class
