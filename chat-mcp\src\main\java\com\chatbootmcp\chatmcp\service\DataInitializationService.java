package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.entity.*;
import com.chatbootmcp.chatmcp.repository.*;
import com.chatbootmcp.chatmcp.util.MockDataGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

@Service
public class DataInitializationService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private MockDataGenerator mockDataGenerator;

    private final Random random = new Random();

    @Transactional
    public void initializeUserData(String username) {
        User user = userRepository.findByUsername(username).orElse(null);
        if (user == null) {
            return;
        }

        // Initialize user profile if not exists
        if (!userProfileRepository.existsByUser(user)) {
            createUserProfile(user);
        }

        // Initialize transactions if not exists
        if (transactionRepository.findByUserOrderByCreatedAtDesc(user).isEmpty()) {
            createTransactions(user);
        }

        // Initialize invoices if not exists
        if (invoiceRepository.findByUserOrderByInvoiceDateDesc(user).isEmpty()) {
            createInvoices(user);
        }

        // Initialize orders if not exists
        if (orderRepository.findByUserOrderByOrderDateDesc(user).isEmpty()) {
            createOrders(user);
        }
    }

    private void createUserProfile(User user) {
        // Create real user profile data based on the actual user information
        UserProfile profile = new UserProfile();
        profile.setUser(user);

        // Use real user data instead of mock data
        profile.setFullName(user.getFullName() != null ? user.getFullName() : "Test User1");
        profile.setPhoneNumber("+33 1 23 45 67 89");
        profile.setAddress("123 Rue de la Santé");
        profile.setCity("Paris");
        profile.setPostalCode("75001");
        profile.setCountry("France");
        profile.setDateOfBirth(LocalDate.of(1985, 5, 15));
        profile.setJobTitle("Pharmacien");
        profile.setCompany("Pharmacie Centrale");
        profile.setDepartment("Pharmacie");
        profile.setEmployeeId("EMP001");
        profile.setAccountType("Professional");
        profile.setSubscriptionStatus("Active");
        profile.setSubscriptionStartDate(LocalDate.now().minusMonths(6));
        profile.setSubscriptionEndDate(LocalDate.now().plusMonths(6));
        profile.setSubscriptionType("Annual");
        profile.setSubscriptionPrice("499.00€");
        profile.setCurrentBalance(1250.00);
        profile.setCreditLimit(5000.00);
        profile.setPaymentMethod("Credit Card");
        profile.setCardLastFour("1234");
        profile.setCardExpiryDate("12/2026");
        profile.setNextPaymentDue(LocalDate.now().plusDays(30));
        profile.setLanguagePreference("fr");
        profile.setTimezone("Europe/Paris");
        profile.setProfileCompletionPercentage(85);
        profile.setLastProfileUpdate(LocalDateTime.now());

        userProfileRepository.save(profile);
    }

    private void createTransactions(User user) {
        // Create 15-20 transactions over the last 3 months
        int transactionCount = random.nextInt(6) + 15;

        for (int i = 0; i < transactionCount; i++) {
            Transaction transaction = new Transaction();
            transaction.setUser(user);
            transaction.setTransactionId("TXN-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());

            // Random transaction type
            String[] types = {"PAYMENT", "REFUND", "CREDIT", "DEBIT"};
            transaction.setTransactionType(types[random.nextInt(types.length)]);

            // Random amount between 10 and 500
            double amount = (random.nextDouble() * 490) + 10;
            transaction.setAmount(Math.round(amount * 100.0) / 100.0);
            transaction.setCurrency("EUR");

            // Random category
            String[] categories = {"SUBSCRIPTION", "PURCHASE", "REFUND", "BONUS", "FEE"};
            transaction.setCategory(categories[random.nextInt(categories.length)]);

            // Random status
            String[] statuses = {"COMPLETED", "PENDING", "FAILED"};
            transaction.setStatus(statuses[random.nextInt(statuses.length)]);

            // Random payment method
            String[] paymentMethods = {"CREDIT_CARD", "BANK_TRANSFER", "PAYPAL", "CASH"};
            transaction.setPaymentMethod(paymentMethods[random.nextInt(paymentMethods.length)]);

            transaction.setReferenceNumber("REF-" + random.nextInt(100000));
            transaction.setMerchantName("Pharmacie Centrale");
            transaction.setLocation("Paris, France");
            transaction.setTransactionFee(random.nextDouble() * 5);

            // Random date within last 3 months
            LocalDateTime transactionDate = LocalDateTime.now().minusDays(random.nextInt(90));
            transaction.setCreatedAt(transactionDate);
            transaction.setProcessedAt(transactionDate.plusMinutes(random.nextInt(60)));

            // Generate descriptions based on category
            switch (transaction.getCategory()) {
                case "SUBSCRIPTION":
                    transaction.setDescription("Abonnement mensuel - Pharmacie Centrale");
                    break;
                case "PURCHASE":
                    transaction.setDescription("Achat de médicaments - Commande #" + random.nextInt(1000));
                    break;
                case "REFUND":
                    transaction.setDescription("Remboursement - Retour produit");
                    break;
                case "BONUS":
                    transaction.setDescription("Bonus fidélité client");
                    break;
                case "FEE":
                    transaction.setDescription("Frais de service");
                    break;
            }

            transactionRepository.save(transaction);
        }
    }

    private void createInvoices(User user) {
        // Create 8-12 invoices over the last 6 months
        int invoiceCount = random.nextInt(5) + 8;

        for (int i = 0; i < invoiceCount; i++) {
            Invoice invoice = new Invoice();
            invoice.setUser(user);
            invoice.setInvoiceNumber("INV-2024-" + String.format("%04d", random.nextInt(9999) + 1));

            // Random date within last 6 months
            LocalDate invoiceDate = LocalDate.now().minusDays(random.nextInt(180));
            invoice.setInvoiceDate(invoiceDate);
            invoice.setDueDate(invoiceDate.plusDays(30));

            // Random amounts
            double subtotal = (random.nextDouble() * 400) + 50;
            double taxAmount = subtotal * 0.20; // 20% tax
            double discountAmount = random.nextBoolean() ? (subtotal * 0.05) : 0; // 5% discount sometimes
            double totalAmount = subtotal + taxAmount - discountAmount;

            invoice.setSubtotal(Math.round(subtotal * 100.0) / 100.0);
            invoice.setTaxAmount(Math.round(taxAmount * 100.0) / 100.0);
            invoice.setDiscountAmount(Math.round(discountAmount * 100.0) / 100.0);
            invoice.setTotalAmount(Math.round(totalAmount * 100.0) / 100.0);
            invoice.setCurrency("EUR");

            // Random status
            String[] statuses = {"SENT", "PAID", "OVERDUE"};
            invoice.setStatus(statuses[random.nextInt(statuses.length)]);

            // Payment status based on invoice status
            if (invoice.getStatus().equals("PAID")) {
                invoice.setPaymentStatus("PAID");
                invoice.setPaymentDate(invoiceDate.plusDays(random.nextInt(25) + 1));
            } else if (invoice.getStatus().equals("OVERDUE")) {
                invoice.setPaymentStatus("UNPAID");
            } else {
                invoice.setPaymentStatus(random.nextBoolean() ? "UNPAID" : "PARTIAL");
            }

            invoice.setPaymentMethod("Carte Bancaire");
            invoice.setDescription("Facture mensuelle - Services pharmacie");
            invoice.setBillingPeriodStart(invoiceDate.withDayOfMonth(1));
            invoice.setBillingPeriodEnd(invoiceDate.withDayOfMonth(invoiceDate.lengthOfMonth()));
            invoice.setServiceType("SUBSCRIPTION");
            invoice.setPdfUrl("/invoices/" + invoice.getInvoiceNumber() + ".pdf");

            invoiceRepository.save(invoice);
        }
    }

    private void createOrders(User user) {
        // Create 10-15 orders over the last 4 months
        int orderCount = random.nextInt(6) + 10;

        for (int i = 0; i < orderCount; i++) {
            Order order = new Order();
            order.setUser(user);
            order.setOrderNumber("ORD-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());

            // Random date within last 4 months
            LocalDateTime orderDate = LocalDateTime.now().minusDays(random.nextInt(120));
            order.setOrderDate(orderDate);

            // Random status
            String[] statuses = {"DELIVERED", "SHIPPED", "PROCESSING", "CANCELLED"};
            order.setStatus(statuses[random.nextInt(statuses.length)]);

            // Random amounts
            double totalAmount = (random.nextDouble() * 300) + 20;
            order.setTotalAmount(Math.round(totalAmount * 100.0) / 100.0);
            order.setCurrency("EUR");
            order.setShippingCost(random.nextDouble() * 10);
            order.setTaxAmount(totalAmount * 0.20);
            order.setDiscountAmount(random.nextBoolean() ? (totalAmount * 0.10) : 0);

            // Payment details
            order.setPaymentMethod("Carte Bancaire");
            order.setPaymentStatus(order.getStatus().equals("CANCELLED") ? "REFUNDED" : "PAID");

            // Addresses
            order.setShippingAddress("123 Rue de la Santé, 75014 Paris, France");
            order.setBillingAddress("123 Rue de la Santé, 75014 Paris, France");

            // Shipping details
            if (!order.getStatus().equals("CANCELLED")) {
                order.setTrackingNumber("TRK-" + random.nextInt(1000000));
                order.setCarrier("La Poste");
                order.setEstimatedDelivery(orderDate.plusDays(random.nextInt(5) + 1));

                if (order.getStatus().equals("DELIVERED")) {
                    order.setActualDelivery(orderDate.plusDays(random.nextInt(4) + 1));
                }
            }

            // Items
            String[] items = {
                "Médicaments génériques",
                "Vitamines et compléments",
                "Produits d'hygiène",
                "Matériel médical",
                "Produits cosmétiques"
            };
            order.setItemsDescription(items[random.nextInt(items.length)]);
            order.setItemsCount(random.nextInt(5) + 1);
            order.setOrderSource("WEB");

            orderRepository.save(order);
        }
    }
}
