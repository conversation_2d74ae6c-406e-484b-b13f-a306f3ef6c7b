{"ast": null, "code": "export function createErrorClass(createImpl) {\n  const _super = instance => {\n    Error.call(instance);\n    instance.stack = new Error().stack;\n  };\n  const ctorFunc = createImpl(_super);\n  ctorFunc.prototype = Object.create(Error.prototype);\n  ctorFunc.prototype.constructor = ctorFunc;\n  return ctorFunc;\n}", "map": {"version": 3, "names": ["createErrorClass", "createImpl", "_super", "instance", "Error", "call", "stack", "ctorFunc", "prototype", "Object", "create", "constructor"], "sources": ["C:/Users/<USER>/Downloads/Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/rxjs/dist/esm/internal/util/createErrorClass.js"], "sourcesContent": ["export function createErrorClass(createImpl) {\n    const _super = (instance) => {\n        Error.call(instance);\n        instance.stack = new Error().stack;\n    };\n    const ctorFunc = createImpl(_super);\n    ctorFunc.prototype = Object.create(Error.prototype);\n    ctorFunc.prototype.constructor = ctorFunc;\n    return ctorFunc;\n}\n"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,UAAU,EAAE;EACzC,MAAMC,MAAM,GAAIC,QAAQ,IAAK;IACzBC,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC;IACpBA,QAAQ,CAACG,KAAK,GAAG,IAAIF,KAAK,CAAC,CAAC,CAACE,KAAK;EACtC,CAAC;EACD,MAAMC,QAAQ,GAAGN,UAAU,CAACC,MAAM,CAAC;EACnCK,QAAQ,CAACC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,KAAK,CAACI,SAAS,CAAC;EACnDD,QAAQ,CAACC,SAAS,CAACG,WAAW,GAAGJ,QAAQ;EACzC,OAAOA,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}