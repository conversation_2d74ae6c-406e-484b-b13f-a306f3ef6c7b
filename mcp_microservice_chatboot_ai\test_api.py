#!/usr/bin/env python3
"""
Simple test script to test the MCP Microservice API
"""

import requests
import json

def test_chat_api():
    url = "http://localhost:8081/api/chat"

    # Test data
    chat_request = {
        "conversationId": "test-winmcp-medical",
        "content": "quelles sont mes informations médicales personnelles?",
        "username": "user1"
    }

    headers = {
        "Content-Type": "application/json"
    }

    print("Testing MCP Microservice API...")
    print(f"URL: {url}")
    print(f"Request: {json.dumps(chat_request, indent=2)}")

    try:
        response = requests.post(url, json=chat_request, headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("✅ API test successful!")
        else:
            print("❌ API test failed!")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_chat_api()
