package com.example.mcp_microservice_chatboot_ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Represents a chat message in the system.
 */

/**
 * Role: Entity class representing a chat message
    Purpose:
    Stores message data (ID, conversation ID, role, content, timestamp)
    Provides builder pattern for easy creation
    Represents both user and assistant messages
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {

    /**
     * The unique identifier of the message.
     */
    private String id;

    /**
     * The ID of the conversation this message belongs to.
     */
    private String conversationId;

    /**
     * The role of the message sender (user or assistant).
     */
    private MessageRole role;

    /**
     * The content of the message.
     */
    private String content;

    /**
     * The timestamp when the message was created.
     */
    private LocalDateTime timestamp;

    /**
     * Enum representing the possible roles for a message.
     */
    public enum MessageRole {
        USER,
        ASSISTANT,
        SYSTEM
    }
}
