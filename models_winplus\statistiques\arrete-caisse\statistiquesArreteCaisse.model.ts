import { ModesPaiement } from "./modesPaiement.model";



export class StatistiquesArreteCaisse { 
    categorieDietetiques?: ModesPaiement;
    categorieParapharmacie?: ModesPaiement;
    categorieSpecialites?: ModesPaiement;
    chiffreAffaireBrut?: number;
    chiffreAffaireNetComptant?: number;
    chiffreAffaireNetCredit?: number;
    chiffreAffaireNetDiffere?: number;
    chiffreAffaireTiersPayant?: number;
    reglementsCredits?: ModesPaiement;
    reglementsDifferes?: ModesPaiement;
    totalMontantReglementTiersPayant?: number;
    totalMontantRemisesReglement?: number;
    totalMontantRemisesVente?: number;
    totalMontantTva?: number;
    totalNbrVentes?: number;
    totalNbrVentesTiersPayant?: number;
    totauxEncaissementVente?: ModesPaiement;
    ventesComptant?: ModesPaiement;
    ventesTiersPayant?: ModesPaiement;
}
