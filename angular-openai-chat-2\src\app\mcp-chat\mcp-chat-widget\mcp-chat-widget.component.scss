@import "../../../assets/scss/custom/plugins/icons/materialdesignicons";
// Variables
$primary-color: #4a6cfa;
$primary-light: #eef1ff;
$primary-dark: #3452d9;
$secondary-color: #f8f9fd;
$text-color: #333333;
$text-light: #6e7191;
$border-color: #e4e8f7;
$shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
$shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
$success-color: #34d399;
$danger-color: #f87171;
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

// Breakpoints
$mobile-breakpoint: 480px;
$tablet-breakpoint: 768px;

.chat-widget {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000;
  font-family: $font-family;

  @media (max-width: $mobile-breakpoint) {
    bottom: 16px;
    right: 16px;
  }
}

// Chat button (collapsed state)
.chat-button {
  width: 50px; // Reduced width for icon-only view
  padding: 0;
  height: 50px;
  border-radius: 30px;
  background-color: #f0f7f9;
  color: #276578;
  border: 2px solid #276578;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $shadow-heavy;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative; // Added for better positioning

  @media (max-width: $mobile-breakpoint) {
    height: 45px;
  }

  &:hover {
    width: 180px; // Expand to full width on hover
    padding: 0 20px;
    justify-content: flex-start; // Changed to flex-start for better alignment

    svg {
      margin-right: 10px; // Add space between icon and text
    }

    .chat-button-text {
      opacity: 1;
      transform: translateX(0);
      visibility: visible;
      margin-left: 5px; // Add some space after the icon
    }
  }

  .chat-icon {
    font-size: 24px;

    @media (max-width: $mobile-breakpoint) {
      font-size: 20px;
    }
  }

  .chat-button-text {
    font-size: 16px;
    font-weight: 500;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    visibility: hidden;
    white-space: nowrap;
    position: absolute; // Position it absolutely
    left: 60px; // Position it to the right of the icon

    @media (max-width: $mobile-breakpoint) {
      font-size: 14px;
    }
  }

  svg {
    width: 35px;
    height: 35px;
    color: $text-light;
    transition: all 0.2s ease;
    visibility: visible !important; // Always visible
    opacity: 1 !important; // Always visible
    z-index: 1; // Ensure it's above other elements

    @media (max-width: $mobile-breakpoint) {
      width: 28px;
      height: 28px;
    }
  }
}

// Chat container (expanded state)
.chat-container {
  width: 450px;
  height: 750px;
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: $shadow-heavy;
  border: 1px solid $border-color;
  animation: slideUp 0.3s ease-out;

  @media (max-width: $tablet-breakpoint) {
    width: 90vw;
    max-width: 450px;
    height: 80vh;
    max-height: 750px;
  }

  @media (max-width: $mobile-breakpoint) {
    width: 100vw;
    height: 100vh;
    max-height: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    animation: mobileSlideUp 0.3s ease-out;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobileSlideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Chat header
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 20px;
  background-color: white;
  border-bottom: 1px solid $border-color;

  @media (max-width: $mobile-breakpoint) {
    padding: 10px 16px;
  }

  .back-button {
    background: none;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: $text-light;
    transition: all 0.2s ease;

    &:hover {
      background-color: $secondary-color;
      color: $text-color;
    }
  }

  .header-title {
    font-weight: 600;
    font-size: 16px;
    color: $text-color;
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;

    @media (max-width: $mobile-breakpoint) {
      font-size: 15px;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-tabs {
    display: flex;
    gap: 8px;

    @media (max-width: $mobile-breakpoint) {
      gap: 4px;
    }

    .tab {
      padding: 8px;
      width: 32px;
      height: 32px;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: all 0.2s ease;
      color: $text-light;

      &:hover:not(.active) {
        background-color: $secondary-color;
        color: $text-color;
      }

      &.active {
        background-color: $primary-light;
        color: $primary-color;
      }
    }
  }

  .close-button {
    background: hsla(11, 100%, 62.2%, 1);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: #fff;
    transition: all 0.2s ease;

    &:hover {
      background-color: $secondary-color;
      color: $text-color;
    }
  }
}

// Chat content
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  position: relative;
  background-color: $secondary-color;

  // Common styles for all tabs
  .home-tab, .chat-tab, .help-tab, .login-tab {
    height: 100%;
    overflow-y: auto;
    padding: 0;
  }

  // Conversation list
  .conversation-list-container {
    padding: 16px;

    .new-chat-btn {
      width: 100%;
      padding: 12px 16px;
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background-color: $primary-dark;
      }

      .new-icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    .conversation-list {
      .conversation-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        background-color: white;
        transition: all 0.2s ease;

        &:hover {
          background-color: $primary-light;
        }

        &.active {
          background-color: $primary-light;
          border-left: 3px solid $primary-color;
        }

        .conv-info {
          display: flex;
          flex-direction: column;

          .conv-title {
            font-weight: 500;
            color: $text-color;
            margin-bottom: 4px;
          }

          .conv-date {
            font-size: 12px;
            color: $text-light;
          }
        }

        .delete-btn {
          background: none;
          border: none;
          color: $text-light;
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(255, 0, 0, 0.1);
            color: $danger-color;
          }
        }
      }
    }
  }

  // Conversation view
  .conversation-view {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// Chat footer
.chat-footer {
  padding: 8px 16px;
  border-top: 1px solid $border-color;
  background-color: white;

  .footer-nav {
    display: flex;
    justify-content: space-around;

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: $text-light;

      i {
        font-size: 20px;
        margin-bottom: 4px;
      }

      span {
        font-size: 12px;
        font-weight: 500;
      }

      &:hover:not(.active) {
        color: $text-color;
      }

      &.active {
        color: $primary-color;
      }
    }
  }
}

// Error message
.error-message {
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  background-color: rgba(255, 0, 0, 0.1);
  color: $danger-color;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
  animation: fadeIn 0.3s ease-out;

  .close-error {
    background: none;
    border: none;
    color: $danger-color;
    cursor: pointer;
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
