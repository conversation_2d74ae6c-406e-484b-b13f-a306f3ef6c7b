package com.example.mcp_microservice_chatboot_ai.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for authentication responses.
 */

/**
 * Role: Data Transfer Object for authentication responses
    Purpose:
    Carries authentication token and user information
    Used to return authentication results to the client
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {
    
    /**
     * The JWT token for authenticated requests.
     */
    private String token;
    
    /**
     * The username of the authenticated user.
     */
    private String username;
    
    /**
     * The user's display name.
     */
    private String displayName;
}
