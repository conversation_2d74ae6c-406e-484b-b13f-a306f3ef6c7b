import { StatutProcessFactureTPA } from 'src/app/winpharm/enums/vente/StatutProcessFactureTPA.enum';

// import { Moment } from 'moment';

import { ConventionAssurance } from 'src/app/winpharm/models/assurance/conventionAssurance.model';
import { OrganismeAssurance } from 'src/app/winpharm/models/tiers/organisme/organismeAssurance.model';
import { Beneficiaire } from '../tiers/client/beneficiaire.model';


export class FactureTpaCriteria {
    convention?: ConventionAssurance;
    dateDebut?: any;
    dateFin?: any;
    envoyeesUniquement?: boolean;
    assure?: Beneficiaire
    nomAssure?: string;
    nonEnvoyeesUniquement?: boolean;
    nonRegleesUniquement?: boolean;
    organisme?: OrganismeAssurance;
    regleesUniquement?: boolean;
    statutProcess?: StatutProcessFactureTPA;

    listStatutProcess?: StatutProcessFactureTPA[]
}

