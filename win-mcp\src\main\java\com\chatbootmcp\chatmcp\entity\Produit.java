package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.math.BigDecimal;

@Entity
@Table(name = "produits")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Produit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "audited")
    private Boolean audited;
    
    @Column(name = "code_barre")
    private String codeBarre;
    
    @Column(name = "code_prd")
    private String codePrd;
    
    @Column(name = "code_groupe")
    private String codeGroupe;
    
    @Column(name = "date_fin_com")
    private LocalDateTime dateFinCom;
    
    @Column(name = "designation")
    private String designation;
    
    @Column(name = "dosage")
    private String dosage;
    
    @Column(name = "est_fabrique")
    private Boolean estFabrique;
    
    @Column(name = "est_marche")
    private Boolean estMarche;
    
    @Column(name = "est_oblg_prescription")
    private Boolean estOblgPrescription;
    
    @Column(name = "est_princeps")
    private Boolean estPrinceps;
    
    @Column(name = "est_prix_marque")
    private Boolean estPrixmarque;
    
    @Column(name = "est_psychotrope")
    private Boolean estPsychotrope;
    
    @Column(name = "est_rbrsbl_base")
    private Boolean estRbrsblBase;
    
    @Column(name = "est_stockable")
    private Boolean estStockable;
    
    @Column(name = "est_toxique")
    private Boolean estToxique;
    
    @Column(name = "est_tpa_base")
    private Boolean estTpaBase;
    
    @Column(name = "est_vendable")
    private Boolean estVendable;
    
    @Column(name = "idhash")
    private String idhash;
    
    @Column(name = "nom_racine")
    private String nomRacine;
    
    @Column(name = "pbr_h", precision = 19, scale = 2)
    private BigDecimal pbrH;
    
    @Column(name = "pbr_p", precision = 19, scale = 2)
    private BigDecimal pbrP;
    
    @Column(name = "presentation")
    private String presentation;
    
    @Column(name = "prix_achat_std", precision = 19, scale = 2)
    private BigDecimal prixAchatStd;
    
    @Column(name = "prix_fab_ht", precision = 19, scale = 2)
    private BigDecimal prixFabHt;
    
    @Column(name = "prix_hosp", precision = 19, scale = 2)
    private BigDecimal prixHosp;
    
    @Column(name = "prix_vente_std", precision = 19, scale = 2)
    private BigDecimal prixVenteStd;
    
    @Column(name = "taux_remb", precision = 5, scale = 2)
    private BigDecimal tauxRemb;
    
    @Column(name = "total_stock", precision = 19, scale = 3)
    private BigDecimal totalStock;
    
    @Column(name = "type_process")
    private String typeProcess;
    
    @Column(name = "user_modifiable")
    private Boolean userModifiable;
    
    @Column(name = "pph_ttc", precision = 19, scale = 2)
    private BigDecimal pphTtc;
    
    @Column(name = "date_validation")
    private LocalDateTime dateValidation;
    
    @Column(name = "date_annulation")
    private LocalDateTime dateAnnulation;
    
    @Column(name = "is_suggestion")
    private Boolean isSuggestion;
    
    @Column(name = "motif_annul_suggestion")
    private String motifAnnulSuggestion;
    
    @Column(name = "suggestion_prd_id")
    private Long suggestionPrdId;
    
    @Column(name = "suggestion_code_prd")
    private String suggestionCodePrd;
    
    @Column(name = "suggestion_tenant_code")
    private String suggestionTenantCode;
    
    @Column(name = "suggestion_tenant_raison_sociale")
    private String suggestionTenantRaisonSociale;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
}
