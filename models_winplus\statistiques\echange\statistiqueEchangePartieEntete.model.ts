import { TypePrixEchange } from 'src/app/winpharm/enums/echange/TypePrixEchange.enum';
import { SensEchange } from 'src/app/winpharm/enums/echange/SensEchange.enum';

// import { Moment } from 'moment';

import { ConfrereAbrege } from 'src/app/winpharm/models/tiers/confrere/confrereAbrege.model';


export class StatistiqueEchangePartieEntete { 
    confrere?: ConfrereAbrege;
    dateEchange?: any;
    numeroEchange?: string;
    sensEchange?: SensEchange;
    typePrixEchange?: TypePrixEchange;
}

