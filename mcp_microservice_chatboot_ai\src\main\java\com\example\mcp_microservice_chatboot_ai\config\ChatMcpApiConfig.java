
package com.example.mcp_microservice_chatboot_ai.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Configuration class for the Chat MCP API client.
 * This class sets up the WebClient bean used to communicate with the chat-mcp API.
 */

/**
 * Role: Configuration class for the Chat MCP API client
    Purpose:
    Creates a WebClient bean configured to communicate with the chat-mcp API
    Sets up the base URL and other configuration for API communication
 */
@Configuration
public class ChatMcpApiConfig {

    @Value("${chat-mcp.api.url}")
    private String chatMcpApiUrl;

    /**
     * Creates a WebClient bean configured to communicate with the chat-mcp API.
     * 
     * @return A WebClient bean
     */
    @Bean
    public WebClient chatMcpWebClient() {
        return WebClient.builder()
                .baseUrl(chatMcpApiUrl)
                .build();
    }
}
