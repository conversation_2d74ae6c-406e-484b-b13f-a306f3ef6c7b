import { TypeControlHoraireLogin } from "../../enums/parametre/typeControlHoraireLogin.enum";
import { TypeControlLieuLogin } from "../../enums/parametre/typeControlLieuLogin.enum";
import { HoraireControlLoginJournalier } from "./horaireControlLoginJournalier.model";



export class ParametresUser {
    audited?: boolean;
    plafondUser?: number;
    userModifiable?: boolean;

    typeControlHoraireLogin?: TypeControlHoraireLogin

    customHorairesLogin?: HoraireControlLoginJournalier[];

    typeControlLieuLogin?: TypeControlLieuLogin;

    specificLieux?: string[] = [];

    logoutAfterEachVente?: boolean


    constructor() {

    }
}

