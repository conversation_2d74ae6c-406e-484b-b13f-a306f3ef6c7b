package com.chatbootmcp.chatmcp.dto.response;

import com.chatbootmcp.chatmcp.entity.Conversation;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class ConversationResponse {
    private Long id;
    private String title;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public ConversationResponse(Conversation conversation) {
        this.id = conversation.getId();
        this.title = conversation.getTitle();
        this.createdAt = conversation.getCreatedAt();
        this.updatedAt = conversation.getUpdatedAt();
    }
}
