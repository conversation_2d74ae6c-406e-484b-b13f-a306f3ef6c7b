# Agentic AI Chatbot MCP Architecture

## Overview

This project consists of three interconnected applications that form a complete AI chatbot ecosystem using the MCP (Microservice Control Plane) architecture pattern. The system provides both direct OpenAI integration and MCP-based chat functionality with database integration.

## 🏗️ Architecture Components

### 1. **chat-mcp** (Demo Backend - Port 8080)
**Purpose**: Demo backend service providing authentication, conversation management, and user data APIs.

**Key Features**:
- JWT-based authentication system
- RESTful API endpoints for chat operations
- H2 in-memory database with JPA/Hibernate
- Mock user data generation
- CORS configuration for cross-origin requests
- WebSocket support for real-time communication

**Package Structure**:
```
com.chatbootmcp.chatmcp/
├── config/          # Security, CORS, WebSocket configuration
├── controller/      # REST API endpoints (Auth, Conversation, Message, UserData)
├── dto/            # Data Transfer Objects (requests/responses)
├── entity/         # JPA entities (User, Conversation, Message, UserData)
├── exception/      # Global exception handling
├── repository/     # JPA repositories
├── service/        # Business logic services
└── util/           # JWT utilities and mock data generators
```

**API Endpoints**:
- `POST /api/auth/login` - User authentication
- `GET/POST /api/conversations` - Conversation management
- `GET/POST /api/messages` - Message handling
- `GET /api/users/data` - User data retrieval

### 2. **mcp_microservice_chatboot_ai** (MCP Server - Port 8081)
**Purpose**: AI-powered microservice implementing the MCP pattern with OpenAI integration and intelligent conversation handling.

**Key Features**:
- OpenAI GPT-4 integration with Spring AI
- Intelligent message classification (greeting vs. question)
- Dual-source response system (database vs. general knowledge)
- Source selection prompting for every non-friendly message
- WebClient integration with chat-mcp backend
- Reactive programming with Spring WebFlux
- Comprehensive user data formatting and processing

**Package Structure**:
```
com.example.mcp_microservice_chatboot_ai/
├── config/         # OpenAI, WebClient, MCP tool configurations
├── controller/     # Chat API endpoints
├── model/          # Data models and DTOs
├── service/        # AI chat service and MCP API service
└── resources/      # Application properties and configurations
```

**Core Functionality**:
- **Message Classification**: Automatically detects friendly messages vs. questions
- **Source Selection**: Prompts users to choose between database (option 1) or general knowledge (option 2)
- **Conversation State Management**: Maintains context while forcing source selection for each question
- **Data Integration**: Connects to chat-mcp for user data or uses mock data
- **French Language Support**: All responses in French with natural conversation flow

### 3. **angular-openai-chat-2** (Frontend - Port 4200)
**Purpose**: Angular-based chat interface providing dual-mode operation (Direct OpenAI vs. MCP Server).

**Key Features**:
- Dual chat implementation (Direct OpenAI + MCP Server)
- Modern, responsive chat widget design
- Real-time conversation management
- User authentication and session handling
- Configurable backend switching
- TypeScript with Angular 17

**Module Structure**:
```
src/app/
├── chat/           # Direct OpenAI chat components
├── mcp-chat/       # MCP server chat components
├── environments/   # Configuration files
└── shared/         # Common utilities and services
```

**Components**:
- **Chat Widget**: Direct OpenAI integration using Assistant API
- **MCP Chat Widget**: Integration with MCP server
- **Conversation Management**: Thread handling and message history
- **Authentication**: Login/logout functionality
- **Help System**: User guidance and documentation

## 🔄 Workflow Pipeline

```mermaid
graph TB
    A[Angular Frontend<br/>Port 4200] --> B{Mode Selection}

    B -->|Direct OpenAI| C[OpenAI Assistant API<br/>api.openai.com]
    B -->|MCP Server| D[MCP Microservice<br/>Port 8081]

    D --> E{Message Type}
    E -->|Greeting| F[Direct AI Response]
    E -->|Question| G[Source Selection Prompt]

    G --> H{User Choice}
    H -->|Option 1: Database| I[Chat-MCP Backend<br/>Port 8080]
    H -->|Option 2: General| J[OpenAI API<br/>General Knowledge]

    I --> K[User Data Retrieval]
    K --> L[Formatted Response]
    J --> L

    L --> M[AI Processing]
    M --> N[French Response]
    N --> A

    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style I fill:#e8f5e8
    style C fill:#fff3e0
    style J fill:#fff3e0
```

## 🔧 Configuration Requirements

### Environment Variables
```properties
# MCP Microservice (Port 8081)
openai.api-key=your-openai-api-key
chat-mcp.api.url=http://localhost:8080/api

# Angular Frontend (Port 4200)
mcpServerUrl=http://localhost:8081
openaiApiKey=your-openai-api-key
useMcpServer=true

# Chat-MCP Backend (Port 8080)
spring.datasource.url=jdbc:h2:mem:testdb
jwt.secret=your-jwt-secret
```

## 🔄 Using with Other Backends

To integrate the **mcp_microservice_chatboot_ai** and **angular-openai-chat-2** with your primary backend instead of the demo **chat-mcp**, you need:

### Backend Requirements

1. **Authentication Endpoint**:
   ```
   POST /api/auth/login
   Request: {"username": "string", "password": "string"}
   Response: {"token": "jwt-token", "username": "string"}
   ```

2. **User Data Endpoint**:
   ```
   GET /api/users/data?username={username}
   Headers: Authorization: Bearer {token}
   Response: {user data object with required fields}
   ```

3. **Conversation Management** (Optional):
   ```
   GET/POST /api/conversations
   GET/POST /api/messages
   ```

### Required Data Fields
Your backend should provide user data with these fields:
```json
{
  "username": "string",
  "fullName": "string",
  "email": "string",
  "role": "string",
  "pharmacy": "string",
  "address": "string",
  "phoneNumber": "string",
  "subscriptionStatus": "string",
  "subscriptionExpiry": "date",
  "currentBalance": "string",
  "lastOrder": "string",
  "orderHistory": ["array of strings"],
  "accountStatus": "string"
}
```

### Configuration Changes

1. **Update MCP Microservice**:
   ```properties
   # application.properties
   chat-mcp.api.url=http://your-backend-url/api
   ```

2. **Modify ChatMcpApiService.java**:
   ```java
   public Mono<Object> getUserData(String username) {
       return fetchUserData(username); // Use real API instead of mock
   }
   ```

3. **Update Angular Environment**:
   ```typescript
   export const environment = {
     mcpServerUrl: "http://localhost:8081", // Keep MCP server URL
     // No changes needed for Angular - it communicates through MCP server
   };
   ```

### CORS Configuration
Ensure your backend allows requests from:
- `http://localhost:4200` (Angular frontend)
- `http://localhost:8081` (MCP microservice)

## 🚀 Deployment Ports

| Service | Port | Purpose |
|---------|------|---------|
| Angular Frontend | 4200 | User interface |
| MCP Microservice | 8081 | AI processing and orchestration |
| Chat-MCP Backend | 8080 | Demo backend (replaceable) |
| Your Primary Backend | Custom | Production data source |

## 📋 Key Benefits

1. **Modular Architecture**: Each component can be developed and deployed independently
2. **Dual Integration**: Supports both direct OpenAI and MCP-mediated interactions
3. **Intelligent Routing**: Automatically determines response source based on query type
4. **Language Consistency**: All responses in French with natural conversation flow
5. **Scalable Design**: Easy to integrate with different backends
6. **Real-time Processing**: Reactive programming for optimal performance

This architecture provides a robust foundation for AI-powered chat applications with flexible backend integration capabilities.

## 🛠️ Development Setup

### Prerequisites
- **Java 17+** for Spring Boot applications
- **Node.js 18+** and **Angular CLI 17+** for frontend
- **Maven 3.6+** for Java build management
- **OpenAI API Key** for AI functionality

### Quick Start Commands

1. **Start Chat-MCP Backend**:
   ```bash
   cd chat-mcp
   ./mvnw spring-boot:run
   # Runs on http://localhost:8080
   ```

2. **Start MCP Microservice**:
   ```bash
   cd mcp_microservice_chatboot_ai
   ./mvnw spring-boot:run
   # Runs on http://localhost:8081
   ```

3. **Start Angular Frontend**:
   ```bash
   cd angular-openai-chat-2
   npm install
   ng serve
   # Runs on http://localhost:4200
   ```

## 🔐 Security Considerations

### Authentication Flow
1. **Frontend** → **MCP Server**: No authentication required (internal communication)
2. **MCP Server** → **Backend**: JWT token-based authentication
3. **Frontend** → **OpenAI**: Direct API key usage (for direct mode)

### Data Privacy
- User conversations are processed through OpenAI API
- Personal data is retrieved from your backend database
- No sensitive data is stored in the MCP microservice
- JWT tokens are cached temporarily for session management

## 🧪 Testing Endpoints

### MCP Microservice Testing
```bash
# Test greeting message
curl -X POST http://localhost:8081/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content":"Hello, how are you?","conversationId":"test123","username":"user1"}'

# Test database question
curl -X POST http://localhost:8081/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content":"What is my full name?","conversationId":"test123","username":"user1"}'
```

### Chat-MCP Backend Testing
```bash
# Test authentication
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"user1","password":"password"}'

# Test user data retrieval
curl -X GET "http://localhost:8080/api/users/data?username=user1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📊 Performance Metrics

### Expected Response Times
- **Greeting Messages**: < 500ms
- **Database Queries**: 1-2 seconds
- **General Knowledge**: 2-4 seconds
- **OpenAI Direct**: 3-6 seconds

### Scalability Considerations
- **MCP Microservice**: Stateless, horizontally scalable
- **Chat-MCP Backend**: Database-dependent, vertical scaling recommended
- **Angular Frontend**: CDN-deployable, globally distributed

## 🔄 Migration Path to Production Backend

### Step-by-Step Integration

1. **Prepare Your Backend**:
   - Implement required API endpoints
   - Configure CORS for MCP microservice
   - Set up JWT authentication
   - Ensure data format compatibility

2. **Update MCP Configuration**:
   ```properties
   # application.properties
   chat-mcp.api.url=https://your-production-backend.com/api
   chat-mcp.api.auth-endpoint=/auth/login
   chat-mcp.api.user-data-endpoint=/users/data
   ```

3. **Modify Data Service**:
   ```java
   // ChatMcpApiService.java
   public Mono<Object> getUserData(String username) {
       return fetchUserData(username); // Remove mock data fallback
   }
   ```

4. **Test Integration**:
   - Verify authentication flow
   - Test user data retrieval
   - Validate response formatting
   - Check error handling

5. **Deploy and Monitor**:
   - Deploy MCP microservice with new configuration
   - Monitor API response times
   - Set up logging and alerting
   - Implement health checks

## 📈 Monitoring and Observability

### Key Metrics to Track
- **API Response Times**: Monitor OpenAI and backend API latency
- **Error Rates**: Track authentication and data retrieval failures
- **Conversation Flow**: Monitor source selection patterns
- **User Engagement**: Track message types and conversation length

### Logging Configuration
```properties
# Enhanced logging for production
logging.level.com.example.mcp_microservice_chatboot_ai=INFO
logging.level.org.springframework.web.reactive.function.client=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
```

## 🎯 Future Enhancements

### Planned Features
- **Multi-language Support**: Extend beyond French
- **Advanced Analytics**: User behavior tracking
- **Caching Layer**: Redis integration for performance
- **WebSocket Support**: Real-time conversation updates
- **Admin Dashboard**: System monitoring and configuration

### Integration Possibilities
- **Voice Integration**: Speech-to-text and text-to-speech
- **File Upload**: Document analysis capabilities
- **Third-party APIs**: CRM, ERP system integration
- **Mobile Apps**: React Native or Flutter clients

This comprehensive architecture enables rapid development and deployment of AI-powered chat solutions while maintaining flexibility for various backend integrations and future enhancements.



# Shéma

![AI Chatboot MCP Schema](ai-chatboot-mcp-svg-shema-html-2025-05-29-12_07_25.png)