package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Transaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "transaction_id", unique = true, nullable = false)
    private String transactionId;
    
    @Column(name = "transaction_type")
    private String transactionType; // PAYMENT, REFUND, CREDIT, DEBIT
    
    @Column(name = "amount", nullable = false)
    private Double amount;
    
    @Column(name = "currency")
    private String currency;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "category")
    private String category; // SUBSCRIPTION, PURCHASE, REFUND, BONUS
    
    @Column(name = "status")
    private String status; // PENDING, COMPLETED, FAILED, CANCELLED
    
    @Column(name = "payment_method")
    private String paymentMethod; // CREDIT_CARD, BANK_TRANSFER, PAYPAL, etc.
    
    @Column(name = "reference_number")
    private String referenceNumber;
    
    @Column(name = "merchant_name")
    private String merchantName;
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "transaction_fee")
    private Double transactionFee;
    
    @Column(name = "balance_after")
    private Double balanceAfter;
    
    @Column(name = "notes")
    private String notes;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "processed_at")
    private LocalDateTime processedAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
