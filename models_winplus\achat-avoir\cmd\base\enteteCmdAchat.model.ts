import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';

// import { Moment } from 'moment';

import { DetailCmdAchat } from 'src/app/winpharm/models/achat-avoir/cmd/base/detailCmdAchat.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { CmdBlRemoteFournisseur } from './cmdBlRemoteFournisseur';


export class EnteteCmdAchat { 
    
    blRemoteFournisseur?: CmdBlRemoteFournisseur;
    audited?: boolean;
    codeFrnsr?: string;
    dateCmd?: any;
    dateCreation?: any;
    dateEnvoiHub?: any;
    destinationCmd?: string;
    detailCmdAchats?: DetailCmdAchat[];
    fournisseur?: Fournisseur;
    id?: number;
    libelleFrnsr?: string;
    mntAchatStd?: number;
    mntBrutHt?: number;
    mntBrutTtc?: number;
    mntNetHt?: number;
    mntNetTtc?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTva?: number;
    mntVenteStd?: number;
    nbrLigne?: number;
    numCmd?: number;
    operateur?: Operateur;
    statut?: Statut;
    totalQtCmd?: number;
    userModifiable?: boolean;
}

