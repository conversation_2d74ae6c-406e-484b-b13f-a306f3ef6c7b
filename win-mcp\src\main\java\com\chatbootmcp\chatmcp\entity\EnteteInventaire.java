package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * EnteteInventaire entity representing inventory headers
 */
@Entity
@Table(name = "entete_inventaire")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnteteInventaire {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero_inventaire")
    private Integer numeroInventaire;

    @Column(name = "date_creation")
    private LocalDateTime dateCreation;

    @Column(name = "date_inventaire")
    private LocalDateTime dateInventaire;

    @Column(name = "methode", length = 50)
    private String methode;

    @Column(name = "statut_inventaire", length = 50)
    private String statutInventaire;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "depot_id")
    private Depot depot;
}
