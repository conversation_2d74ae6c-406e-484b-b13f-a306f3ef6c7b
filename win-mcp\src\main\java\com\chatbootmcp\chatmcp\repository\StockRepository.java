package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Stock;
import com.chatbootmcp.chatmcp.entity.Produit;
import com.chatbootmcp.chatmcp.entity.Depot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Repository interface for Stock entity
 */
@Repository
public interface StockRepository extends JpaRepository<Stock, Long> {
    
    List<Stock> findByProduit(Produit produit);
    
    List<Stock> findByDepot(Depot depot);
    
    List<Stock> findByProduitAndDepot(Produit produit, Depot depot);
    
    @Query("SELECT s FROM Stock s WHERE s.qteUnit > 0")
    List<Stock> findStockWithQuantity();
    
    @Query("SELECT s FROM Stock s WHERE s.qteUnit < :threshold")
    List<Stock> findLowStock(@Param("threshold") BigDecimal threshold);
    
    @Query("SELECT s FROM Stock s WHERE s.datePeremption <= :date")
    List<Stock> findExpiringStock(@Param("date") LocalDate date);
    
    @Query("SELECT SUM(s.qteUnit) FROM Stock s WHERE s.produit = :produit")
    BigDecimal getTotalStockForProduct(@Param("produit") Produit produit);
    
    @Query("SELECT s FROM Stock s WHERE s.produit.codePrd = :codePrd")
    List<Stock> findByProductCode(@Param("codePrd") String codePrd);
}
