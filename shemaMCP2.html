<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2266.01953125 1190" style="max-width: 2266.01953125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c"><style>#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .error-icon{fill:#a44141;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .error-text{fill:#ddd;stroke:#ddd;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edge-thickness-normal{stroke-width:1px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edge-thickness-thick{stroke-width:3.5px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edge-pattern-solid{stroke-dasharray:0;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .marker.cross{stroke:lightgrey;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c p{margin:0;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .cluster-label text{fill:#F9FFFE;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .cluster-label span{color:#F9FFFE;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .cluster-label span p{background-color:transparent;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .label text,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c span{fill:#ccc;color:#ccc;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node rect,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node circle,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node ellipse,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node polygon,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .rough-node .label text,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node .label text,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .image-shape .label,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .icon-shape .label{text-anchor:middle;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .rough-node .label,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node .label,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .image-shape .label,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .icon-shape .label{text-align:center;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .node.clickable{cursor:pointer;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .arrowheadPath{fill:lightgrey;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .cluster text{fill:#F9FFFE;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .cluster span{color:#F9FFFE;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c rect.text{fill:none;stroke-width:0;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .icon-shape,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .icon-shape p,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .icon-shape rect,#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph4" class="cluster"><rect height="306" width="764.65625" y="619" x="8" style=""></rect><g transform="translate(331.0625, 619)" class="cluster-label"><foreignObject height="24" width="118.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🧠 AI Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="385" width="691.828125" y="797" x="1566.19140625" style=""></rect><g transform="translate(1835.30859375, 797)" class="cluster-label"><foreignObject height="24" width="153.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🏥 Win-MCP Backend</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="385" width="753.53515625" y="797" x="792.65625" style=""></rect><g transform="translate(1089.455078125, 797)" class="cluster-label"><foreignObject height="24" width="159.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💼 Chat-MCP Backend</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="513" width="1189.39453125" y="210" x="897.9140625" style=""></rect><g transform="translate(1392.611328125, 210)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 MCP Orchestration Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="128" width="273.9453125" y="8" x="938.765625" style=""></rect><g transform="translate(1007.92578125, 8)" class="cluster-label"><foreignObject height="24" width="135.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 Frontend Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B1_0" d="M1129.71,313L1134.942,317.167C1140.174,321.333,1150.638,329.667,1155.87,337.333C1161.102,345,1161.102,352,1161.102,355.5L1161.102,359"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B1_B2_1" d="M1161.102,417L1161.102,423.167C1161.102,429.333,1161.102,441.667,1161.102,453.333C1161.102,465,1161.102,476,1161.102,481.5L1161.102,487"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B2_B3_2" d="M1170.266,545L1172.359,551.167C1174.452,557.333,1178.638,569.667,1180.731,582C1182.824,594.333,1182.824,606.667,1182.824,616.333C1182.824,626,1182.824,633,1182.824,636.5L1182.824,640"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B2_B4_3" d="M1256.883,525.842L1371.196,535.202C1485.509,544.562,1714.135,563.281,1828.449,578.807C1942.762,594.333,1942.762,606.667,1942.762,616.333C1942.762,626,1942.762,633,1942.762,636.5L1942.762,640"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C1_4" d="M1260.449,880.608L1289.74,888.007C1319.03,895.405,1377.611,910.203,1406.901,921.768C1436.191,933.333,1436.191,941.667,1436.191,949.333C1436.191,957,1436.191,964,1436.191,967.5L1436.191,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C2_5" d="M1225.304,900L1229.843,904.167C1234.381,908.333,1243.458,916.667,1247.997,925C1252.535,933.333,1252.535,941.667,1252.535,949.333C1252.535,957,1252.535,964,1252.535,967.5L1252.535,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C3_6" d="M1113.87,900L1106.503,904.167C1099.136,908.333,1084.402,916.667,1077.035,925C1069.668,933.333,1069.668,941.667,1069.668,949.333C1069.668,957,1069.668,964,1069.668,967.5L1069.668,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C4_7" d="M1105.199,878.07L1069.63,885.892C1034.061,893.713,962.923,909.357,927.354,921.345C891.785,933.333,891.785,941.667,891.785,949.333C891.785,957,891.785,964,891.785,967.5L891.785,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C1_C5_8" d="M1436.191,1029L1436.191,1033.167C1436.191,1037.333,1436.191,1045.667,1418.715,1055.938C1401.239,1066.209,1366.287,1078.418,1348.811,1084.523L1331.335,1090.627"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_C5_9" d="M1252.535,1029L1252.535,1033.167C1252.535,1037.333,1252.535,1045.667,1252.559,1053.333C1252.583,1061,1252.631,1068,1252.655,1071.5L1252.679,1075"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C3_C5_10" d="M1069.668,1029L1069.668,1033.167C1069.668,1037.333,1069.668,1045.667,1087.158,1055.94C1104.649,1066.213,1139.63,1078.427,1157.12,1084.533L1174.61,1090.64"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C4_C5_11" d="M891.785,1029L891.785,1033.167C891.785,1037.333,891.785,1045.667,938.896,1058.181C986.006,1070.695,1080.227,1087.391,1127.338,1095.738L1174.448,1104.086"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D1_12" d="M2017.215,883.093L2040.753,890.077C2064.29,897.062,2111.366,911.031,2134.904,922.182C2158.441,933.333,2158.441,941.667,2158.441,949.333C2158.441,957,2158.441,964,2158.441,967.5L2158.441,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D2_13" d="M1973.488,900L1976.77,904.167C1980.053,908.333,1986.618,916.667,1989.901,925C1993.184,933.333,1993.184,941.667,1993.184,949.333C1993.184,957,1993.184,964,1993.184,967.5L1993.184,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D3_14" d="M1876.778,900L1869.728,904.167C1862.679,908.333,1848.58,916.667,1841.53,925C1834.48,933.333,1834.48,941.667,1834.48,949.333C1834.48,957,1834.48,964,1834.48,967.5L1834.48,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D4_15" d="M1868.309,878.543L1835.448,886.286C1802.587,894.029,1736.866,909.514,1704.005,921.424C1671.145,933.333,1671.145,941.667,1671.145,949.333C1671.145,957,1671.145,964,1671.145,967.5L1671.145,971"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_D5_16" d="M2158.441,1029L2158.441,1033.167C2158.441,1037.333,2158.441,1045.667,2146.238,1054.989C2134.034,1064.311,2109.627,1074.622,2097.423,1079.778L2085.22,1084.933"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D5_17" d="M1993.184,1029L1993.184,1033.167C1993.184,1037.333,1993.184,1045.667,1993.94,1053.348C1994.696,1061.03,1996.208,1068.06,1996.964,1071.575L1997.72,1075.089"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_D5_18" d="M1834.48,1029L1834.48,1033.167C1834.48,1037.333,1834.48,1045.667,1850.169,1055.655C1865.858,1065.644,1897.236,1077.287,1912.924,1083.109L1928.613,1088.931"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_D5_19" d="M1671.145,1029L1671.145,1033.167C1671.145,1037.333,1671.145,1045.667,1714.026,1058.006C1756.908,1070.345,1842.671,1086.691,1885.552,1094.863L1928.434,1103.036"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E1_20" d="M322.016,687.913L294.764,693.76C267.513,699.608,213.01,711.304,185.759,723.319C158.508,735.333,158.508,747.667,158.508,760C158.508,772.333,158.508,784.667,158.508,796.333C158.508,808,158.508,819,158.508,824.5L158.508,830"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E2_21" d="M400.828,698L400.828,702.167C400.828,706.333,400.828,714.667,400.828,725C400.828,735.333,400.828,747.667,400.828,760C400.828,772.333,400.828,784.667,400.828,796.333C400.828,808,400.828,819,400.828,824.5L400.828,830"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E3_22" d="M479.641,688.679L505.142,694.399C530.643,700.119,581.646,711.56,607.147,723.446C632.648,735.333,632.648,747.667,632.648,760C632.648,772.333,632.648,784.667,632.648,796.333C632.648,808,632.648,819,632.648,824.5L632.648,830"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_23" d="M1118.224,111L1122.229,115.167C1126.234,119.333,1134.244,127.667,1138.249,138C1142.254,148.333,1142.254,160.667,1142.254,173C1142.254,185.333,1142.254,197.667,1138.711,207.519C1135.168,217.372,1128.082,224.744,1124.539,228.43L1120.996,232.116"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B3_C_24" d="M1182.824,698L1182.824,702.167C1182.824,706.333,1182.824,714.667,1182.824,725C1182.824,735.333,1182.824,747.667,1182.824,760C1182.824,772.333,1182.824,784.667,1182.824,794.333C1182.824,804,1182.824,811,1182.824,814.5L1182.824,818"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B4_D_25" d="M1942.762,698L1942.762,702.167C1942.762,706.333,1942.762,714.667,1942.762,725C1942.762,735.333,1942.762,747.667,1942.762,760C1942.762,772.333,1942.762,784.667,1942.762,794.333C1942.762,804,1942.762,811,1942.762,814.5L1942.762,818"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B2_E_26" d="M1139.304,545L1134.326,551.167C1129.347,557.333,1119.39,569.667,1114.412,582C1109.434,594.333,1109.434,606.667,1005.133,620.487C900.832,634.308,692.231,649.616,587.931,657.27L483.63,664.924"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_B_27" d="M479.641,664.243L567.592,656.703C655.543,649.162,831.445,634.081,919.396,620.374C1007.348,606.667,1007.348,594.333,1007.348,577.5C1007.348,560.667,1007.348,539.333,1007.348,518C1007.348,496.667,1007.348,475.333,1007.348,454C1007.348,432.667,1007.348,411.333,1007.348,392C1007.348,372.667,1007.348,355.333,1011.623,342.938C1015.899,330.543,1024.45,323.086,1028.726,319.357L1033.001,315.629"></path><path marker-end="url(#mermaid-d985febe-ddd6-4765-a3d8-da402d74271c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_A_28" d="M1043.252,235L1039.247,230.833C1035.242,226.667,1027.233,218.333,1023.228,208C1019.223,197.667,1019.223,185.333,1019.223,173C1019.223,160.667,1019.223,148.333,1022.766,138.481C1026.309,128.628,1033.394,121.256,1036.937,117.57L1040.48,113.884"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1142.25390625, 173)" class="edgeLabel"><g transform="translate(-49.8046875, -12)" class="label"><foreignObject height="24" width="99.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>User Question</p></span></div></foreignObject></g></g><g transform="translate(1182.82421875, 760)" class="edgeLabel"><g transform="translate(-38.9609375, -12)" class="label"><foreignObject height="24" width="77.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Fetch Data</p></span></div></foreignObject></g></g><g transform="translate(1942.76171875, 760)" class="edgeLabel"><g transform="translate(-38.9609375, -12)" class="label"><foreignObject height="24" width="77.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Fetch Data</p></span></div></foreignObject></g></g><g transform="translate(1109.43359375, 582)" class="edgeLabel"><g transform="translate(-53.390625, -12)" class="label"><foreignObject height="24" width="106.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Process with AI</p></span></div></foreignObject></g></g><g transform="translate(1007.34765625, 454)" class="edgeLabel"><g transform="translate(-72.125, -12)" class="label"><foreignObject height="24" width="144.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Intelligent Response</p></span></div></foreignObject></g></g><g transform="translate(1019.22265625, 173)" class="edgeLabel"><g transform="translate(-53.2265625, -12)" class="label"><foreignObject height="24" width="106.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>JSON Response</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1080.73828125, 72)" id="flowchart-A-255" class="node default"><rect height="78" width="171.828125" y="-39" x="-85.9140625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-55.9140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="111.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Angular Chat UI<br>Port 4200</p></span></div></foreignObject></g></g><g transform="translate(1080.73828125, 274)" id="flowchart-B-256" class="node default"><rect height="78" width="183.484375" y="-39" x="-91.7421875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-61.7421875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="123.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MCP Microservice<br>Port 8081</p></span></div></foreignObject></g></g><g transform="translate(1161.1015625, 390)" id="flowchart-B1-257" class="node default"><rect height="54" width="158.75" y="-27" x="-79.375" style="" class="basic label-container"></rect><g transform="translate(-49.375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AiChatService</p></span></div></foreignObject></g></g><g transform="translate(1161.1015625, 518)" id="flowchart-B2-258" class="node default"><rect height="54" width="191.5625" y="-27" x="-95.78125" style="" class="basic label-container"></rect><g transform="translate(-65.78125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="131.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Smart MCP System</p></span></div></foreignObject></g></g><g transform="translate(1182.82421875, 671)" id="flowchart-B3-259" class="node default"><rect height="54" width="225.4375" y="-27" x="-112.71875" style="" class="basic label-container"></rect><g transform="translate(-82.71875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="165.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ChatMcpToolsFunctions</p></span></div></foreignObject></g></g><g transform="translate(1942.76171875, 671)" id="flowchart-B4-260" class="node default"><rect height="54" width="219.09375" y="-27" x="-109.546875" style="" class="basic label-container"></rect><g transform="translate(-79.546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="159.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WinMcpToolsFunctions</p></span></div></foreignObject></g></g><g transform="translate(1182.82421875, 861)" id="flowchart-C-269" class="node default"><rect height="78" width="155.25" y="-39" x="-77.625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-47.625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="95.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Chat-MCP API<br>Port 8080</p></span></div></foreignObject></g></g><g transform="translate(1436.19140625, 1002)" id="flowchart-C1-270" class="node default"><rect height="54" width="150" y="-27" x="-75" style="" class="basic label-container"></rect><g transform="translate(-45, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Profiles</p></span></div></foreignObject></g></g><g transform="translate(1252.53515625, 1002)" id="flowchart-C2-271" class="node default"><rect height="54" width="117.3125" y="-27" x="-58.65625" style="" class="basic label-container"></rect><g transform="translate(-28.65625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="57.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Invoices</p></span></div></foreignObject></g></g><g transform="translate(1069.66796875, 1002)" id="flowchart-C3-272" class="node default"><rect height="54" width="148.421875" y="-27" x="-74.2109375" style="" class="basic label-container"></rect><g transform="translate(-44.2109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="88.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Transactions</p></span></div></foreignObject></g></g><g transform="translate(891.78515625, 1002)" id="flowchart-C4-273" class="node default"><rect height="54" width="107.34375" y="-27" x="-53.671875" style="" class="basic label-container"></rect><g transform="translate(-23.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="47.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Orders</p></span></div></foreignObject></g></g><g transform="translate(1252.97265625, 1118)" id="flowchart-C5-274" class="node default"><rect height="78" width="149.171875" y="-39" x="-74.5859375" style="" class="basic label-container"></rect><g transform="translate(-44.5859375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="89.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>H2 Database<br>chatmcp_db</p></span></div></foreignObject></g></g><g transform="translate(1942.76171875, 861)" id="flowchart-D-291" class="node default"><rect height="78" width="148.90625" y="-39" x="-74.453125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-44.453125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="88.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Win-MCP API<br>Port 8082</p></span></div></foreignObject></g></g><g transform="translate(2158.44140625, 1002)" id="flowchart-D1-292" class="node default"><rect height="54" width="109.140625" y="-27" x="-54.5703125" style="" class="basic label-container"></rect><g transform="translate(-24.5703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="49.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Clients</p></span></div></foreignObject></g></g><g transform="translate(1993.18359375, 1002)" id="flowchart-D2-293" class="node default"><rect height="54" width="121.375" y="-27" x="-60.6875" style="" class="basic label-container"></rect><g transform="translate(-30.6875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="61.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Products</p></span></div></foreignObject></g></g><g transform="translate(1834.48046875, 1002)" id="flowchart-D3-294" class="node default"><rect height="54" width="96.03125" y="-27" x="-48.015625" style="" class="basic label-container"></rect><g transform="translate(-18.015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="36.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Sales</p></span></div></foreignObject></g></g><g transform="translate(1671.14453125, 1002)" id="flowchart-D4-295" class="node default"><rect height="54" width="130.640625" y="-27" x="-65.3203125" style="" class="basic label-container"></rect><g transform="translate(-35.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="70.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Purchases</p></span></div></foreignObject></g></g><g transform="translate(2006.94921875, 1118)" id="flowchart-D5-296" class="node default"><rect height="78" width="149.171875" y="-39" x="-74.5859375" style="" class="basic label-container"></rect><g transform="translate(-44.5859375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="89.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>H2 Database<br>winplusdb</p></span></div></foreignObject></g></g><g transform="translate(400.828125, 671)" id="flowchart-E-313" class="node default"><rect height="54" width="157.625" y="-27" x="-78.8125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-48.8125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="97.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OpenAI GPT-4</p></span></div></foreignObject></g></g><g transform="translate(158.5078125, 861)" id="flowchart-E1-314" class="node default"><rect height="54" width="231.015625" y="-27" x="-115.5078125" style="" class="basic label-container"></rect><g transform="translate(-85.5078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="171.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Question Understanding</p></span></div></foreignObject></g></g><g transform="translate(400.828125, 861)" id="flowchart-E2-315" class="node default"><rect height="54" width="153.625" y="-27" x="-76.8125" style="" class="basic label-container"></rect><g transform="translate(-46.8125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="93.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Analysis</p></span></div></foreignObject></g></g><g transform="translate(632.6484375, 861)" id="flowchart-E3-316" class="node default"><rect height="54" width="210.015625" y="-27" x="-105.0078125" style="" class="basic label-container"></rect><g transform="translate(-75.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="150.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Response Generation</p></span></div></foreignObject></g></g></g></g></g></svg>
</body>
</html>