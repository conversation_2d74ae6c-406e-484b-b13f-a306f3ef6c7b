package com.example.mcp_microservice_chatboot_ai.controller;

import com.example.mcp_microservice_chatboot_ai.model.dto.ConversationRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ConversationResponse;
import com.example.mcp_microservice_chatboot_ai.service.ChatMcpApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Controller for handling conversation-related endpoints.
 */

/**
 * Role: REST controller for conversation-related endpoints
Purpose:
Exposes endpoints for creating and retrieving conversations
Delegates conversation management to ChatMcpApiService
Returns conversation data to clients
 */
@RestController
@RequestMapping("/conversations")
public class ConversationController {

    private final ChatMcpApiService chatMcpApiService;

    @Autowired
    public ConversationController(ChatMcpApiService chatMcpApiService) {
        this.chatMcpApiService = chatMcpApiService;
    }

    /**
     * Endpoint for creating a new conversation.
     * 
     * @param conversationRequest The conversation request
     * @return A Mono containing the conversation response
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ConversationResponse> createConversation(@RequestBody ConversationRequest conversationRequest) {
        return chatMcpApiService.createConversation(conversationRequest);
    }

    /**
     * Endpoint for getting all conversations for a user.
     * 
     * @param username The username
     * @return A Flux containing the conversation responses
     */
    @GetMapping(value = "/user/{username}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Flux<ConversationResponse> getUserConversations(@PathVariable String username) {
        return chatMcpApiService.getUserConversations(username);
    }
}
