package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * BatchConfigurationItem entity representing batch configuration items
 */
@Entity
@Table(name = "batch_configuration_item")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchConfigurationItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", length = 100)
    private String name;

    @Column(name = "label")
    private String label;

    @Column(name = "type", length = 50)
    private String type;

    @Column(name = "required")
    private Boolean required;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "batch_admin_id")
    private BatchAdmin batchAdmin;
}
