import { TypeSoldeEchange } from 'src/app/winpharm/enums/echange/TypeSoldeEchange.enum';
import { NatureOperationSoldeEchange } from 'src/app/winpharm/enums/echange/NatureOperationSoldeEchange.enum';

// import { Moment } from 'moment';

import { Confrere } from 'src/app/winpharm/models/tiers/confrere/confrere.model';
import { DetailSoldeEchangeFt } from './detailSoldeEchangeFt.model';
import { DocumentVentilable } from 'src/app/winpharm/models/common/documentVentilable.model';
import { EnteteEchange } from './enteteEchange.model';
import { ModePaiement } from '../../enums/common/ModePaiement.enum';


export class EnteteSoldeEchange {
    audited?: boolean;
    confrere?: Confrere;
    dateSolde?: any;
    detailSoldeEchangeFts?: DetailSoldeEchangeFt[];
    document?: DocumentVentilable;
    enteteEchanges?: EnteteEchange[];
    id?: number;
    mntSolde?: number;
    natureOperation?: NatureOperationSoldeEchange;
    numSolde?: string;
    referenceReglement?: string;
    sousStatut?: TypeSoldeEchange;
    userModifiable?: boolean;

    dateDebut?: any
    dateFin?: any
    modePaiement?: ModePaiement
}

