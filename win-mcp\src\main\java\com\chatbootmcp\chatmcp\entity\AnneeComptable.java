package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * AnneeComptable entity representing accounting years
 */
@Entity
@Table(name = "annee_comptable")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnneeComptable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "annee")
    private Integer annee;

    @Column(name = "date_creation")
    private LocalDateTime dateCreation;

    @Column(name = "date_ouverture")
    private LocalDateTime dateOuverture;

    @Column(name = "statut", length = 2)
    private String statut;

    @Column(name = "tenant_id")
    private Integer tenantId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_ouverture_id")
    private Operateur userOuverture;

    @Column(name = "societe_tenant_id")
    private Long societeTenantId;
}
