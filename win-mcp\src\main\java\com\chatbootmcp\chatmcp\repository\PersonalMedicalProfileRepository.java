package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.PersonalMedicalProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.List;

@Repository
public interface PersonalMedicalProfileRepository extends JpaRepository<PersonalMedicalProfile, Long> {
    
    /**
     * Find medical profile by username
     */
    Optional<PersonalMedicalProfile> findByUsername(String username);
    
    /**
     * Check if medical profile exists for username
     */
    boolean existsByUsername(String username);
    
    /**
     * Find all profiles with specific blood type
     */
    List<PersonalMedicalProfile> findByBloodType(String bloodType);
    
    /**
     * Find profiles by doctor name
     */
    List<PersonalMedicalProfile> findByDoctorName(String doctorName);
    
    /**
     * Find profiles with allergies containing specific text
     */
    @Query("SELECT p FROM PersonalMedicalProfile p WHERE p.allergies LIKE %:allergyText%")
    List<PersonalMedicalProfile> findByAllergiesContaining(@Param("allergyText") String allergyText);
    
    /**
     * Find profiles with chronic conditions containing specific text
     */
    @Query("SELECT p FROM PersonalMedicalProfile p WHERE p.chronicConditions LIKE %:conditionText%")
    List<PersonalMedicalProfile> findByChronicConditionsContaining(@Param("conditionText") String conditionText);
    
    /**
     * Count total medical profiles
     */
    @Query("SELECT COUNT(p) FROM PersonalMedicalProfile p")
    long countTotalProfiles();
    
    /**
     * Find profiles with missing essential information
     */
    @Query("SELECT p FROM PersonalMedicalProfile p WHERE p.bloodType IS NULL OR p.emergencyContact IS NULL OR p.doctorName IS NULL")
    List<PersonalMedicalProfile> findIncompleteProfiles();
}
