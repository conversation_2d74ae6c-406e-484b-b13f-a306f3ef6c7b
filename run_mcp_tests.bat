@echo off
echo 🧪 MCP System Test Runner
echo ========================
echo.
echo This script will run comprehensive tests on the MCP system.
echo Make sure both services are running:
echo   - Chat-MCP Backend (port 8080)
echo   - MCP Microservice (port 8081)
echo.
pause
echo.
echo 🚀 Starting tests...
powershell -ExecutionPolicy Bypass -File "test_all_mcp_questions.ps1"
echo.
echo ✅ Tests completed!
pause
