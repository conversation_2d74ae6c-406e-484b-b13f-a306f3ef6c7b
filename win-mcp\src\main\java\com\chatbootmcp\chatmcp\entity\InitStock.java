package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * InitStock entity representing stock initialization
 */
@Entity
@Table(name = "init_stock")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InitStock {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "date_creation")
    private LocalDateTime dateCreation;

    @Column(name = "date_annulation")
    private LocalDateTime dateAnnulation;

    @Column(name = "date_validation")
    private LocalDateTime dateValidation;

    @Column(name = "mnt_prix_achat_ttc", precision = 15, scale = 2)
    private BigDecimal mntPrixAchatTtc;

    @Column(name = "mnt_prix_vente_ttc", precision = 15, scale = 2)
    private BigDecimal mntPrixVenteTtc;

    @Column(name = "nbr_lignes")
    private Integer nbrLignes;

    @Column(name = "total_qte", precision = 15, scale = 2)
    private BigDecimal totalQte;

    @Column(name = "statut", length = 50)
    private String statut;

    @Column(name = "is_loaded_from_file")
    private Boolean isLoadedFromFile = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "depot_id")
    private Depot depot;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "operateur_id")
    private Operateur operateur;
}
