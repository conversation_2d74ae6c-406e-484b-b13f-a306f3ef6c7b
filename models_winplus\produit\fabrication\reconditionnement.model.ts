import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';

// import { Moment } from 'moment';

import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { Produit } from 'src/app/winpharm/models/produit/base/produit.model';


export class Reconditionnement {
    audited?: boolean;
    dateCreation?: any;
    datePeremption?: string;
    id?: number;
    intrantDepot?: Depot;
    intrantProduit?: Produit;
    numLot?: string;
    intrantPrixVenteTtc?: number;
    qteIntrant?: number;
    qteSortie?: number;
    sortieDepot?: Depot;
    sortieProduit?: Produit;
    statut?: Statut;
    userModifiable?: boolean;
    sortiePrixVenteTtc?: number;

    //transient field
    qteParUnite?: number;
}

