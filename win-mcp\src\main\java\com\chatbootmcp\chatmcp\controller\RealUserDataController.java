package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.entity.UserProfile;
import com.chatbootmcp.chatmcp.entity.Transaction;
import com.chatbootmcp.chatmcp.entity.Invoice;
import com.chatbootmcp.chatmcp.entity.Order;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import com.chatbootmcp.chatmcp.repository.UserProfileRepository;
import com.chatbootmcp.chatmcp.repository.TransactionRepository;
import com.chatbootmcp.chatmcp.repository.InvoiceRepository;
import com.chatbootmcp.chatmcp.repository.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller for real user data without mock data
 */
@RestController
@RequestMapping("/real-data")
public class RealUserDataController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private OrderRepository orderRepository;

    /**
     * Get real user profile data
     */
    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getUserProfile(
            @AuthenticationPrincipal UserDetails userDetails) {

        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        Map<String, Object> profileData = new HashMap<>();

        // Basic user information
        profileData.put("username", user.getUsername());
        profileData.put("email", user.getEmail());
        profileData.put("fullName", user.getFullName());
        profileData.put("roles", user.getRoles());
        profileData.put("createdAt", user.getCreatedAt());
        profileData.put("updatedAt", user.getUpdatedAt());

        // User profile information
        UserProfile profile = userProfileRepository.findByUser(user).orElse(null);
        if (profile != null) {
            profileData.put("phoneNumber", profile.getPhoneNumber());
            profileData.put("address", profile.getAddress());
            profileData.put("city", profile.getCity());
            profileData.put("postalCode", profile.getPostalCode());
            profileData.put("country", profile.getCountry());
            profileData.put("dateOfBirth", profile.getDateOfBirth());
            profileData.put("jobTitle", profile.getJobTitle());
            profileData.put("company", profile.getCompany());
            profileData.put("department", profile.getDepartment());
            profileData.put("employeeId", profile.getEmployeeId());
            profileData.put("accountType", profile.getAccountType());
            profileData.put("subscriptionStatus", profile.getSubscriptionStatus());
            profileData.put("subscriptionStartDate", profile.getSubscriptionStartDate());
            profileData.put("subscriptionEndDate", profile.getSubscriptionEndDate());
            profileData.put("subscriptionType", profile.getSubscriptionType());
            profileData.put("subscriptionPrice", profile.getSubscriptionPrice());
            profileData.put("currentBalance", profile.getCurrentBalance());
            profileData.put("creditLimit", profile.getCreditLimit());
            profileData.put("paymentMethod", profile.getPaymentMethod());
            profileData.put("cardLastFour", profile.getCardLastFour());
            profileData.put("cardExpiryDate", profile.getCardExpiryDate());
            profileData.put("nextPaymentDue", profile.getNextPaymentDue());
            profileData.put("languagePreference", profile.getLanguagePreference());
            profileData.put("timezone", profile.getTimezone());
            profileData.put("notificationPreferences", profile.getNotificationPreferences());
            profileData.put("profileCompletionPercentage", profile.getProfileCompletionPercentage());
            profileData.put("lastProfileUpdate", profile.getLastProfileUpdate());
        }

        return ResponseEntity.ok(profileData);
    }

    /**
     * Get real user transactions
     */
    @GetMapping("/transactions")
    public ResponseEntity<List<Map<String, Object>>> getUserTransactions(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(defaultValue = "10") int limit) {

        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        List<Transaction> transactions = transactionRepository.findByUserOrderByCreatedAtDesc(user)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());

        List<Map<String, Object>> transactionData = transactions.stream()
                .map(transaction -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("id", transaction.getId());
                    data.put("transactionId", transaction.getTransactionId());
                    data.put("amount", transaction.getAmount());
                    data.put("transactionType", transaction.getTransactionType());
                    data.put("description", transaction.getDescription());
                    data.put("category", transaction.getCategory());
                    data.put("merchantName", transaction.getMerchantName());
                    data.put("location", transaction.getLocation());
                    data.put("paymentMethod", transaction.getPaymentMethod());
                    data.put("status", transaction.getStatus());
                    data.put("balanceAfter", transaction.getBalanceAfter());
                    data.put("transactionFee", transaction.getTransactionFee());
                    data.put("currency", transaction.getCurrency());
                    data.put("referenceNumber", transaction.getReferenceNumber());
                    data.put("notes", transaction.getNotes());
                    data.put("createdAt", transaction.getCreatedAt());
                    data.put("processedAt", transaction.getProcessedAt());
                    return data;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(transactionData);
    }

    /**
     * Get real user invoices
     */
    @GetMapping("/invoices")
    public ResponseEntity<List<Map<String, Object>>> getUserInvoices(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(defaultValue = "10") int limit) {

        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        List<Invoice> invoices = invoiceRepository.findByUserOrderByInvoiceDateDesc(user)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());

        List<Map<String, Object>> invoiceData = invoices.stream()
                .map(invoice -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("id", invoice.getId());
                    data.put("invoiceNumber", invoice.getInvoiceNumber());
                    data.put("invoiceDate", invoice.getInvoiceDate());
                    data.put("dueDate", invoice.getDueDate());
                    data.put("totalAmount", invoice.getTotalAmount());
                    data.put("subtotal", invoice.getSubtotal());
                    data.put("taxAmount", invoice.getTaxAmount());
                    data.put("discountAmount", invoice.getDiscountAmount());
                    data.put("status", invoice.getStatus());
                    data.put("paymentStatus", invoice.getPaymentStatus());
                    data.put("paymentMethod", invoice.getPaymentMethod());
                    data.put("paymentDate", invoice.getPaymentDate());
                    data.put("description", invoice.getDescription());
                    data.put("serviceType", invoice.getServiceType());
                    data.put("currency", invoice.getCurrency());
                    data.put("notes", invoice.getNotes());
                    data.put("pdfUrl", invoice.getPdfUrl());
                    data.put("billingPeriodStart", invoice.getBillingPeriodStart());
                    data.put("billingPeriodEnd", invoice.getBillingPeriodEnd());
                    data.put("createdAt", invoice.getCreatedAt());
                    return data;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(invoiceData);
    }

    /**
     * Get real user orders
     */
    @GetMapping("/orders")
    public ResponseEntity<List<Map<String, Object>>> getUserOrders(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(defaultValue = "10") int limit) {

        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        List<Order> orders = orderRepository.findByUserOrderByOrderDateDesc(user)
                .stream()
                .limit(limit)
                .collect(Collectors.toList());

        List<Map<String, Object>> orderData = orders.stream()
                .map(order -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("id", order.getId());
                    data.put("orderNumber", order.getOrderNumber());
                    data.put("orderDate", order.getOrderDate());
                    data.put("totalAmount", order.getTotalAmount());
                    data.put("status", order.getStatus());
                    data.put("paymentStatus", order.getPaymentStatus());
                    data.put("paymentMethod", order.getPaymentMethod());
                    data.put("itemsDescription", order.getItemsDescription());
                    data.put("itemsCount", order.getItemsCount());
                    data.put("shippingAddress", order.getShippingAddress());
                    data.put("billingAddress", order.getBillingAddress());
                    data.put("shippingCost", order.getShippingCost());
                    data.put("taxAmount", order.getTaxAmount());
                    data.put("discountAmount", order.getDiscountAmount());
                    data.put("trackingNumber", order.getTrackingNumber());
                    data.put("carrier", order.getCarrier());
                    data.put("estimatedDelivery", order.getEstimatedDelivery());
                    data.put("actualDelivery", order.getActualDelivery());
                    data.put("orderSource", order.getOrderSource());
                    data.put("specialInstructions", order.getSpecialInstructions());
                    data.put("notes", order.getNotes());
                    data.put("currency", order.getCurrency());
                    data.put("createdAt", order.getCreatedAt());
                    return data;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(orderData);
    }

    /**
     * Get user financial summary
     */
    @GetMapping("/financial-summary")
    public ResponseEntity<Map<String, Object>> getFinancialSummary(
            @AuthenticationPrincipal UserDetails userDetails) {

        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        Map<String, Object> financialData = new HashMap<>();

        // Get totals from repositories
        Double totalCredits = transactionRepository.getTotalCreditsForUser(user);
        Double totalDebits = transactionRepository.getTotalDebitsForUser(user);
        Double totalPaidInvoices = invoiceRepository.getTotalPaidAmountForUser(user);
        Double totalUnpaidInvoices = invoiceRepository.getTotalUnpaidAmountForUser(user);
        Double totalOrderAmount = orderRepository.getTotalOrderAmountForUser(user);
        Double averageOrderValue = orderRepository.getAverageOrderValueForUser(user);

        financialData.put("totalCredits", totalCredits != null ? totalCredits : 0.0);
        financialData.put("totalDebits", totalDebits != null ? totalDebits : 0.0);
        financialData.put("netBalance", (totalCredits != null ? totalCredits : 0.0) - (totalDebits != null ? totalDebits : 0.0));
        financialData.put("totalPaidInvoices", totalPaidInvoices != null ? totalPaidInvoices : 0.0);
        financialData.put("totalUnpaidInvoices", totalUnpaidInvoices != null ? totalUnpaidInvoices : 0.0);
        financialData.put("totalOrderAmount", totalOrderAmount != null ? totalOrderAmount : 0.0);
        financialData.put("averageOrderValue", averageOrderValue != null ? averageOrderValue : 0.0);

        return ResponseEntity.ok(financialData);
    }
}
