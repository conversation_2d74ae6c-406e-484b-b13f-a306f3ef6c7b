{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abder<PERSON>mane_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { AppComponent } from './app.component';\ndescribe('AppComponent', () => {\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [RouterTestingModule],\n      declarations: [AppComponent]\n    }).compileComponents();\n  }));\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n  it(`should have as title 'angular-openai-chat-2'`, () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app.title).toEqual('angular-openai-chat-2');\n  });\n  it('should render title', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    fixture.detectChanges();\n    const compiled = fixture.nativeElement;\n    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, angular-openai-chat-2');\n  });\n});", "map": {"version": 3, "names": ["TestBed", "RouterTestingModule", "AppComponent", "describe", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "declarations", "compileComponents", "it", "fixture", "createComponent", "app", "componentInstance", "expect", "toBeTruthy", "title", "toEqual", "detectChanges", "compiled", "nativeElement", "querySelector", "textContent", "toContain"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\r\nimport { RouterTestingModule } from '@angular/router/testing';\r\nimport { AppComponent } from './app.component';\r\n\r\ndescribe('AppComponent', () => {\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [\r\n        RouterTestingModule\r\n      ],\r\n      declarations: [\r\n        AppComponent\r\n      ],\r\n    }).compileComponents();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    const app = fixture.componentInstance;\r\n    expect(app).toBeTruthy();\r\n  });\r\n\r\n  it(`should have as title 'angular-openai-chat-2'`, () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    const app = fixture.componentInstance;\r\n    expect(app.title).toEqual('angular-openai-chat-2');\r\n  });\r\n\r\n  it('should render title', () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    fixture.detectChanges();\r\n    const compiled = fixture.nativeElement as HTMLElement;\r\n    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, angular-openai-chat-2');\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5BC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAML,OAAO,CAACM,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPN,mBAAmB,CACpB;MACDO,YAAY,EAAE,CACZN,YAAY;KAEf,CAAC,CAACO,iBAAiB,EAAE;EACxB,CAAC,EAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGX,OAAO,CAACY,eAAe,CAACV,YAAY,CAAC;IACrD,MAAMW,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAAC,CAACG,UAAU,EAAE;EAC1B,CAAC,CAAC;EAEFN,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtD,MAAMC,OAAO,GAAGX,OAAO,CAACY,eAAe,CAACV,YAAY,CAAC;IACrD,MAAMW,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAACI,KAAK,CAAC,CAACC,OAAO,CAAC,uBAAuB,CAAC;EACpD,CAAC,CAAC;EAEFR,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7B,MAAMC,OAAO,GAAGX,OAAO,CAACY,eAAe,CAACV,YAAY,CAAC;IACrDS,OAAO,CAACQ,aAAa,EAAE;IACvB,MAAMC,QAAQ,GAAGT,OAAO,CAACU,aAA4B;IACrDN,MAAM,CAACK,QAAQ,CAACE,aAAa,CAAC,IAAI,CAAC,EAAEC,WAAW,CAAC,CAACC,SAAS,CAAC,8BAA8B,CAAC;EAC7F,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}