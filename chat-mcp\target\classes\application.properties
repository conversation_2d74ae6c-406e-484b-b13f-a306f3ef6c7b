spring.application.name=chat-mcp

# Server configuration
server.port=8080
server.servlet.context-path=/api

# Database configuration
spring.datasource.url=jdbc:h2:mem:chatdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# JPA configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# JWT configuration
jwt.secret=yourSecretKeyHereMakeItLongAndSecureForProductionUse
jwt.expiration=86400000

# Logging
logging.level.com.chatbootmcp.chatmcp=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
