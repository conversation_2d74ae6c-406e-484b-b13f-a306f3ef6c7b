import { StatutInventaire } from 'src/app/winpharm/enums/inventaire/StatutInventaire.enum';
import { Alphabet } from 'src/app/winpharm/enums/inventaire/Alphabet.enum';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Rayon } from 'src/app/winpharm/models/produit/base/rayon.model';


export class DetailInventaireListe {
    audited?: boolean;
    categorie?: CategorieProduit;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codeRayon?: string;
    depot?: Depot;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    laboratoire?: Fournisseur;
    plageAlphaDeb?: Alphabet;
    plageAlphaFin?: Alphabet;
    rayon?: Rayon;
    statutListe?: StatutInventaire;
    libelle?: String;
    userModifiable?: boolean;
}
