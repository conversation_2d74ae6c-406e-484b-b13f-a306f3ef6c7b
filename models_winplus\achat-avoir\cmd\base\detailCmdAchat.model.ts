import { TypeAssistantAchat } from 'src/app/winpharm/enums/achat-avoir/TypeAssistantAchat.enum';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Produit } from 'src/app/winpharm/models/produit/base/produit.model';
import { Taxe } from 'src/app/winpharm/models/common/taxe.model';


export class DetailCmdAchat {
    audited?: boolean;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codePrd?: string;
    ctgr?: CategorieProduit;
    dsgnPrd?: string;
    fournisseur?: Fournisseur;
    frm?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    labo?: Fournisseur;
    mntLigneAchatStd?: number;
    mntLigneBrutHt?: number;
    mntLigneBrutTtc?: number;
    mntLigneNetHt?: number;
    mntLigneNetTtc?: number;
    mntLigneRemiseHt?: number;
    mntLigneRemiseTtc?: number;
    mntLigneTva?: number;
    mntLigneVenteStd?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTva?: number;
    numCmd?: number;
    numLigne?: number;
    pbrH?: number;
    pbrP?: number;
    prixAchatStd?: number;
    prixBrutHt?: number;
    prixBrutTtc?: number;
    prixHosp?: number;
    prixNetHt?: number;
    prixNetTtc?: number;
    prixVenteStd?: number;
    prixVenteTtc?: number;
    produit?: Produit;
    qtCmd?: number;
    qtPropose?: number;
    qtCmdOriginale?: number;
    stockId?: string;
    tauxMarge?: number;
    tauxRemise?: number;
    tauxTva?: number;
    tva?: Taxe;
    typeAssistant?: TypeAssistantAchat;
    userModifiable?: boolean;
}
