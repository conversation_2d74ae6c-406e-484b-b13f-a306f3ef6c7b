package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * TransfertStock entity representing stock transfers between depots
 */
@Entity
@Table(name = "transfert_stock")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransfertStock {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "num_transfert")
    private Integer numTransfert;

    @Column(name = "date_creation")
    private LocalDateTime dateCreation;

    @Column(name = "date_annulation")
    private LocalDateTime dateAnnulation;

    @Column(name = "qte_totale", precision = 15, scale = 2)
    private BigDecimal qteTotale;

    @Column(name = "statut", length = 50)
    private String statut;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "zone_source_id")
    private Depot zoneSource;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "zone_destination_id")
    private Depot zoneDestination;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_creation_id")
    private Operateur userCreation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_annulation_id")
    private Operateur userAnnulation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_validation_id")
    private Operateur userValidation;
}
