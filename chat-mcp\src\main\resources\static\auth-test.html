<!DOCTYPE html>
<html>
<head>
    <title>Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        input {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Chat MCP Authentication Test</h1>
    
    <div class="container">
        <h2>Health Check</h2>
        <button id="healthCheck">Check Health</button>
        <pre id="healthResult"></pre>
    </div>
    
    <div class="container">
        <h2>Regular Login</h2>
        <div>
            <label for="username">Username:</label>
            <input type="text" id="username" value="user1">
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" value="password">
        </div>
        <button id="loginBtn">Login</button>
        <pre id="loginResult"></pre>
    </div>
    
    <div class="container">
        <h2>Test Login (Username Only)</h2>
        <div>
            <label for="testUsername">Username:</label>
            <input type="text" id="testUsername" value="user1">
        </div>
        <button id="testLoginBtn">Test Login</button>
        <pre id="testLoginResult"></pre>
    </div>
    
    <div class="container">
        <h2>Test Protected Endpoint</h2>
        <div>
            <label for="token">JWT Token:</label>
            <input type="text" id="token" placeholder="Paste your JWT token here">
        </div>
        <button id="testProtectedBtn">Test Protected Endpoint</button>
        <pre id="protectedResult"></pre>
    </div>

    <script>
        // Health Check
        document.getElementById('healthCheck').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/auth/health');
                const data = await response.json();
                document.getElementById('healthResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('healthResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Regular Login
        document.getElementById('loginBtn').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('loginResult').textContent = JSON.stringify(data, null, 2);
                    document.getElementById('token').value = data.token;
                } else {
                    const error = await response.json();
                    document.getElementById('loginResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
                }
            } catch (error) {
                document.getElementById('loginResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Test Login
        document.getElementById('testLoginBtn').addEventListener('click', async () => {
            const username = document.getElementById('testUsername').value;
            
            try {
                const response = await fetch('/api/auth/test-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('testLoginResult').textContent = JSON.stringify(data, null, 2);
                    document.getElementById('token').value = data.token;
                } else {
                    const error = await response.json();
                    document.getElementById('testLoginResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
                }
            } catch (error) {
                document.getElementById('testLoginResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Test Protected Endpoint
        document.getElementById('testProtectedBtn').addEventListener('click', async () => {
            const token = document.getElementById('token').value;
            
            if (!token) {
                document.getElementById('protectedResult').textContent = 'Error: No token provided';
                return;
            }
            
            try {
                const response = await fetch('/api/conversations', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('protectedResult').textContent = JSON.stringify(data, null, 2);
                } else {
                    const error = await response.text();
                    document.getElementById('protectedResult').textContent = 'Error: ' + error;
                }
            } catch (error) {
                document.getElementById('protectedResult').textContent = 'Error: ' + error.message;
            }
        });
    </script>
</body>
</html>
