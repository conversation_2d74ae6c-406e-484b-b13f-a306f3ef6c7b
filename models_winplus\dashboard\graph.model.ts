import moment from "moment";
import { Serie } from "./series.model";
import {
    ApexAxisChartSeries,
    ApexChart,
    ChartComponent,
    ApexDataLabels,
    ApexPlotOptions,
    ApexYAxis,
    ApexTitleSubtitle,
    ApexXAxis,
    ApexFill,
    ApexGrid,
    ApexStroke,
    ApexAnnotations,
    ApexTooltip,
    ApexMarkers
} from "ng-apexcharts";
export class LineGraph {

    lineG: Partial<LineChartOptions>
    public chartOptions: Partial<LineChartOptions>;
    constructor() {

    }

    initGraph() {
        this.lineG = {
            chart: {
                height: 364,
                type: 'line',
                dropShadow: {
                    enabled: true,
                    opacity: 0.2,
                    blur: 7,
                    left: -7,
                    top: 7
                },

            },
            series: [],
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 4
            },
            xaxis: {
                type: "category",
                tickAmount: 20,


            }
            ,
            yaxis: {
                labels: {
                    formatter: (val: any) => {
                        return val + "dhs";
                    },
                    offsetX: -15
                }
            },
            tooltip: {
                x: {
                    show: true,
                    formatter(val, { seriesIndex, dataPointIndex, w }) {
                        const b = w?.config?.["series"]?.[seriesIndex ?? 0]?.["data"]?.[dataPointIndex]?.["x"]
                        return b ?? ""
                    },
                },
            },
            noData: {
                text: 'No data available', // Default message
                align: 'center',
                verticalAlign: 'middle',
                style: {
                    color: '#666666',
                    fontSize: '14px',
                    fontFamily: 'Arial'
                }
            }

        };


        this.lineG['noData']
        return this

    }

    setData(data) {
        this.lineG["series"] = data
        return this;
    }

    typeXaxis(type) {
        this.lineG["xaxis"]["type"] = type
        return this;
    }
    xasisCategory(categories) {

        this.lineG["xaxis"]["categories"] = categories


        return this
    }

    // stroke(stroke: { curve?: string, width?: number }) {
    //     if (stroke?.curve) this.lineG['stroke']["curve"] = stroke?.curve
    //     if (stroke?.width) this.lineG['stroke']["width"] = stroke?.width
    //     return this;
    // }

    labelYaxis(unit) {
        this.lineG["yaxis"]["labels"] = {
            formatter: (val: any) => {
                return val + unit;
            },
        }
        return this;
    }

    setGraphTitle(title) {
        this.lineG.title = {
            text: title,
            align: 'left'
        }
        return this
    }
    setYaxisTitle(title) {
        this.lineG["yaxis"] = {
            title: {
                text: title,
                style: {
                    fontSize: '12px'
                }
            }

        }
        return this;

    }

    get graph() {
        return this?.lineG
    }


    setMinMax() {

    }

    /**
 * Customize the noData message and styling
 * @param options - Configuration for the noData state
 */
    setNoDataOptions(options: {
        text?: string;
        align?: 'left' | 'center' | 'right';
        verticalAlign?: 'top' | 'middle' | 'bottom';
        style?: {
            color?: string;
            fontSize?: string;
            fontFamily?: string;
        };
    }) {
        this.lineG.noData = {
            ...this.lineG.noData,
            ...options
        };
        return this;
    }


}

// 
export class PieGraph {

    pieGraph: any
    constructor() {

    }

    initGraph() {
        this.pieGraph = {
            chart: {
                height: 240,
                type: 'donut',
            },
            series: [],
            labels: [],
            dataLabels: {
                enabled: false
            },
            stroke: {
                colors: ['transparent']
            },

            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };
        return this

    }

    setData(data: any[]) {
        this.pieGraph["series"] = data
        return this;

    }
    setLabels(labels: any[]) {
        this.pieGraph["labels"] = labels
        return this;
    }

    get graph() {
        return this?.pieGraph
    }
}


export class BarGraph {

    barGraph: Partial<BarChartOptions>;
    //public chartOptions: Partial<BarChartOptions>;

    constructor() { }

    initGraph() {
        this.barGraph = {
            chart: {
                height: 500,
                width: '100%',
                type: 'bar',
                stacked: false
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '50%',
                    dataLabels: {
                        position: "top" // top, center, bottom
                    }
                },
            },
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val + "%";
                },
                offsetY: -20,
                style: {
                    fontSize: "15px",
                    colors: ["#304758"]
                }
            },
            // stroke: {
            //     show: true,
            //     width: 2,
            //     colors: ['transparent']
            // },
            series: [],
            // zoom: {
            //     enabled: false
            // },

            // colors: ['#65d20a', '#e3eaef'],
            xaxis: {
                categories: [],
                axisBorder: {
                    show: false
                },
            },
            yaxis: {
                labels: {
                    show: false,
                    formatter: (val: any) => {
                        return val;
                    },
                    offsetX: -15
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },

            },
            fill: {
                opacity: 1
            },
            // tooltip: {
            //     y: {
            //         formatter: (val: any) => {
            //             return val;
            //         }
            //     },
            // },
        };
        return this
    }

    setData(data: any[]) {
        /// serie = [ { data : dataValue ,name:"nameSerie"}]

        this.barGraph["series"] = data
        return this;

    }
    typeXaxis(type) {
        this.barGraph["xaxis"]["type"] = type
        return this;
    }
    xasisCategory(categories) {
        this.barGraph["xaxis"]["categories"] = categories
        return this
    }

    labelYaxis(unit) {
        this.barGraph["yaxis"]["labels"] = {
            formatter: (val: any) => {
                return val + unit;
            },
        }
        this.barGraph["tooltip"] = {
            y: {
                formatter: (val: any) => {
                    return + val + unit;
                }
            },
        }
        return this;
    }

    /// set bar 

    setBarPlotOption(plotOptions: ApexPlotOptions) {
        this.barGraph["plotOptions"] = plotOptions
        return this
    }

    setColors(colors: any[]) {
        this.barGraph["colors"] = colors
    }

    setDataLabels(dataLabels: ApexDataLabels) {
        this.barGraph["dataLabels"] = {
            enabled: dataLabels.enabled,
            formatter: dataLabels.formatter,
            offsetY: dataLabels?.offsetY,
            style: {
                fontSize: dataLabels?.style?.fontSize + "px",
                colors: dataLabels?.style?.colors
            }
        }

        return this
    }

    setChartWidthHeight(chart: { height?: string, width?: string }) {
        this.barGraph["chart"]["height"] = chart?.height
        this.barGraph["chart"]["width"] = chart?.width

        return this
    }


    get graph() {
        return this.barGraph
    }

}




export type BarChartOptions = {
    series: ApexAxisChartSeries;
    chart: ApexChart;
    dataLabels: ApexDataLabels;
    plotOptions: ApexPlotOptions;
    yaxis: ApexYAxis;
    xaxis: ApexXAxis;
    fill: ApexFill;
    title: ApexTitleSubtitle;
};

/// line chart 
export type LineChartOptions = {
    series: ApexAxisChartSeries;
    chart: ApexChart;
    xaxis: ApexXAxis;
    yaxis: ApexYAxis,
    dataLabels: ApexDataLabels;
    grid: ApexGrid;
    stroke: ApexStroke;
    title: ApexTitleSubtitle;
    tooltip: ApexTooltip;
    noData?: any


};