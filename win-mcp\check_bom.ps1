# PowerShell script to check for <PERSON>OM in Java files

$javaFiles = Get-ChildItem -Path "src" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding Byte
    
    # Check for UTF-8 BOM (EF BB BF)
    if ($content.Length -ge 3 -and $content[0] -eq 0xEF -and $content[1] -eq 0xBB -and $content[2] -eq 0xBF) {
        Write-Host "BOM found in: $($file.FullName)"
        
        # Create a new file without BOM
        $newContent = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $newContent = $newContent.TrimStart([char]0xFEFF)
        $newContent | Out-File -FilePath "$($file.FullName).new" -Encoding UTF8
        
        # Replace the original file
        Remove-Item -Path $file.FullName
        Move-Item -Path "$($file.FullName).new" -Destination $file.FullName
        
        Write-Host "BOM removed from: $($file.FullName)"
    }
}

Write-Host "BOM check completed!"
