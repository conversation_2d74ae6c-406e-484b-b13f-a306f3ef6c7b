import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { TypeBla } from 'src/app/winpharm/enums/achat-avoir/TypeBla.enum';

// import { Moment } from 'moment';

import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { Devise } from 'src/app/winpharm/models/common/devise.model';
import { EnteteCmdAchat } from 'src/app/winpharm/models/achat-avoir/cmd/base/enteteCmdAchat.model';
import { EnteteDemandeAvoir } from 'src/app/winpharm/models/achat-avoir/avoir/dmdavoir/enteteDemandeAvoir.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { DetailOcrBlAchat } from './detailOcrBlAchat.model';
import { SourceBlEnum } from 'src/app/winpharm/enums/achat-avoir/SourceBl.enum';


export class EnteteOcrBlAchat {
    audited?: boolean;
    axeAnalytique?: string;
    codeFrnsr?: string;
    cumulBla?: number;
    dateBla?: any;
    dateCreation?: any;
    dateFacture?: any;
    depot?: Depot;
    detailBlAchats?: DetailOcrBlAchat[];
    devise?: Devise;
    enteteCmdAchat?: EnteteCmdAchat;
    enteteDemandeAvoirs?: EnteteDemandeAvoir[];
    flagAccepteEcart?: boolean;
    fournisseur?: Fournisseur;
    id?: number;
    libelleFrnsr?: string;
    mntAchatStd?: number;
    mntBrutHt?: number;
    mntBrutTtc?: number;
    mntNetEffectifHt?: number;
    mntNetEffectifTtc?: number;
    mntNetEffectifTva?: number;
    mntNetHt?: number;
    mntNetTtc?: number;
    mntRemiseEffectifHt?: number;
    mntRemiseEffectifTtc?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTva?: number;
    mntVenteStd?: number;
    nbrLigne?: number;
    numBla?: number;
    numCmd?: number;
    numFacture?: number;
    operateur?: Operateur;
    statut?: Statut;
    totalQtLivre?: number;
    typeBla?: TypeBla;
    userModifiable?: boolean;
    tauxRemise?: number;
    dateExport?: string;
    random_id?:string;
    // transient
    diff?: number;
    sourceBl : SourceBlEnum;
    montantBla?: number;

}

