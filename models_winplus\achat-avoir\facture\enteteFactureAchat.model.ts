import { TypeStatutReglement } from 'src/app/winpharm/enums/achat-avoir/TypeStatutReglement.enum';
import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { NatureFactureAchat } from 'src/app/winpharm/enums/achat-avoir/NatureFactureAchat.enum';

// import { Moment } from 'moment';

import { DetailFactureAchatFt } from './detailFactureAchatFt.model';
import { DocumentVentilable } from 'src/app/winpharm/models/common/documentVentilable.model';
import { EnteteBlAchat } from 'src/app/winpharm/models/achat-avoir/reception/enteteBlAchat.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';


export class EnteteFactureAchat {
    audited?: boolean;
    codeFrnsr?: string;
    dateFacture?: any;
    detailFactureAchatFts?: DetailFactureAchatFt[];
    document?: DocumentVentilable;
    enteteBlAchats?: EnteteBlAchat[];
    flagAccepteEcart?: boolean;
    fournisseur?: Fournisseur;
    id?: number;
    mntAchatStd?: number;
    mntBrutHt?: number;
    mntBrutTtc?: number;
    mntNetEffectifHt?: number;
    mntNetEffectifTtc?: number;
    mntNetEffectifTva?: number;
    mntNetHt?: number;
    mntNetTtc?: number;
    mntRemiseEffectifHt?: number;
    mntRemiseEffectifTtc?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTva?: number;
    mntVenteStd?: number;
    nature?: NatureFactureAchat;
    nbrLigne?: number;
    numFacture?: number;
    statut?: Statut;
    statutReglement?: TypeStatutReglement;
    tauxRemise?: number;
    tauxRemiseEffectif?: number;
    totalQtLivre?: number;
    userModifiable?: boolean;
}

