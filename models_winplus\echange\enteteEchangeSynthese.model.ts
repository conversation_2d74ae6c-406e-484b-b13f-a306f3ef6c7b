import { SensEchange } from 'src/app/winpharm/enums/echange/SensEchange.enum';
import { TypePrixEchange } from 'src/app/winpharm/enums/echange/TypePrixEchange.enum';

import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';


export class EnteteEchangeSynthese { 
    audited?: boolean;
    ft?: FamilleTarifaire;
    mntEchange?: number;
    sensEchange?: SensEchange;
    typePrix?: TypePrixEchange;
    userModifiable?: boolean;
}

