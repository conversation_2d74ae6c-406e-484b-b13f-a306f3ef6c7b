package com.example.mcp_microservice_chatboot_ai.model.dto;

import com.example.mcp_microservice_chatboot_ai.model.ChatMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for chat message responses.
 */

/**
 * Role: Data Transfer Object for chat responses
    Purpose:
    Carries AI-generated response content and metadata
    Used to return AI responses to the client
    Includes static factory method to create from ChatMessage
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponse {
    
    /**
     * The unique identifier of the message.
     */
    private String id;
    
    /**
     * The ID of the conversation this message belongs to.
     */
    private String conversationId;
    
    /**
     * The role of the message sender (user or assistant).
     */
    private ChatMessage.MessageRole role;
    
    /**
     * The content of the message.
     */
    private String content;
    
    /**
     * The timestamp when the message was created.
     */
    private LocalDateTime timestamp;
    
    /**
     * Factory method to create a ChatResponse from a ChatMessage.
     * 
     * @param message The ChatMessage to convert
     * @return A new ChatResponse
     */
    public static ChatResponse fromChatMessage(ChatMessage message) {
        return ChatResponse.builder()
                .id(message.getId())
                .conversationId(message.getConversationId())
                .role(message.getRole())
                .content(message.getContent())
                .timestamp(message.getTimestamp())
                .build();
    }
}
