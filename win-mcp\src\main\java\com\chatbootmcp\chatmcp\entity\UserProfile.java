package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_profiles")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserProfile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    // Personal Information
    @Column(name = "full_name")
    private String fullName;
    
    @Column(name = "phone_number")
    private String phoneNumber;
    
    @Column(name = "address")
    private String address;
    
    @Column(name = "city")
    private String city;
    
    @Column(name = "postal_code")
    private String postalCode;
    
    @Column(name = "country")
    private String country;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    // Professional Information
    @Column(name = "job_title")
    private String jobTitle;
    
    @Column(name = "company")
    private String company;
    
    @Column(name = "department")
    private String department;
    
    @Column(name = "employee_id")
    private String employeeId;
    
    // Account Information
    @Column(name = "account_type")
    private String accountType;
    
    @Column(name = "subscription_status")
    private String subscriptionStatus;
    
    @Column(name = "subscription_start_date")
    private LocalDate subscriptionStartDate;
    
    @Column(name = "subscription_end_date")
    private LocalDate subscriptionEndDate;
    
    @Column(name = "subscription_type")
    private String subscriptionType;
    
    @Column(name = "subscription_price")
    private String subscriptionPrice;
    
    // Financial Information
    @Column(name = "current_balance")
    private Double currentBalance;
    
    @Column(name = "credit_limit")
    private Double creditLimit;
    
    @Column(name = "payment_method")
    private String paymentMethod;
    
    @Column(name = "card_last_four")
    private String cardLastFour;
    
    @Column(name = "card_expiry_date")
    private String cardExpiryDate;
    
    @Column(name = "next_payment_due")
    private LocalDate nextPaymentDue;
    
    // Preferences
    @Column(name = "language_preference")
    private String languagePreference;
    
    @Column(name = "timezone")
    private String timezone;
    
    @Column(name = "notification_preferences")
    private String notificationPreferences;
    
    // Metadata
    @Column(name = "profile_completion_percentage")
    private Integer profileCompletionPercentage;
    
    @Column(name = "last_profile_update")
    private LocalDateTime lastProfileUpdate;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
