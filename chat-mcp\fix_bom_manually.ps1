# PowerShell script to manually fix BOM in specific files

$filesToFix = @(
    "src\main\java\com\chatbootmcp\chatmcp\ChatMcpApplication.java",
    "src\main\java\com\chatbootmcp\chatmcp\config\CorsConfig.java",
    "src\main\java\com\chatbootmcp\chatmcp\config\DataInitializer.java",
    "src\main\java\com\chatbootmcp\chatmcp\config\SecurityConfig.java",
    "src\main\java\com\chatbootmcp\chatmcp\config\WebSocketConfig.java"
)

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        # Read the file content as bytes
        $bytes = [System.IO.File]::ReadAllBytes($file)
        
        # Check for UTF-8 BOM (EF BB BF)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            Write-Host "BOM found in $file, removing..."
            
            # Create a new array without the BOM
            $newBytes = $bytes[3..($bytes.Length-1)]
            
            # Write the bytes back to the file
            [System.IO.File]::WriteAllBytes($file, $newBytes)
            
            Write-Host "BOM removed from $file"
        } else {
            Write-Host "No BOM found in $file"
        }
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "Done!"
