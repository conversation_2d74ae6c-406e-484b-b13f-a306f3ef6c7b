package com.example.mcp_microservice_chatboot_ai.controller;

import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatResponse;
import com.example.mcp_microservice_chatboot_ai.service.AiChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * Controller for handling chat-related endpoints.
 */

/**
 * Role: REST controller for chat-related endpoints
    Purpose:
    Exposes endpoint for sending chat messages
    Delegates message processing to AiChatService
    Returns AI-generated responses to clients
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private final AiChatService aiChatService;

    @Autowired
    public ChatController(AiChatService aiChatService) {
        this.aiChatService = aiChatService;
    }

    /**
     * Endpoint for sending a chat message and getting a response.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ChatResponse> sendMessage(@RequestBody ChatRequest chatRequest) {
        return aiChatService.processChat(chatRequest);
    }
}
