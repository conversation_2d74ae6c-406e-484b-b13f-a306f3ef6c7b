import { TypeVente } from 'src/app/winpharm/enums/vente/TypeVente.enum';
import { TypeOperationVente } from 'src/app/winpharm/enums/vente/TypeOperationVente.enum';
import { TypeEncaissementVente } from 'src/app/winpharm/enums/vente/TypeEncaissementVente.enum';
import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { StatutLivraisonVente } from 'src/app/winpharm/enums/vente/StatutLivraisonVente.enum';
import { StatutEncaissementVente } from 'src/app/winpharm/enums/vente/StatutEncaissementVente.enum';
import { ModePaiement } from 'src/app/winpharm/enums/common/ModePaiement.enum';
import { TypeStatistiqueCriteria } from '../../enums/statistiques/TypeStatistiqueCriteria.enum';

// import { Moment } from 'moment';

import { Beneficiaire } from 'src/app/winpharm/models/tiers/client/beneficiaire.model';
import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { Client } from 'src/app/winpharm/models/tiers/client/client.model';
import { ConventionAssurance } from 'src/app/winpharm/models/assurance/conventionAssurance.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Medecin } from 'src/app/winpharm/models/common/medecin.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { OrganismeAssurance } from 'src/app/winpharm/models/tiers/organisme/organismeAssurance.model';
import { Rayon } from 'src/app/winpharm/models/produit/base/rayon.model';


export class VenteCriteria {
    beneficiaire?: Beneficiaire;
    categorie?: CategorieProduit;
    client?: Client;
    convention?: ConventionAssurance;
    dateDebut?: any;
    dateFacture?: any;
    dateFin?: any;
    forme?: FormeProduit;
    idFactureTpa?: number;
    laboratoire?: Fournisseur;
    medecin?: Medecin;
    modePaiement?: ModePaiement;
    nbrJour?: number;
    numeroBlv?: number;
    numeroFacture?: number;
    operateur?: Operateur;
    organisme?: OrganismeAssurance;
    rayon?: Rayon;
    statutEncaissement?: StatutEncaissementVente;
    statutLivraison?: StatutLivraisonVente;
    statutVente?: Statut;
    typeEncaissement?: TypeEncaissementVente;
    typeOperation?: TypeOperationVente;
    typeStatistiqueCriteria?: TypeStatistiqueCriteria;
    typeVente?: TypeVente;
    isFactureTPA?: boolean
    isFactureClient?: boolean

    listTypesVentes?: TypeVente[]
}

