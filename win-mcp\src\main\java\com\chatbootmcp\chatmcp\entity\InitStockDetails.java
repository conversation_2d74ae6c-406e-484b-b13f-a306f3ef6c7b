package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * InitStockDetails entity representing detailed stock initialization items
 */
@Entity
@Table(name = "init_stock_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InitStockDetails {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code_prd", length = 50)
    private String codePrd;

    @Column(name = "dsgn_prd")
    private String dsgnPrd;

    @Column(name = "num_lot", length = 100)
    private String numLot;

    @Column(name = "date_peremption")
    private LocalDate datePeremption;

    @Column(name = "quantite", precision = 15, scale = 2)
    private BigDecimal quantite;

    @Column(name = "prix_achat_ttc", precision = 15, scale = 2)
    private BigDecimal prixAchatTtc;

    @Column(name = "prix_vente_ttc", precision = 15, scale = 2)
    private BigDecimal prixVenteTtc;

    @Column(name = "mnt_ligne_achat_ttc", precision = 15, scale = 2)
    private BigDecimal mntLigneAchatTtc;

    @Column(name = "mnt_ligne_vente_ttc", precision = 15, scale = 2)
    private BigDecimal mntLigneVenteTtc;

    @Column(name = "prix_achat_std", precision = 15, scale = 2)
    private BigDecimal prixAchatStd;

    @Column(name = "prix_vente_std", precision = 15, scale = 2)
    private BigDecimal prixVenteStd;

    @Column(name = "prix_fab_ht", precision = 15, scale = 2)
    private BigDecimal prixFabHt;

    @Column(name = "prix_hosp", precision = 15, scale = 2)
    private BigDecimal prixHosp;

    @Column(name = "pbr_h", precision = 15, scale = 2)
    private BigDecimal pbrH;

    @Column(name = "pbr_p", precision = 15, scale = 2)
    private BigDecimal pbrP;

    @Column(name = "taux_marge", precision = 5, scale = 2)
    private BigDecimal tauxMarge;

    @Column(name = "taux_tva", precision = 5, scale = 2)
    private BigDecimal tauxTva;

    @Column(name = "num_ligne")
    private Integer numLigne;

    @Column(name = "code_barre", length = 50)
    private String codeBarre;

    @Column(name = "valider")
    private Boolean valider = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "init_stock_id")
    private InitStock initStock;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;

    @Column(name = "ctgr_id")
    private Long ctgrId;

    @Column(name = "frm_id")
    private Long frmId;

    @Column(name = "ft_id")
    private Long ftId;

    @Column(name = "labo_id")
    private Long laboId;
}
