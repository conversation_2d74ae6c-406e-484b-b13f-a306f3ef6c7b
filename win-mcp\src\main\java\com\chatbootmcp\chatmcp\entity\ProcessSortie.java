package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * ProcessSortie entity representing production process outputs
 */
@Entity
@Table(name = "process_sortie")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessSortie {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ordre_production_id")
    private OrdreProduction ordreProduction;
}
