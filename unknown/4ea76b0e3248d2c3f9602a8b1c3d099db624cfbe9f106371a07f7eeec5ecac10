{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  demo: \"saas\",\n  // other possible options are creative and modern\n  openaiApiKey: \"********************************************************************************************************************************************************************\",\n  mcpServerUrl: \"http://localhost:8081\",\n  // URL of the MCP server\n  useMcpServer: true // Set to false to use direct OpenAI calls\n};", "map": {"version": 3, "names": ["environment", "production", "demo", "openaiApiKey", "mcpServerUrl", "useMcpServer"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n  demo: \"saas\", // other possible options are creative and modern\r\n  openaiApiKey: \"********************************************************************************************************************************************************************\",\r\n  mcpServerUrl: \"http://localhost:8081\", // URL of the MCP server\r\n  useMcpServer: true, // Set to false to use direct OpenAI calls\r\n};"], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,IAAI,EAAE,MAAM;EAAE;EACdC,YAAY,EAAE,sKAAsK;EACpLC,YAAY,EAAE,uBAAuB;EAAE;EACvCC,YAAY,EAAE,IAAI,CAAE;CACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}