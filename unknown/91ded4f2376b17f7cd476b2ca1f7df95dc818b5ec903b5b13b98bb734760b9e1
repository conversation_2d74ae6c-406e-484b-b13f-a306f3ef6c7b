{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-home.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./chat-home.component.scss?ngResource\";\n// src/app/chat/components/chat-home.component.ts\nimport { Component, EventEmitter, Output } from '@angular/core';\nlet ChatHomeComponent = class ChatHomeComponent {\n  constructor() {\n    this.askQuestion = new EventEmitter();\n    this.selectArticle = new EventEmitter();\n    this.navigateToHelpTab = new EventEmitter();\n    this.featuredArticles = [{\n      id: 'article1',\n      title: 'Premier médicament marocain à base de cannabis',\n      subtitle: 'Innovation',\n      description: 'Le 19/12/2024',\n      content: 'Après avoir lancé, en 2015, le SSB 400, premier générique pour traiter l\\'hépatite C, Pharma 5 s\\'attaque à l\\'épilepsie ...',\n      image: 'https://cdn.sophatel.com/de4b0f80-4bd1-462f-8cea-9db2f798759e_cannabis.jpg',\n      url: 'https://www.officielpharma.ma/actuList'\n    }, {\n      id: 'article2',\n      title: 'OFFICINE PLUS 2022',\n      subtitle: 'Événement',\n      description: 'Le 01/09/2022',\n      content: 'Découvrez les 4 belles raisons pour une inscription en ligne ...',\n      image: 'https://cdn.sophatel.com/ca6e9c2a-a209-4bd2-bb34-8fad0b2af21e_OFFICINE%20PLUS%202022%20(2).jpg',\n      url: 'https://www.officielpharma.ma/actuList'\n    }, {\n      id: 'article3',\n      title: 'Suspension des AMM et retrait des lots contenant de la PHOLCODINE',\n      subtitle: 'Alerte sanitaire',\n      description: 'Le 28/03/2023',\n      content: 'Conformément aux recommandations de la Commission Nationale de Pharmacovigilance, il a été décidé la suspension des Autorisations ...',\n      image: 'https://cdn.sophatel.com/fe98abab-bb9f-44f1-8a59-72faa96349da_LOGO%20minist%C3%83%C2%A8re%20de%20la%20sant%C3%83%C2%A9.png',\n      url: 'https://www.officielpharma.ma/actuList'\n    }];\n    this.suggestedQuestions = [{\n      text: 'Quels sont les médicaments les plus vendus ce mois-ci ?'\n    }, {\n      text: 'Quels produits ont été les plus achetés cette année ?'\n    }, {\n      text: 'Quels sont les produits actuellement en rupture de stock ?'\n    }, {\n      text: 'Quels produits nécessitent un réapprovisionnement urgent ?'\n    }];\n  }\n  ngOnInit() {}\n  onAskQuestion() {\n    this.askQuestion.emit();\n  }\n  onSelectArticle(id) {\n    const article = this.featuredArticles.find(a => a.id === id);\n    if (article && article.url) {\n      window.open(article.url, '_blank');\n    } else {\n      this.selectArticle.emit(id);\n    }\n  }\n  onSelectQuestion(text) {\n    this.askQuestion.emit(text);\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      askQuestion: [{\n        type: Output\n      }],\n      selectArticle: [{\n        type: Output\n      }],\n      navigateToHelpTab: [{\n        type: Output\n      }]\n    };\n  }\n};\nChatHomeComponent = __decorate([Component({\n  selector: 'app-chat-home',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ChatHomeComponent);\nexport { ChatHomeComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Output", "ChatHomeComponent", "constructor", "askQuestion", "selectArticle", "navigateToHelpTab", "featuredArticles", "id", "title", "subtitle", "description", "content", "image", "url", "suggestedQuestions", "text", "ngOnInit", "onAskQuestion", "emit", "onSelectArticle", "article", "find", "a", "window", "open", "onSelectQuestion", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-home\\chat-home.component.ts"], "sourcesContent": ["// src/app/chat/components/chat-home.component.ts\r\nimport { Component, EventEmitter, OnInit, Output, ViewEncapsulation } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-chat-home',\r\n  templateUrl: './chat-home.component.html',\r\n  styleUrls: ['./chat-home.component.scss'],\r\n  // encapsulation: ViewEncapsulation.ShadowDom\r\n})\r\nexport class ChatHomeComponent implements OnInit {\r\n  @Output() askQuestion = new EventEmitter<string>();\r\n  @Output() selectArticle = new EventEmitter<string>();\r\n  @Output() navigateToHelpTab = new EventEmitter<void>();\r\n\r\n  featuredArticles = [\r\n    {\r\n      id: 'article1',\r\n      title: 'Premier médicament marocain à base de cannabis',\r\n      subtitle: 'Innovation',\r\n      description: 'Le 19/12/2024',\r\n      content: 'Après avoir lancé, en 2015, le SSB 400, premier générique pour traiter l\\'hépatite C, Pharma 5 s\\'attaque à l\\'épilepsie ...',\r\n      image: 'https://cdn.sophatel.com/de4b0f80-4bd1-462f-8cea-9db2f798759e_cannabis.jpg',\r\n      url: 'https://www.officielpharma.ma/actuList'\r\n    },\r\n    {\r\n      id: 'article2',\r\n      title: 'OFFICINE PLUS 2022',\r\n      subtitle: 'Événement',\r\n      description: 'Le 01/09/2022',\r\n      content: 'Découvrez les 4 belles raisons pour une inscription en ligne ...',\r\n      image: 'https://cdn.sophatel.com/ca6e9c2a-a209-4bd2-bb34-8fad0b2af21e_OFFICINE%20PLUS%202022%20(2).jpg',\r\n      url: 'https://www.officielpharma.ma/actuList'\r\n    },\r\n    {\r\n      id: 'article3',\r\n      title: 'Suspension des AMM et retrait des lots contenant de la PHOLCODINE',\r\n      subtitle: 'Alerte sanitaire',\r\n      description: 'Le 28/03/2023',\r\n      content: 'Conformément aux recommandations de la Commission Nationale de Pharmacovigilance, il a été décidé la suspension des Autorisations ...',\r\n      image: 'https://cdn.sophatel.com/fe98abab-bb9f-44f1-8a59-72faa96349da_LOGO%20minist%C3%83%C2%A8re%20de%20la%20sant%C3%83%C2%A9.png',\r\n      url: 'https://www.officielpharma.ma/actuList'\r\n    }\r\n  ];\r\n\r\n  suggestedQuestions = [\r\n    { text: 'Quels sont les médicaments les plus vendus ce mois-ci ?' },\r\n    { text: 'Quels produits ont été les plus achetés cette année ?' },\r\n    { text: 'Quels sont les produits actuellement en rupture de stock ?' },\r\n    { text: 'Quels produits nécessitent un réapprovisionnement urgent ?' }\r\n  ];\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void { }\r\n\r\n  onAskQuestion(): void {\r\n    this.askQuestion.emit();\r\n  }\r\n\r\n  onSelectArticle(id: string): void {\r\n    const article = this.featuredArticles.find(a => a.id === id);\r\n    if (article && article.url) {\r\n      window.open(article.url, '_blank');\r\n    } else {\r\n      this.selectArticle.emit(id);\r\n    }\r\n  }\r\n\r\n  onSelectQuestion(text: string): void {\r\n    this.askQuestion.emit(text);\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA;AACA,SAASA,SAAS,EAAEC,YAAY,EAAUC,MAAM,QAA2B,eAAe;AAQnF,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EA0C5BC,YAAA;IAzCU,KAAAC,WAAW,GAAG,IAAIJ,YAAY,EAAU;IACxC,KAAAK,aAAa,GAAG,IAAIL,YAAY,EAAU;IAC1C,KAAAM,iBAAiB,GAAG,IAAIN,YAAY,EAAQ;IAEtD,KAAAO,gBAAgB,GAAG,CACjB;MACEC,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,gDAAgD;MACvDC,QAAQ,EAAE,YAAY;MACtBC,WAAW,EAAE,eAAe;MAC5BC,OAAO,EAAE,8HAA8H;MACvIC,KAAK,EAAE,4EAA4E;MACnFC,GAAG,EAAE;KACN,EACD;MACEN,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,QAAQ,EAAE,WAAW;MACrBC,WAAW,EAAE,eAAe;MAC5BC,OAAO,EAAE,kEAAkE;MAC3EC,KAAK,EAAE,gGAAgG;MACvGC,GAAG,EAAE;KACN,EACD;MACEN,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,mEAAmE;MAC1EC,QAAQ,EAAE,kBAAkB;MAC5BC,WAAW,EAAE,eAAe;MAC5BC,OAAO,EAAE,uIAAuI;MAChJC,KAAK,EAAE,4HAA4H;MACnIC,GAAG,EAAE;KACN,CACF;IAED,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,IAAI,EAAE;IAAyD,CAAE,EACnE;MAAEA,IAAI,EAAE;IAAuD,CAAE,EACjE;MAAEA,IAAI,EAAE;IAA4D,CAAE,EACtE;MAAEA,IAAI,EAAE;IAA4D,CAAE,CACvE;EAEe;EAEhBC,QAAQA,CAAA,GAAW;EAEnBC,aAAaA,CAAA;IACX,IAAI,CAACd,WAAW,CAACe,IAAI,EAAE;EACzB;EAEAC,eAAeA,CAACZ,EAAU;IACxB,MAAMa,OAAO,GAAG,IAAI,CAACd,gBAAgB,CAACe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKA,EAAE,CAAC;IAC5D,IAAIa,OAAO,IAAIA,OAAO,CAACP,GAAG,EAAE;MAC1BU,MAAM,CAACC,IAAI,CAACJ,OAAO,CAACP,GAAG,EAAE,QAAQ,CAAC;IACpC,CAAC,MAAM;MACL,IAAI,CAACT,aAAa,CAACc,IAAI,CAACX,EAAE,CAAC;IAC7B;EACF;EAEAkB,gBAAgBA,CAACV,IAAY;IAC3B,IAAI,CAACZ,WAAW,CAACe,IAAI,CAACH,IAAI,CAAC;EAC7B;;;;;;;cA5DCf;MAAM;;cACNA;MAAM;;cACNA;MAAM;;;;AAHIC,iBAAiB,GAAAyB,UAAA,EAN7B5B,SAAS,CAAC;EACT6B,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;;CAG1C,CAAC,C,EACW5B,iBAAiB,CA8D7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}