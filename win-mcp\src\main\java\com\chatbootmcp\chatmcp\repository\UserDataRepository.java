package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.entity.UserData;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.List;
import java.util.Optional;

public interface UserDataRepository extends JpaRepository<UserData, Long> {
    List<UserData> findByUser(User user);
    Optional<UserData> findByUserAndDataType(User user, String dataType);
}
