package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Fournisseur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Fournisseur entity
 */
@Repository
public interface FournisseurRepository extends JpaRepository<Fournisseur, Long> {

    Optional<Fournisseur> findByCodeFournisseur(String codeFournisseur);

    List<Fournisseur> findByRaisonSocialeContainingIgnoreCase(String raisonSociale);

    @Query("SELECT f FROM Fournisseur f ORDER BY f.raisonSociale")
    List<Fournisseur> findAllOrderByRaisonSociale();
}
