package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.entity.PersonalMedicalProfile;
import com.chatbootmcp.chatmcp.repository.PersonalMedicalProfileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 🏥 Personal Medical Profile Controller for Win-MCP
 * Provides medical profile information for users
 */
@RestController
@RequestMapping("/api/winplus")
@CrossOrigin(origins = "*")
public class PersonalMedicalProfileController {

    @Autowired
    private PersonalMedicalProfileRepository personalMedicalProfileRepository;

    /**
     * Get medical profile for a specific user
     */
    @GetMapping("/medical-profile/{username}")
    public ResponseEntity<Map<String, Object>> getMedicalProfile(@PathVariable String username) {
        try {
            System.out.println("🏥 Fetching medical profile for user: " + username);
            
            Optional<PersonalMedicalProfile> profileOpt = personalMedicalProfileRepository.findByUsername(username);
            
            Map<String, Object> response = new HashMap<>();
            
            if (profileOpt.isPresent()) {
                PersonalMedicalProfile profile = profileOpt.get();
                
                Map<String, Object> medicalData = new HashMap<>();
                medicalData.put("id", profile.getId());
                medicalData.put("username", profile.getUsername());
                medicalData.put("bloodType", profile.getBloodType());
                medicalData.put("allergies", profile.getAllergies());
                medicalData.put("chronicConditions", profile.getChronicConditions());
                medicalData.put("currentMedications", profile.getCurrentMedications());
                medicalData.put("emergencyContact", profile.getEmergencyContact());
                medicalData.put("emergencyPhone", profile.getEmergencyPhone());
                medicalData.put("insuranceNumber", profile.getInsuranceNumber());
                medicalData.put("doctorName", profile.getDoctorName());
                medicalData.put("doctorPhone", profile.getDoctorPhone());
                medicalData.put("birthDate", profile.getBirthDate());
                medicalData.put("heightCm", profile.getHeightCm());
                medicalData.put("weightKg", profile.getWeightKg());
                medicalData.put("lastCheckupDate", profile.getLastCheckupDate());
                medicalData.put("medicalNotes", profile.getMedicalNotes());
                medicalData.put("createdAt", profile.getCreatedAt());
                medicalData.put("updatedAt", profile.getUpdatedAt());
                
                response.put("status", "success");
                response.put("medicalProfile", medicalData);
                
                System.out.println("✅ Medical profile found for user: " + username);
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "not_found");
                response.put("message", "Profil médical non trouvé pour l'utilisateur: " + username);
                
                System.out.println("❌ Medical profile not found for user: " + username);
                return ResponseEntity.ok(response);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error fetching medical profile for user " + username + ": " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Erreur lors de la récupération du profil médical: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Get all medical profiles (for admin purposes)
     */
    @GetMapping("/medical-profiles")
    public ResponseEntity<Map<String, Object>> getAllMedicalProfiles() {
        try {
            System.out.println("🏥 Fetching all medical profiles...");
            
            Iterable<PersonalMedicalProfile> profiles = personalMedicalProfileRepository.findAll();
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("medicalProfiles", profiles);
            response.put("count", personalMedicalProfileRepository.count());
            
            System.out.println("✅ Fetched " + personalMedicalProfileRepository.count() + " medical profiles");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("❌ Error fetching all medical profiles: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Erreur lors de la récupération des profils médicaux: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Create or update medical profile
     */
    @PostMapping("/medical-profile")
    public ResponseEntity<Map<String, Object>> createOrUpdateMedicalProfile(@RequestBody PersonalMedicalProfile profile) {
        try {
            System.out.println("🏥 Creating/updating medical profile for user: " + profile.getUsername());
            
            PersonalMedicalProfile savedProfile = personalMedicalProfileRepository.save(profile);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("medicalProfile", savedProfile);
            response.put("message", "Profil médical sauvegardé avec succès");
            
            System.out.println("✅ Medical profile saved for user: " + profile.getUsername());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("❌ Error saving medical profile: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Erreur lors de la sauvegarde du profil médical: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Delete medical profile
     */
    @DeleteMapping("/medical-profile/{username}")
    public ResponseEntity<Map<String, Object>> deleteMedicalProfile(@PathVariable String username) {
        try {
            System.out.println("🏥 Deleting medical profile for user: " + username);
            
            Optional<PersonalMedicalProfile> profileOpt = personalMedicalProfileRepository.findByUsername(username);
            
            Map<String, Object> response = new HashMap<>();
            
            if (profileOpt.isPresent()) {
                personalMedicalProfileRepository.delete(profileOpt.get());
                
                response.put("status", "success");
                response.put("message", "Profil médical supprimé avec succès");
                
                System.out.println("✅ Medical profile deleted for user: " + username);
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "not_found");
                response.put("message", "Profil médical non trouvé pour l'utilisateur: " + username);
                
                System.out.println("❌ Medical profile not found for deletion: " + username);
                return ResponseEntity.ok(response);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error deleting medical profile for user " + username + ": " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Erreur lors de la suppression du profil médical: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
