interface IProduitFournisseurPayload {
    id: number;
    code_site: number; 
    id_source: string; 
    code_winplus: string; 
    designation: string; 
    verified: boolean;
}
export type ProductFournisseurResponse = {
    code_barre: string;
    code_groupe: string;
    code_site: number;
    code_winplus: string | null;
    designation: string;
    id: number;
    id_source: string;
    prix_achat_std: number;
    prix_vente_std: number;
    verified: boolean;
  };

export type PaginatedProductFournisseurResponse = {
  products: ProductFournisseurResponse[];
  totalElements: number;
}

export type ProduitFournisseurCriteria  = {
    code_site?: number; 
    id_source?: string; 
    designation?: string; 
    code_winplus?: string; 
    code_groupe?: string; 
    verified?: string; 
    id?:string;
    page?: number;
    page_size?: number;
  }



export class ProduitFournisseurPayload implements IProduitFournisseurPayload{
    id: number;
    code_site: number;
    id_source: string;
    code_winplus: string;
    designation: string;
    verified: boolean;

    
    constructor(data: IProduitFournisseurPayload) {
        Object.assign(this, data);
    }
}