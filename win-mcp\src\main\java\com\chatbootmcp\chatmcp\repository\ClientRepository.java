package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Client;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ClientRepository extends JpaRepository<Client, Long> {
    
    Optional<Client> findByCodeClient(String codeClient);
    
    List<Client> findByEstActifTrue();
    
    Page<Client> findByEstActifTrue(Pageable pageable);
    
    @Query("SELECT c FROM Client c WHERE c.estActif = true AND " +
           "(LOWER(c.nom) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.prenom) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.codeClient) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Client> findActiveClientsBySearchTerm(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT c FROM Client c WHERE c.soldeClient > 0")
    List<Client> findClientsWithPositiveBalance();
    
    @Query("SELECT c FROM Client c WHERE c.soldeClient < 0")
    List<Client> findClientsWithNegativeBalance();
    
    @Query("SELECT c FROM Client c WHERE c.caClient > :minCa ORDER BY c.caClient DESC")
    List<Client> findTopClientsByCA(@Param("minCa") java.math.BigDecimal minCa);
}
