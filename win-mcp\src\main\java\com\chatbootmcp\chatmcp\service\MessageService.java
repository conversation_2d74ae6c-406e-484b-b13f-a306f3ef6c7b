package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.dto.request.MessageRequest;
import com.chatbootmcp.chatmcp.dto.response.MessageResponse;
import com.chatbootmcp.chatmcp.entity.Conversation;
import com.chatbootmcp.chatmcp.entity.Message;
import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.exception.ResourceNotFoundException;
import com.chatbootmcp.chatmcp.repository.ConversationRepository;
import com.chatbootmcp.chatmcp.repository.MessageRepository;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MessageService {

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private ConversationRepository conversationRepository;

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private ConversationService conversationService;
    
    @Autowired
    private AIService aiService;
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    public List<MessageResponse> getConversationMessages(Long conversationId, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new ResourceNotFoundException("Conversation not found"));
        
        if (!conversation.getUser().getId().equals(user.getId())) {
            throw new ResourceNotFoundException("Conversation not found for this user");
        }
        
        return messageRepository.findByConversationOrderByTimestampAsc(conversation).stream()
                .map(MessageResponse::new)
                .collect(Collectors.toList());
    }

    public MessageResponse sendMessage(String username, MessageRequest request) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Conversation conversation = conversationRepository.findById(request.getConversationId())
                .orElseThrow(() -> new ResourceNotFoundException("Conversation not found"));
        
        if (!conversation.getUser().getId().equals(user.getId())) {
            throw new ResourceNotFoundException("Conversation not found for this user");
        }
        
        // Create and save user message
        Message userMessage = new Message();
        userMessage.setConversation(conversation);
        userMessage.setContent(request.getContent());
        userMessage.setRole(Message.MessageRole.USER);
        userMessage.setTimestamp(LocalDateTime.now());
        
        messageRepository.save(userMessage);
        
        // Update conversation timestamp
        conversationService.updateConversationTimestamp(conversation.getId());
        
        // Get conversation history for context
        List<Message> conversationHistory = messageRepository.findByConversationOrderByTimestampAsc(conversation);
        
       // Generate AI response
        String aiResponse = aiService.generateResponse(request.getContent(), conversationHistory);
        
        // Create and save assistant message
        Message assistantMessage = new Message();
        assistantMessage.setConversation(conversation);
        assistantMessage.setContent(aiResponse);
        assistantMessage.setRole(Message.MessageRole.ASSISTANT);
        assistantMessage.setTimestamp(LocalDateTime.now());
        
        messageRepository.save(assistantMessage);
        
        // Update conversation timestamp again
        conversationService.updateConversationTimestamp(conversation.getId());
        
        // Send real-time update via WebSocket
        MessageResponse assistantResponse = new MessageResponse(assistantMessage);
        messagingTemplate.convertAndSend("/topic/conversation/" + conversation.getId(), assistantResponse);
        
        return assistantResponse;
    }
}
