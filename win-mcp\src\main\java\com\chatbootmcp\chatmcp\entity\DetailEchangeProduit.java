package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * DetailEchangeProduit entity representing product details in exchanges
 */
@Entity
@Table(name = "detail_echange_produit")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailEchangeProduit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "entete_echange_id")
    private EnteteEchange enteteEchange;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;
}
