<!DOCTYPE html>
<html>
<head>
    <title>Mock Data Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            margin-right: 10px;
        }
        input {
            padding: 8px;
            margin: 5px 0;
            width: 100%;
            box-sizing: border-box;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .tabs {
            display: flex;
            margin-bottom: 10px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            background-color: #f1f1f1;
            margin-right: 5px;
        }
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
        }
        .tab-content {
            display: none;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 0 5px 5px 5px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Chat MCP Mock Data Test</h1>
    
    <div class="container">
        <h2>Authentication</h2>
        <div>
            <label for="username">Username:</label>
            <input type="text" id="username" value="user1">
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" value="password">
        </div>
        <button id="loginBtn">Login</button>
        <pre id="loginResult"></pre>
    </div>
    
    <div class="container">
        <h2>JWT Token</h2>
        <div>
            <label for="token">JWT Token:</label>
            <input type="text" id="token" placeholder="Your JWT token will appear here after login">
        </div>
    </div>
    
    <div class="container">
        <h2>Mock Data APIs</h2>
        
        <div class="tabs">
            <div class="tab active" data-tab="dashboard">Dashboard</div>
            <div class="tab" data-tab="analytics">Analytics</div>
            <div class="tab" data-tab="notifications">Notifications</div>
            <div class="tab" data-tab="recommendations">Recommendations</div>
        </div>
        
        <div class="tab-content active" id="dashboard-tab">
            <p>Get dashboard data for the current user:</p>
            <button id="getDashboardBtn">Get Dashboard Data</button>
            <pre id="dashboardResult"></pre>
        </div>
        
        <div class="tab-content" id="analytics-tab">
            <p>Get analytics data for the current user:</p>
            <div>
                <label for="period">Time Period:</label>
                <select id="period">
                    <option value="day">Day</option>
                    <option value="week" selected>Week</option>
                    <option value="month">Month</option>
                    <option value="year">Year</option>
                </select>
            </div>
            <button id="getAnalyticsBtn">Get Analytics Data</button>
            <pre id="analyticsResult"></pre>
        </div>
        
        <div class="tab-content" id="notifications-tab">
            <p>Get notifications for the current user:</p>
            <button id="getNotificationsBtn">Get Notifications</button>
            <pre id="notificationsResult"></pre>
        </div>
        
        <div class="tab-content" id="recommendations-tab">
            <p>Get recommendations for the current user:</p>
            <button id="getRecommendationsBtn">Get Recommendations</button>
            <pre id="recommendationsResult"></pre>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and tab contents
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab + '-tab').classList.add('active');
            });
        });
        
        // Login
        document.getElementById('loginBtn').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('loginResult').textContent = JSON.stringify(data, null, 2);
                    document.getElementById('token').value = data.token;
                } else {
                    const error = await response.json();
                    document.getElementById('loginResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
                }
            } catch (error) {
                document.getElementById('loginResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Get Dashboard Data
        document.getElementById('getDashboardBtn').addEventListener('click', async () => {
            const token = document.getElementById('token').value;
            
            if (!token) {
                document.getElementById('dashboardResult').textContent = 'Error: Please login first to get a token';
                return;
            }
            
            try {
                const response = await fetch('/api/mock-data/dashboard', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('dashboardResult').textContent = JSON.stringify(data, null, 2);
                } else {
                    const error = await response.text();
                    document.getElementById('dashboardResult').textContent = 'Error: ' + error;
                }
            } catch (error) {
                document.getElementById('dashboardResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Get Analytics Data
        document.getElementById('getAnalyticsBtn').addEventListener('click', async () => {
            const token = document.getElementById('token').value;
            const period = document.getElementById('period').value;
            
            if (!token) {
                document.getElementById('analyticsResult').textContent = 'Error: Please login first to get a token';
                return;
            }
            
            try {
                const response = await fetch(`/api/mock-data/analytics?period=${period}`, {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('analyticsResult').textContent = JSON.stringify(data, null, 2);
                } else {
                    const error = await response.text();
                    document.getElementById('analyticsResult').textContent = 'Error: ' + error;
                }
            } catch (error) {
                document.getElementById('analyticsResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Get Notifications
        document.getElementById('getNotificationsBtn').addEventListener('click', async () => {
            const token = document.getElementById('token').value;
            
            if (!token) {
                document.getElementById('notificationsResult').textContent = 'Error: Please login first to get a token';
                return;
            }
            
            try {
                const response = await fetch('/api/mock-data/notifications', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('notificationsResult').textContent = JSON.stringify(data, null, 2);
                } else {
                    const error = await response.text();
                    document.getElementById('notificationsResult').textContent = 'Error: ' + error;
                }
            } catch (error) {
                document.getElementById('notificationsResult').textContent = 'Error: ' + error.message;
            }
        });
        
        // Get Recommendations
        document.getElementById('getRecommendationsBtn').addEventListener('click', async () => {
            const token = document.getElementById('token').value;
            
            if (!token) {
                document.getElementById('recommendationsResult').textContent = 'Error: Please login first to get a token';
                return;
            }
            
            try {
                const response = await fetch('/api/mock-data/recommendations', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('recommendationsResult').textContent = JSON.stringify(data, null, 2);
                } else {
                    const error = await response.text();
                    document.getElementById('recommendationsResult').textContent = 'Error: ' + error;
                }
            } catch (error) {
                document.getElementById('recommendationsResult').textContent = 'Error: ' + error.message;
            }
        });
    </script>
</body>
</html>
