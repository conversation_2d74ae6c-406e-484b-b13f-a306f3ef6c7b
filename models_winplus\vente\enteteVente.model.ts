import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { ModePaiement } from 'src/app/winpharm/enums/common/ModePaiement.enum';
import { SousStatutVente } from 'src/app/winpharm/enums/vente/SousStatutVente.enum';
import { StatutLivraisonVente } from 'src/app/winpharm/enums/vente/StatutLivraisonVente.enum';
import { TypeAvoirVente } from 'src/app/winpharm/enums/vente/TypeAvoirVente.enum';
import { TypeEncaissementVente } from 'src/app/winpharm/enums/vente/TypeEncaissementVente.enum';
import { TypeOperationVente } from 'src/app/winpharm/enums/vente/TypeOperationVente.enum';
import { TypeVente } from 'src/app/winpharm/enums/vente/TypeVente.enum';


// import { Moment } from 'moment';

import { Beneficiaire } from 'src/app/winpharm/models/tiers/client/beneficiaire.model';
import { Client } from 'src/app/winpharm/models/tiers/client/client.model';
import { DetailVente } from './detailVente.model';
import { EnteteBlVente } from './enteteBlVente.model';
import { EnteteVenteExtTpa } from './enteteVenteExtTpa.model';
import { Medecin } from 'src/app/winpharm/models/common/medecin.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { StatutEncaissementVente } from '../../enums/vente/StatutEncaissementVente.enum';
import { ConfigurationGestionRemise } from '../../enums/parametre/ConfigurationGestionRemise.enum';
import { TypeRemiseClient } from '../../enums/tiers/TypeRemiseClient.enum';
import { DocumentVentilable } from '../common/documentVentilable.model';
import { DocumentFile } from 'src/app/shared/upload-file/models/document.model';


export class EnteteVente {
    audited?: boolean;
    autresTermes?: string;
    bls?: EnteteBlVente[];
    client?: Client;
    dateVente?: any;
    details?: DetailVente[];
    documentVentilable?: DocumentVentilable;
    enteteExtTPA?: EnteteVenteExtTpa;
    heureVenteDebut?: any;
    heureVenteFin?: any;
    id?: number;
    maladieChronique?: boolean;
    medecin?: Medecin;
    mntBrutHt?: number;
    mntBrutTtc?: number;
    mntEncaisse?: number;
    mntMargeEffHt?: number;
    mntMargeEffTtc?: number;
    mntMargeStdHt?: number;
    mntMargeStdTtc?: number;
    mntNetHt?: number;
    mntNetTtc?: number;
    mntRemiseHt?: number;
    mntRemiseTtc?: number;
    mntTaxeParafiscale?: number;
    mntTva?: number;
    mntVenteDu?: number;
    modeEncaissement?: ModePaiement;
    nbrLignes?: number;
    nbrPrd?: number;
    nomClient?: string;
    nomPatient?: string;
    numBlv?: number;
    numFacture?: number;
    numVente?: number;
    operateur?: Operateur;
    patient?: Beneficiaire;
    resteAVentiler?: number;
    soldeClientAvantVente?: number;
    sousStatut?: SousStatutVente;
    statut?: Statut;
    statutLivre?: StatutLivraisonVente;
    tauxRemise?: number;
    totalQte?: number;
    typeAvoir?: TypeAvoirVente;
    typeEncaissement?: TypeEncaissementVente;
    typeOperation?: TypeOperationVente;
    typeVente?: TypeVente;
    userModifiable?: boolean;


    configurationGestionRemise?: ConfigurationGestionRemise;
    typeRemiseClient?: TypeRemiseClient;
    ordonnance?: DocumentFile

    /****************************** transient fields ******************************/
    statusEnc?: StatutEncaissementVente;
    totalMontantResteApayer?: number;


    montantRecu?: number;
    monnaie?: "dhs" | "rial";
    monnaieRendue?: number





    constructor(configurationGestionRemise: ConfigurationGestionRemise, operator: Operateur = null) {
        this.statut = Statut.BROUILLON;

        // this.typeOperation = TypeOperationVente.VENTE;
        this.typeVente = TypeVente.CONSEIL;
        this.typeEncaissement = TypeEncaissementVente.COMPTANT;
        this.modeEncaissement = ModePaiement.ESPECE;

        this.configurationGestionRemise = configurationGestionRemise;


        this.maladieChronique = false;
        this.totalQte = 0;
        this.mntBrutTtc = 0;
        this.mntNetTtc = 0;
        this.mntVenteDu = 0;
        this.mntEncaisse = 0;
        this.totalMontantResteApayer = 0;
        this.mntRemiseTtc = 0;
        this.tauxRemise = 0;

        this.enteteExtTPA = new EnteteVenteExtTpa();

        this.details = [];


        this.operateur = operator;
    }


}

