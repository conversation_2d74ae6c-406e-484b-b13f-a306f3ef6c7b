package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "invoices")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Invoice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "invoice_number", unique = true, nullable = false)
    private String invoiceNumber;
    
    @Column(name = "invoice_date", nullable = false)
    private LocalDate invoiceDate;
    
    @Column(name = "due_date")
    private LocalDate dueDate;
    
    @Column(name = "subtotal", nullable = false)
    private Double subtotal;
    
    @Column(name = "tax_amount")
    private Double taxAmount;
    
    @Column(name = "discount_amount")
    private Double discountAmount;
    
    @Column(name = "total_amount", nullable = false)
    private Double totalAmount;
    
    @Column(name = "currency")
    private String currency;
    
    @Column(name = "status")
    private String status; // DRAFT, SENT, PAID, OVERDUE, CANCELLED
    
    @Column(name = "payment_status")
    private String paymentStatus; // UNPAID, PARTIAL, PAID
    
    @Column(name = "payment_method")
    private String paymentMethod;
    
    @Column(name = "payment_date")
    private LocalDate paymentDate;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "billing_period_start")
    private LocalDate billingPeriodStart;
    
    @Column(name = "billing_period_end")
    private LocalDate billingPeriodEnd;
    
    @Column(name = "service_type")
    private String serviceType; // SUBSCRIPTION, ONE_TIME, RECURRING
    
    @Column(name = "notes")
    private String notes;
    
    @Column(name = "pdf_url")
    private String pdfUrl;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
