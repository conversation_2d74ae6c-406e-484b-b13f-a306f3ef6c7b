package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.UserInvoicesRequest;
import com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.scheduler.Schedulers;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Service containing tool functions for communicating with win-mcp (WinPlus) backend
 * This service handles all interactions with the WinPlus simulation APIs
 */
@Service
public class WinMcpToolsFunctions {

    @Autowired
    private ChatMcpApiService chatMcpApiService; // For authentication

    private final WebClient winMcpWebClient;

    public WinMcpToolsFunctions() {
        this.winMcpWebClient = WebClient.builder()
                .baseUrl("http://localhost:8080")
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    /**
     * Tool function to get user profile data from WinPlus
     */
    public Function<UserProfileRequest, String> getWinPlusUserProfileTool() {
        return request -> {
            try {
                // Authenticate first
                String token = chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (token == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Call the WinPlus API
                Map<String, Object> userData = winMcpWebClient.get()
                        .uri("/api/winplus/user-data/" + request.username())
                        .header("Authorization", "Bearer " + token)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (userData == null) {
                    return "Aucune donnée utilisateur trouvée dans WinPlus pour: " + request.username();
                }

                return formatWinPlusUserData(userData);

            } catch (Exception e) {
                System.out.println("Error in getWinPlusUserProfileTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération du profil utilisateur depuis WinPlus: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get client information from WinPlus
     */
    public Function<UserProfileRequest, String> getWinPlusClientInfoTool() {
        return request -> {
            try {
                // Call the WinPlus API to get client by code
                Map<String, Object> clientData = winMcpWebClient.get()
                        .uri("/api/winplus/clients/code/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (clientData == null) {
                    return "Aucun client trouvé dans WinPlus pour le code: " + request.username();
                }

                return formatWinPlusClientData(clientData);

            } catch (Exception e) {
                System.out.println("Error in getWinPlusClientInfoTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des informations client depuis WinPlus: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get sales/ventes from WinPlus
     */
    public Function<UserProfileRequest, String> getWinPlusSalesTool() {
        return request -> {
            try {
                // First get client ID
                Map<String, Object> clientData = winMcpWebClient.get()
                        .uri("/api/winplus/clients/code/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (clientData == null) {
                    return "Aucun client trouvé pour récupérer les ventes: " + request.username();
                }

                Long clientId = Long.valueOf(clientData.get("id").toString());

                // Get sales for this client
                Map<String, Object> salesData = winMcpWebClient.get()
                        .uri("/api/winplus/ventes/client/" + clientId + "?size=10")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (salesData == null) {
                    return "Aucune vente trouvée pour le client: " + request.username();
                }

                return formatWinPlusSalesData(salesData);

            } catch (Exception e) {
                System.out.println("Error in getWinPlusSalesTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des ventes depuis WinPlus: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get products from WinPlus
     */
    public Function<UserProfileRequest, String> getWinPlusProductsTool() {
        return request -> {
            try {
                // Get products
                Map<String, Object> productsData = winMcpWebClient.get()
                        .uri("/api/winplus/produits?size=10")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (productsData == null) {
                    return "Aucun produit trouvé dans WinPlus";
                }

                return formatWinPlusProductsData(productsData);

            } catch (Exception e) {
                System.out.println("Error in getWinPlusProductsTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des produits depuis WinPlus: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get purchases/achats from WinPlus
     */
    public Function<UserProfileRequest, String> getWinPlusPurchasesTool() {
        return request -> {
            try {
                // Get purchases
                Map<String, Object> purchasesData = winMcpWebClient.get()
                        .uri("/api/winplus/achats?size=10")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (purchasesData == null) {
                    return "Aucun achat trouvé dans WinPlus";
                }

                return formatWinPlusPurchasesData(purchasesData);

            } catch (Exception e) {
                System.out.println("Error in getWinPlusPurchasesTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des achats depuis WinPlus: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get dashboard summary from WinPlus
     */
    public Function<UserProfileRequest, String> getWinPlusDashboardTool() {
        return request -> {
            try {
                // Get dashboard summary
                Map<String, Object> dashboardData = winMcpWebClient.get()
                        .uri("/api/winplus/dashboard/summary")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (dashboardData == null) {
                    return "Aucune donnée de tableau de bord trouvée dans WinPlus";
                }

                return formatWinPlusDashboardData(dashboardData);

            } catch (Exception e) {
                System.out.println("Error in getWinPlusDashboardTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération du tableau de bord depuis WinPlus: " + e.getMessage();
            }
        };
    }

    // ==================== FORMATTING METHODS ====================

    private String formatWinPlusUserData(Map<String, Object> userData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== DONNÉES UTILISATEUR WINPLUS ===\n");
        
        if (userData.containsKey("user")) {
            Map<String, Object> user = (Map<String, Object>) userData.get("user");
            sb.append("Utilisateur: ").append(user.get("username")).append("\n");
            sb.append("Email: ").append(user.get("email")).append("\n");
            sb.append("Nom complet: ").append(user.get("fullName")).append("\n");
        }
        
        if (userData.containsKey("client")) {
            Map<String, Object> client = (Map<String, Object>) userData.get("client");
            sb.append("\n=== INFORMATIONS CLIENT ===\n");
            sb.append("Code client: ").append(client.get("codeClient")).append("\n");
            sb.append("Nom: ").append(client.get("nom")).append(" ").append(client.get("prenom")).append("\n");
            sb.append("Adresse: ").append(client.get("adr1")).append("\n");
            sb.append("Téléphone: ").append(client.get("gsm")).append("\n");
            sb.append("Solde: ").append(client.get("soldeClient")).append(" DH\n");
            sb.append("Plafond crédit: ").append(client.get("plafondCredit")).append(" DH\n");
            sb.append("CA Client: ").append(client.get("caClient")).append(" DH\n");
        }
        
        if (userData.containsKey("clientStatistics")) {
            Map<String, Object> stats = (Map<String, Object>) userData.get("clientStatistics");
            sb.append("\n=== STATISTIQUES CLIENT ===\n");
            sb.append("Nombre de ventes: ").append(stats.get("totalSales")).append("\n");
            sb.append("Montant total: ").append(stats.get("totalAmount")).append(" DH\n");
        }
        
        return sb.toString();
    }

    private String formatWinPlusClientData(Map<String, Object> clientData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== INFORMATIONS CLIENT WINPLUS ===\n");
        sb.append("Code client: ").append(clientData.get("codeClient")).append("\n");
        sb.append("Nom: ").append(clientData.get("nom")).append(" ").append(clientData.get("prenom")).append("\n");
        sb.append("Email: ").append(clientData.get("email")).append("\n");
        sb.append("Téléphone: ").append(clientData.get("gsm")).append("\n");
        sb.append("Adresse: ").append(clientData.get("adr1"));
        if (clientData.get("adr2") != null) {
            sb.append(", ").append(clientData.get("adr2"));
        }
        sb.append("\n");
        sb.append("Solde actuel: ").append(clientData.get("soldeClient")).append(" DH\n");
        sb.append("Plafond crédit: ").append(clientData.get("plafondCredit")).append(" DH\n");
        sb.append("Chiffre d'affaires: ").append(clientData.get("caClient")).append(" DH\n");
        sb.append("Taux remise: ").append(clientData.get("tauxRemise")).append("%\n");
        sb.append("Statut: ").append(clientData.get("estActif").equals(true) ? "Actif" : "Inactif").append("\n");
        
        return sb.toString();
    }

    private String formatWinPlusSalesData(Map<String, Object> salesData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== VENTES WINPLUS ===\n");
        
        if (salesData.containsKey("ventes")) {
            List<Map<String, Object>> ventes = (List<Map<String, Object>>) salesData.get("ventes");
            
            for (Map<String, Object> vente : ventes) {
                sb.append("Vente #").append(vente.get("numVente")).append("\n");
                sb.append("  Date: ").append(vente.get("dateVente")).append("\n");
                sb.append("  Montant TTC: ").append(vente.get("mntNetTtc")).append(" DH\n");
                sb.append("  Quantité: ").append(vente.get("totalQte")).append("\n");
                sb.append("  Nombre d'articles: ").append(vente.get("nbrPrd")).append("\n");
                sb.append("  Remise: ").append(vente.get("mntRemiseTtc")).append(" DH\n\n");
            }
        }
        
        sb.append("Total des éléments: ").append(salesData.get("totalItems")).append("\n");
        
        return sb.toString();
    }

    private String formatWinPlusProductsData(Map<String, Object> productsData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== PRODUITS WINPLUS ===\n");
        
        if (productsData.containsKey("produits")) {
            List<Map<String, Object>> produits = (List<Map<String, Object>>) productsData.get("produits");
            
            for (Map<String, Object> produit : produits) {
                sb.append("Produit: ").append(produit.get("designation")).append("\n");
                sb.append("  Code: ").append(produit.get("codePrd")).append("\n");
                sb.append("  Prix vente: ").append(produit.get("prixVenteStd")).append(" DH\n");
                sb.append("  Stock: ").append(produit.get("totalStock")).append("\n");
                sb.append("  Prescription: ").append(produit.get("estOblgPrescription").equals(true) ? "Oui" : "Non").append("\n\n");
            }
        }
        
        return sb.toString();
    }

    private String formatWinPlusPurchasesData(Map<String, Object> purchasesData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== ACHATS WINPLUS ===\n");
        
        if (purchasesData.containsKey("achats")) {
            List<Map<String, Object>> achats = (List<Map<String, Object>>) purchasesData.get("achats");
            
            for (Map<String, Object> achat : achats) {
                sb.append("Facture: ").append(achat.get("numFacture")).append("\n");
                sb.append("  Date: ").append(achat.get("dateFacture")).append("\n");
                sb.append("  Montant TTC: ").append(achat.get("montantTtc")).append(" DH\n");
                sb.append("  Statut: ").append(achat.get("statut")).append("\n");
                sb.append("  Réglée: ").append(achat.get("estReglee").equals(true) ? "Oui" : "Non").append("\n\n");
            }
        }
        
        return sb.toString();
    }

    private String formatWinPlusDashboardData(Map<String, Object> dashboardData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== TABLEAU DE BORD WINPLUS ===\n");
        
        if (dashboardData.containsKey("clients")) {
            Map<String, Object> clients = (Map<String, Object>) dashboardData.get("clients");
            sb.append("Clients total: ").append(clients.get("total")).append("\n");
            sb.append("Clients actifs: ").append(clients.get("active")).append("\n");
        }
        
        if (dashboardData.containsKey("products")) {
            Map<String, Object> products = (Map<String, Object>) dashboardData.get("products");
            sb.append("Produits total: ").append(products.get("total")).append("\n");
            sb.append("Produits vendables: ").append(products.get("vendable")).append("\n");
        }
        
        if (dashboardData.containsKey("sales")) {
            Map<String, Object> sales = (Map<String, Object>) dashboardData.get("sales");
            sb.append("Ventes (30 derniers jours): ").append(sales.get("last30Days")).append(" DH\n");
        }
        
        if (dashboardData.containsKey("purchases")) {
            Map<String, Object> purchases = (Map<String, Object>) dashboardData.get("purchases");
            sb.append("Achats (30 derniers jours): ").append(purchases.get("last30Days")).append(" DH\n");
            sb.append("Montant impayé: ").append(purchases.get("outstanding")).append(" DH\n");
        }
        
        return sb.toString();
    }
}
