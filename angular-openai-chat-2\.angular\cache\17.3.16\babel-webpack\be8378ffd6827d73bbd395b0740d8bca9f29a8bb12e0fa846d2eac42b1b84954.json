{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-message.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./chat-message.component.scss?ngResource\";\n// chat-message.component.ts\nimport { Component } from '@angular/core';\nlet ChatMessageComponent = class ChatMessageComponent {};\nChatMessageComponent = __decorate([Component({\n  selector: 'app-chat-message',\n  standalone: true,\n  imports: [],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ChatMessageComponent);\nexport { ChatMessageComponent };", "map": {"version": 3, "names": ["Component", "ChatMessageComponent", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-message\\chat-message.component.ts"], "sourcesContent": ["// chat-message.component.ts\r\nimport { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-chat-message',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './chat-message.component.html',\r\n  styleUrl: './chat-message.component.scss'\r\n})\r\nexport class ChatMessageComponent {\r\n\r\n}\r\n"], "mappings": ";;;AAAA;AACA,SAASA,SAAS,QAAQ,eAAe;AASlC,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB,GAEhC;AAFYA,oBAAoB,GAAAC,UAAA,EAPhCF,SAAS,CAAC;EACTG,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,EAAE;EACXC,QAAA,EAAAC,oBAA4C;;CAE7C,CAAC,C,EACWN,oBAAoB,CAEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}