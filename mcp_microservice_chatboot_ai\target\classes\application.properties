# Role: Central configuration file for the Spring Boot application
# Purpose:
# Configures server port (8081)
# Sets up MCP server properties (name, version, type, capabilities)
# Configures API endpoints for communication with chat-mcp
# Sets up logging levels


spring.application.name=mcp_microservice_chatboot_ai

# Server configuration
server.port=8081

# OpenAI configuration
openai.api-key=********************************************************************************************************************************************************************
openai.model=gpt-4o
openai.temperature=0.7
openai.max-tokens=2000
openai.timeout=60

# Spring AI OpenAI configuration
spring.ai.openai.api-key=********************************************************************************************************************************************************************
spring.ai.openai.model=gpt-4o
spring.ai.openai.temperature=0.7
spring.ai.openai.max-tokens=2000

# Backend Configuration
# Options: CHAT_MCP, WIN_MCP
mcp.backend.type=WIN_MCP

# Chat-MCP API configuration
chat-mcp.api.url=http://localhost:8080/api
chat-mcp.api.auth-endpoint=/auth/login
chat-mcp.api.messages-endpoint=/messages
chat-mcp.api.conversations-endpoint=/conversations
chat-mcp.api.user-data-endpoint=/users/data

# Win-MCP API configuration
win-mcp.api.url=http://localhost:8082/api/winplus
win-mcp.api.auth-endpoint=/auth/login
win-mcp.api.user-data-endpoint=/user-data

# Logging
logging.level.com.example.mcp_microservice_chatboot_ai=DEBUG
logging.level.org.springframework.web=INFO

# Logging configuration
logging.level.com.theokanning.openai=DEBUG
