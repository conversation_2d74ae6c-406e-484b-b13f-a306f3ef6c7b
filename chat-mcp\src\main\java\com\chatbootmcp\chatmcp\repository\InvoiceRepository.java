package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Invoice;
import com.chatbootmcp.chatmcp.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    List<Invoice> findByUserOrderByInvoiceDateDesc(User user);
    List<Invoice> findByUserAndStatusOrderByInvoiceDateDesc(User user, String status);
    List<Invoice> findByUserAndPaymentStatusOrderByInvoiceDateDesc(User user, String paymentStatus);
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);

    @Query("SELECT i FROM Invoice i WHERE i.user = :user AND i.invoiceDate >= :startDate ORDER BY i.invoiceDate DESC")
    List<Invoice> findByUserAndInvoiceDateAfterOrderByInvoiceDateDesc(@Param("user") User user, @Param("startDate") LocalDate startDate);

    @Query("SELECT SUM(i.totalAmount) FROM Invoice i WHERE i.user = :user AND i.paymentStatus = 'PAID'")
    Double getTotalPaidAmountForUser(@Param("user") User user);

    @Query("SELECT SUM(i.totalAmount) FROM Invoice i WHERE i.user = :user AND i.paymentStatus = 'UNPAID'")
    Double getTotalUnpaidAmountForUser(@Param("user") User user);

    List<Invoice> findTop5ByUserOrderByInvoiceDateDesc(User user);

    @Query("SELECT i FROM Invoice i WHERE i.user = :user AND i.dueDate < :currentDate AND i.paymentStatus != 'PAID'")
    List<Invoice> findOverdueInvoicesForUser(@Param("user") User user, @Param("currentDate") LocalDate currentDate);
}
