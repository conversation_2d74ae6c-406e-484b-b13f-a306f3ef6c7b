package com.example.mcp_microservice_chatboot_ai;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Main application class for the MCP Microservice Chatboot AI.
 * This class is the entry point for the Spring Boot application.
 */

/**
 * Role: Main application class and entry point
    Purpose:
    Bootstraps the Spring Boot application
    Configures component scanning
    Logs application startup
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.example.mcp_microservice_chatboot_ai")
public class McpMicroserviceChatbootAiApplication {

    private static final Logger logger = LoggerFactory.getLogger(McpMicroserviceChatbootAiApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(McpMicroserviceChatbootAiApplication.class, args);
        logger.info("MCP Microservice Chatboot AI started successfully");
    }
}
