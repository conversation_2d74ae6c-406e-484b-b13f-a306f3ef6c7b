package com.example.mcp_microservice_chatboot_ai.config;

import com.example.mcp_microservice_chatboot_ai.model.Resource;
import com.example.mcp_microservice_chatboot_ai.service.ChatMcpApiService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Configuration class for MCP resources.
 * This class sets up the resource specifications for the MCP server.
 */

/**
 * Role: Configuration class for MCP resources
 *        Purpose:
 *        Sets up resource specifications for the MCP server
 *        Creates system info and help resources
 *        Provides information about the system to clients
 */
@Configuration
public class McpResourceConfig {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ChatMcpApiService chatMcpApiService;

    /**
     * Creates a list of documents for the system.
     *
     * @return A list of documents
     */
    @Bean
    public List<Resource> systemResources() {
        List<Resource> resources = new ArrayList<>();

        // System info resource
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("name", "MCP Chatboot AI Server");
        systemInfo.put("version", "1.0.0");
        systemInfo.put("status", "running");
        systemInfo.put("uptime", "1h 23m");

        Resource systemInfoResource = Resource.builder()
                .id("system-info")
                .title("System Information")
                .type("system")
                .content(objectMapper.valueToTree(systemInfo).toString())
                .build();
        resources.add(systemInfoResource);

        // Help resource
        String helpContent = """
                # MCP Chatboot AI Server Help

                This server provides AI chat capabilities and integrates with chat-mcp APIs.

                ## Available Tools

                - getWeather: Get weather information for a location
                - getUserInfo: Get information about a user

                ## Available Resources

                - system-info: Provides information about the system
                - help: Provides help information about the server in English
                - aide: Provides help information about the server in French
                - user-info: Provides user information from the MCP database
                """;

        Resource helpResource = Resource.builder()
                .id("help")
                .title("Help Information")
                .type("help")
                .content(helpContent)
                .build();
        resources.add(helpResource);

        // French help resource
        String frenchHelpContent = """
                # Aide du Serveur MCP Chatboot AI

                Ce serveur fournit des capacités de chat IA et s'intègre avec les API chat-mcp.

                ## Outils Disponibles

                - getWeather: Obtenir des informations météo pour un lieu
                - getUserInfo: Obtenir des informations sur un utilisateur

                ## Ressources Disponibles

                - system-info: Fournit des informations sur le système
                - help: Fournit des informations d'aide sur le serveur en anglais
                - aide: Fournit des informations d'aide sur le serveur en français
                - user-info: Fournit des informations sur l'utilisateur depuis la base de données MCP
                """;

        Resource frenchHelpResource = Resource.builder()
                .id("aide")
                .title("Aide en Français")
                .type("help")
                .language("fr")
                .content(frenchHelpContent)
                .build();
        resources.add(frenchHelpResource);

        return resources;
    }
}
