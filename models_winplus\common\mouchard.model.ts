import { TypeTransaction } from "../../enums/statistiques/TypeTransaction.enum"
import { Operateur } from "./operateur.model"



export class Mouchard {

    dateCreation: string
    entiteExt1: string
    entiteExt2: string
    entiteExt3: string
    entiteExt4: string
    id: number
    identifiantEntite: string
    identifiantTransaction: string
    newValue: string
    oldValue: string
    typeChangement: string
    typeEntite: string
    typeTransaction: TypeTransaction
    user: Operateur
}