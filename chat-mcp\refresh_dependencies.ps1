# PowerShell script to refresh Maven dependencies

Write-Host "Cleaning Maven repository cache..."
if (Test-Path "$env:USERPROFILE\.m2\repository\org\projectlombok") {
    Remove-Item -Path "$env:USERPROFILE\.m2\repository\org\projectlombok" -Recurse -Force
    Write-Host "Lombok cache cleared."
}

if (Test-Path "$env:USERPROFILE\.m2\repository\jakarta\validation") {
    Remove-Item -Path "$env:USERPROFILE\.m2\repository\jakarta\validation" -Recurse -Force
    Write-Host "Jakarta Validation cache cleared."
}

Write-Host "Downloading dependencies..."
try {
    # Try with mvn
    mvn dependency:purge-local-repository -DreResolve=false
    mvn clean install -DskipTests
} catch {
    Write-Host "Maven command failed. Trying with alternative methods..."
    
    # Try with Maven wrapper if available
    if (Test-Path ".\mvnw") {
        .\mvnw dependency:purge-local-repository -DreResolve=false
        .\mvnw clean install -DskipTests
    } else {
        Write-Host "Maven wrapper not found. Please manually refresh dependencies in your IDE."
    }
}

Write-Host "Dependencies refreshed. Please refresh your IDE now."
