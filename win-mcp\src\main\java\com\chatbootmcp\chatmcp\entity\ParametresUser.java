package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * ParametresUser entity representing user parameters and settings
 */
@Entity
@Table(name = "parametres_user")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParametresUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "operateur_id")
    private Operateur operateur;

    @Column(name = "plafond_user", precision = 15, scale = 2)
    private BigDecimal plafondUser;

    @Column(name = "type_control_horaire_login", length = 50)
    private String typeControlHoraireLogin;

    @Column(name = "type_control_lieu_login", length = 50)
    private String typeControlLieuLogin;

    @Column(name = "logout_after_each_vente")
    private Boolean logoutAfterEachVente;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;
}
