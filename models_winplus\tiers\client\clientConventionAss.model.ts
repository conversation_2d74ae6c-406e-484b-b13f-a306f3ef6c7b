
import { ConventionAssurance } from 'src/app/winpharm/models/assurance/conventionAssurance.model';


export class ClientConventionAss { 
    audited?: boolean;
    convention?: ConventionAssurance;
    estActif?: boolean;     // TODO:  this is an error (true field in the backend = actif)    but this field is not used  apparently
    id?: number;
    numAffiliation?: string;
    numImmatriculation?: string;
    primaire?: boolean;
    userModifiable?: boolean;
}
