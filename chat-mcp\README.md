# Chat MCP Backend

This is a Spring Boot MCP (Microservice Control Plane) backend for a chat application with mock statistical data.

## Package Structure

The application is organized in the following package structure:

```
com.chatbootmcp.chatmcp
├── config
│   ├── CorsConfig.java
│   ├── DataInitializer.java
│   ├── SecurityConfig.java
│   └── WebSocketConfig.java
├── controller
│   ├── AuthController.java
│   ├── ConversationController.java
│   ├── MessageController.java
│   └── UserDataController.java
├── dto
│   ├── request
│   │   ├── ConversationRequest.java
│   │   ├── LoginRequest.java
│   │   └── MessageRequest.java
│   └── response
│       ├── AuthResponse.java
│       ├── ConversationResponse.java
│       ├── MessageResponse.java
│       └── UserDataResponse.java
├── entity
│   ├── Conversation.java
│   ├── Message.java
│   ├── User.java
│   └── UserData.java
├── exception
│   ├── GlobalExceptionHandler.java
│   ├── ResourceNotFoundException.java
│   └── UnauthorizedException.java
├── repository
│   ├── ConversationRepository.java
│   ├── MessageRepository.java
│   ├── UserDataRepository.java
│   └── UserRepository.java
├── security
│   └── JwtAuthenticationFilter.java
├── service
│   ├── AIService.java
│   ├── AuthService.java
│   ├── ConversationService.java
│   ├── MessageService.java
│   ├── UserDataService.java
│   └── UserDetailsServiceImpl.java
├── util
│   ├── JwtUtil.java
│   └── MockDataGenerator.java
└── ChatMcpApplication.java
```

## How to Run

1. Make sure you have Java 17 or higher installed
2. Build the project using Maven (or the Maven wrapper if you don't have Maven installed):
   ```
   mvn clean package
   ```
   or
   ```
   ./mvnw clean package    # For Linux/Mac
   mvnw.cmd clean package  # For Windows
   ```
3. Run the application:
   ```
   java -jar target/chat-mcp-0.0.1-SNAPSHOT.jar
   ```

The application will start on port 8080 with the context path `/api`.

## Testing the API

### Authentication

```
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "username": "user1",
  "password": "password"
}
```

### Create a Conversation

```
POST http://localhost:8080/api/conversations
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "title": "My First Conversation"
}
```

### Send a Message

```
POST http://localhost:8080/api/messages
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "conversationId": 1,
  "content": "Tell me about my account statistics"
}
```

### Get User Data

```
GET http://localhost:8080/api/users/data
Authorization: Bearer YOUR_JWT_TOKEN
```

## WebSocket Testing

Open the WebSocket test client in your browser:
```
http://localhost:8080/api/websocket-test.html
```
