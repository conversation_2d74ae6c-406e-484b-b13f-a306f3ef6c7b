package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "entete_ventes")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnteteVente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "audited")
    private Boolean audited;

    @Column(name = "autres_termes")
    private String autresTermes;

    @Column(name = "date_vente")
    private LocalDateTime dateVente;

    @Column(name = "heure_vente_debut")
    private LocalTime heureVenteDebut;

    @Column(name = "heure_vente_fin")
    private LocalTime heureVenteFin;

    @Column(name = "maladie_chronique")
    private Boolean maladieChronique;

    @Column(name = "mnt_brut_ht", precision = 19, scale = 2)
    private BigDecimal mntBrutHt;

    @Column(name = "mnt_brut_ttc", precision = 19, scale = 2)
    private BigDecimal mntBrutTtc;

    @Column(name = "mnt_encaisse", precision = 19, scale = 2)
    private BigDecimal mntEncaisse;

    @Column(name = "mnt_marge_eff_ht", precision = 19, scale = 2)
    private BigDecimal mntMargeEffHt;

    @Column(name = "mnt_marge_eff_ttc", precision = 19, scale = 2)
    private BigDecimal mntMargeEffTtc;

    @Column(name = "mnt_marge_std_ht", precision = 19, scale = 2)
    private BigDecimal mntMargeStdHt;

    @Column(name = "mnt_marge_std_ttc", precision = 19, scale = 2)
    private BigDecimal mntMargeStdTtc;

    @Column(name = "mnt_net_ht", precision = 19, scale = 2)
    private BigDecimal mntNetHt;

    @Column(name = "mnt_net_ttc", precision = 19, scale = 2)
    private BigDecimal mntNetTtc;

    @Column(name = "mnt_remise_ht", precision = 19, scale = 2)
    private BigDecimal mntRemiseHt;

    @Column(name = "mnt_remise_ttc", precision = 19, scale = 2)
    private BigDecimal mntRemiseTtc;

    @Column(name = "mnt_taxe_parafiscale", precision = 19, scale = 2)
    private BigDecimal mntTaxeParafiscale;

    @Column(name = "mnt_tva", precision = 19, scale = 2)
    private BigDecimal mntTva;

    @Column(name = "mnt_vente_du", precision = 19, scale = 2)
    private BigDecimal mntVenteDu;

    @Column(name = "nbr_lignes")
    private Integer nbrLignes;

    @Column(name = "nbr_prd")
    private Integer nbrPrd;

    @Column(name = "nom_client")
    private String nomClient;

    @Column(name = "nom_patient")
    private String nomPatient;

    @Column(name = "num_blv")
    private Long numBlv;

    @Column(name = "num_facture")
    private Long numFacture;

    @Column(name = "num_vente")
    private Long numVente;

    @Column(name = "reste_a_ventiler", precision = 19, scale = 2)
    private BigDecimal resteAVentiler;

    @Column(name = "solde_client_avant_vente", precision = 19, scale = 2)
    private BigDecimal soldeClientAvantVente;

    @Column(name = "taux_remise", precision = 5, scale = 2)
    private BigDecimal tauxRemise;

    @Column(name = "total_qte", precision = 19, scale = 3)
    private BigDecimal totalQte;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "montant_recu", precision = 19, scale = 2)
    private BigDecimal montantRecu;

    @Column(name = "monnaie")
    private String monnaie;

    @Column(name = "monnaie_rendue", precision = 19, scale = 2)
    private BigDecimal monnaieRendue;

    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Additional fields from schema
    @Column(name = "statut", length = 50)
    private String statut;

    @Column(name = "sous_statut", length = 50)
    private String sousStatut;

    @Column(name = "statut_livre", length = 50)
    private String statutLivre;

    @Column(name = "type_vente", length = 50)
    private String typeVente;

    @Column(name = "type_operation", length = 50)
    private String typeOperation;

    @Column(name = "type_encaissement", length = 50)
    private String typeEncaissement;

    @Column(name = "type_avoir", length = 50)
    private String typeAvoir;

    @Column(name = "configuration_gestion_remise", length = 50)
    private String configurationGestionRemise;

    @Column(name = "type_remise_client", length = 50)
    private String typeRemiseClient;

    @Column(name = "status_enc", length = 50)
    private String statusEnc;

    @Column(name = "total_montant_reste_apayer", precision = 15, scale = 2)
    private BigDecimal totalMontantResteApayer;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Client client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id")
    private Beneficiaire patient;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "operateur_id")
    private Operateur operateur;

    @Column(name = "medecin_id")
    private Long medecinId;

    @Column(name = "mode_encaissement_id")
    private Long modeEncaissementId;

    @OneToMany(mappedBy = "enteteVente", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<DetailVente> details;
}
