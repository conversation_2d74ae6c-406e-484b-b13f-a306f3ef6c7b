package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.EnteteVente;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EnteteVenteRepository extends JpaRepository<EnteteVente, Long> {
    
    List<EnteteVente> findByClientId(Long clientId);
    
    Page<EnteteVente> findByClientId(Long clientId, Pageable pageable);
    
    @Query("SELECT v FROM EnteteVente v WHERE v.dateVente BETWEEN :startDate AND :endDate")
    List<EnteteVente> findVentesByDateRange(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT v FROM EnteteVente v WHERE v.dateVente BETWEEN :startDate AND :endDate")
    Page<EnteteVente> findVentesByDateRange(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate, 
                                           Pageable pageable);
    
    @Query("SELECT v FROM EnteteVente v WHERE v.client.id = :clientId AND v.dateVente BETWEEN :startDate AND :endDate")
    List<EnteteVente> findVentesByClientAndDateRange(@Param("clientId") Long clientId,
                                                     @Param("startDate") LocalDateTime startDate,
                                                     @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT SUM(v.mntNetTtc) FROM EnteteVente v WHERE v.dateVente BETWEEN :startDate AND :endDate")
    java.math.BigDecimal calculateTotalSalesByDateRange(@Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT v FROM EnteteVente v WHERE v.mntNetTtc >= :minAmount ORDER BY v.mntNetTtc DESC")
    List<EnteteVente> findHighValueSales(@Param("minAmount") java.math.BigDecimal minAmount);
    
    @Query("SELECT v FROM EnteteVente v ORDER BY v.dateVente DESC")
    Page<EnteteVente> findRecentSales(Pageable pageable);
}
