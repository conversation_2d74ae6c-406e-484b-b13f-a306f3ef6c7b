# 🤖 MCP System - Example Questions for Testing

This file contains all possible questions you can ask the MCP system to test the real database integration.

## 📋 How to Test

1. **Start both services:**
   - Chat-MCP Backend: `cd chat-mcp && .\mvnw.cmd spring-boot:run` (port 8080)
   - MCP Microservice: `cd mcp_microservice_chatboot_ai && .\mvnw.cmd spring-boot:run` (port 8081)

2. **Test via API:**
   ```bash
   # Ask a question
   curl -X POST http://localhost:8081/api/chat \
     -H "Content-Type: application/json" \
     -d '{"content": "Quel est mon nom?", "conversationId": "test123", "username": "user1"}'
   
   # Choose option 1 (MCP Database)
   curl -X POST http://localhost:8081/api/chat \
     -H "Content-Type: application/json" \
     -d '{"content": "1", "conversationId": "test123", "username": "user1"}'
   ```

3. **Always choose option 1** for database questions to get real data from H2.

---

## 👤 **PERSONAL INFORMATION QUESTIONS**

### Basic Identity
- `Quel est mon nom ?`
- `Quel est mon nom complet ?`
- `Comment je m'appelle ?`
- `Quelle est mon identité ?`
- `Qui suis-je ?`

### Contact Information
- `Quelle est mon adresse email ?`
- `Quel est mon email ?`
- `Quelle est mon adresse ?`
- `Où j'habite ?`
- `Quel est mon numéro de téléphone ?`
- `Comment me contacter ?`
- `Dans quelle ville j'habite ?`
- `Quel est mon code postal ?`
- `Dans quel pays je vis ?`

### Professional Information
- `Quel est mon métier ?`
- `Où je travaille ?`
- `Quelle est ma profession ?`
- `Dans quelle entreprise je travaille ?`
- `Quel est mon poste ?`
- `Quel est mon département ?`
- `Quel est mon ID employé ?`
- `Quelle est ma fonction ?`

---

## 💰 **FINANCIAL INFORMATION QUESTIONS**

### Account Balance
- `Quel est mon solde ?`
- `Quel est mon solde actuel ?`
- `Combien j'ai sur mon compte ?`
- `Quelle est ma balance ?`
- `Combien d'argent j'ai ?`

### Credit and Limits
- `Quelle est ma limite de crédit ?`
- `Quel est mon plafond ?`
- `Combien je peux dépenser ?`
- `Quelle est ma limite ?`

### Payment Information
- `Quelle est ma méthode de paiement ?`
- `Comment je paie ?`
- `Quels sont les derniers chiffres de ma carte ?`
- `Quand expire ma carte ?`
- `Quelle est la date d'expiration de ma carte ?`
- `Quand est ma prochaine échéance ?`
- `Quand dois-je payer ?`

---

## 📄 **SUBSCRIPTION INFORMATION QUESTIONS**

### Subscription Status
- `Quel est mon statut d'abonnement ?`
- `Mon abonnement est-il actif ?`
- `Suis-je abonné ?`
- `Quel type d'abonnement j'ai ?`

### Subscription Details
- `Quand a commencé mon abonnement ?`
- `Quand se termine mon abonnement ?`
- `Combien coûte mon abonnement ?`
- `Quel est le prix de mon abonnement ?`
- `Quand expire mon abonnement ?`

---

## 🧾 **TRANSACTION QUESTIONS**

### Recent Transactions
- `Quelles sont mes dernières transactions ?`
- `Mes transactions récentes ?`
- `Qu'est-ce que j'ai acheté récemment ?`
- `Mes derniers achats ?`
- `Historique de mes transactions ?`

### Transaction Summary
- `Combien j'ai dépensé ?`
- `Total de mes débits ?`
- `Total de mes crédits ?`
- `Quel est mon solde net ?`
- `Balance de mes transactions ?`

---

## 📋 **INVOICE QUESTIONS**

### Recent Invoices
- `Quelles sont mes factures ?`
- `Mes dernières factures ?`
- `Factures récentes ?`
- `Historique de mes factures ?`
- `Mes factures impayées ?`

### Invoice Summary
- `Combien je dois ?`
- `Total de mes factures payées ?`
- `Total de mes factures impayées ?`
- `Combien de factures en retard ?`
- `Mes factures en souffrance ?`

---

## 🛒 **ORDER QUESTIONS**

### Recent Orders
- `Quelles sont mes commandes ?`
- `Mes dernières commandes ?`
- `Historique de mes commandes ?`
- `Qu'est-ce que j'ai commandé ?`
- `Mes achats récents ?`

### Order Summary
- `Combien de commandes j'ai ?`
- `Total de mes commandes ?`
- `Valeur moyenne de mes commandes ?`
- `Combien j'ai dépensé en commandes ?`
- `Mes commandes actives ?`

---

## 📊 **ACCOUNT STATISTICS QUESTIONS**

### Account Information
- `Quel type de compte j'ai ?`
- `Quand j'ai créé mon compte ?`
- `Depuis quand je suis client ?`
- `Mon profil est-il complet ?`
- `Pourcentage de completion de mon profil ?`

### Preferences
- `Quelle est ma langue préférée ?`
- `Quel est mon fuseau horaire ?`
- `Mes préférences de notification ?`
- `Ma date de naissance ?`

---

## 🔍 **COMPLEX QUESTIONS**

### Summary Questions
- `Résumé de mon compte ?`
- `Toutes mes informations ?`
- `Mon profil complet ?`
- `Vue d'ensemble de mon compte ?`
- `Mes données personnelles ?`

### Comparative Questions
- `Combien j'ai gagné vs dépensé ?`
- `Plus de crédits ou de débits ?`
- `Factures payées vs impayées ?`
- `Commandes livrées vs en cours ?`

---

## 🎯 **TESTING TIPS**

1. **Always choose option 1** when prompted for data source
2. **Test with different conversation IDs** to simulate different sessions
3. **Try variations** of the same question to test AI understanding
4. **Mix French and English** questions to test language handling
5. **Test edge cases** like empty responses or error conditions

---

## 📝 **Sample Test Sequence**

```
1. "Bonjour" → Should get friendly response
2. "Quel est mon nom ?" → Should ask for source (1 or 2)
3. "1" → Should return "Test User1"
4. "Mon solde ?" → Should ask for source again
5. "1" → Should return "1250.0 €"
6. "Mes factures ?" → Should ask for source
7. "1" → Should return real invoice data
```

---

**🎉 All questions will return real data from the H2 database when you choose option 1!**
