# PowerShell script to fix Java files with BOM

$javaFiles = Get-ChildItem -Path "src" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    # Read the file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Check if the file starts with a BOM character
    if ($content.StartsWith([char]0xFEFF)) {
        Write-Host "Fixing BOM in: $($file.FullName)"
        
        # Remove the BOM character
        $content = $content.TrimStart([char]0xFEFF)
        
        # Write the content back to the file
        $content | Out-File -FilePath $file.FullName -Encoding utf8
        
        Write-Host "Fixed: $($file.FullName)"
    }
}

Write-Host "All files fixed!"
