package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * EnteteBlAchat entity representing purchase order headers
 */
@Entity
@Table(name = "entete_bl_achat")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnteteBlAchat {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "date_bla")
    private LocalDateTime dateBla;

    @Column(name = "date_creation")
    private LocalDateTime dateCreation;

    @Column(name = "date_facture")
    private LocalDateTime dateFacture;

    @Column(name = "mnt_brut_ht", precision = 15, scale = 2)
    private BigDecimal mntBrutHt;

    @Column(name = "mnt_brut_ttc", precision = 15, scale = 2)
    private BigDecimal mntBrutTtc;

    @Column(name = "mnt_net_ht", precision = 15, scale = 2)
    private BigDecimal mntNetHt;

    @Column(name = "mnt_net_ttc", precision = 15, scale = 2)
    private BigDecimal mntNetTtc;

    @Column(name = "mnt_remise_ht", precision = 15, scale = 2)
    private BigDecimal mntRemiseHt;

    @Column(name = "mnt_remise_ttc", precision = 15, scale = 2)
    private BigDecimal mntRemiseTtc;

    @Column(name = "mnt_tva", precision = 15, scale = 2)
    private BigDecimal mntTva;

    @Column(name = "mnt_achat_std", precision = 15, scale = 2)
    private BigDecimal mntAchatStd;

    @Column(name = "mnt_vente_std", precision = 15, scale = 2)
    private BigDecimal mntVenteStd;

    @Column(name = "nbr_ligne")
    private Integer nbrLigne;

    @Column(name = "flag_accepte_ecart")
    private Boolean flagAccepteEcart;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "depot_id")
    private Depot depot;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fournisseur_id")
    private Fournisseur fournisseur;

    @Column(name = "devise_id")
    private Long deviseId;

    @Column(name = "entete_cmd_achat_id")
    private Long enteteCmdAchatId;
}
