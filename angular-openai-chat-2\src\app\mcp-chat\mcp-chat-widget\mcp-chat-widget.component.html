<!-- mcp-chat-widget.component.html -->
<div class="chat-widget app-mcp-chat-widget-root" [class.expanded]="isExpanded">
  <!-- Chat button (collapsed state) -->
  <button
    *ngIf="!isExpanded"
    class="chat-button"
    (click)="toggleChat()">
    <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="512" height="512" viewBox="0 0 32 32"><path fill="#276578" d="m13.294 7.436.803 2.23a8.84 8.84 0 0 0 5.316 5.316l2.23.803a.229.229 0 0 1 0 .43l-2.23.803a8.84 8.84 0 0 0-5.316 5.316l-.803 2.23a.229.229 0 0 1-.43 0l-.803-2.23a8.84 8.84 0 0 0-5.316-5.316l-2.23-.803a.229.229 0 0 1 0-.43l2.23-.803a8.84 8.84 0 0 0 5.316-5.316l.803-2.23a.228.228 0 0 1 .43 0m10.038-5.359.407 1.129a4.48 4.48 0 0 0 2.692 2.692l1.129.407a.116.116 0 0 1 0 .218l-1.129.407a4.48 4.48 0 0 0-2.692 2.692l-.407 1.129a.116.116 0 0 1-.218 0l-.407-1.129a4.48 4.48 0 0 0-2.692-2.692l-1.129-.407a.116.116 0 0 1 0-.218l1.129-.407a4.48 4.48 0 0 0 2.692-2.692l.407-1.129a.116.116 0 0 1 .218 0m0 19.173.407 1.129a4.48 4.48 0 0 0 2.692 2.692l1.129.407a.116.116 0 0 1 0 .218l-1.129.407a4.48 4.48 0 0 0-2.692 2.692l-.407 1.129a.116.116 0 0 1-.218 0l-.407-1.129a4.48 4.48 0 0 0-2.692-2.692l-1.129-.407a.116.116 0 0 1 0-.218l1.129-.407a4.48 4.48 0 0 0 2.692-2.692l.407-1.129c.037-.102.182-.102.218 0" data-original="#000000"/></svg>
    <span class="chat-button-text">MCP Assistant</span>
  </button>

  <!-- Chat container (expanded state) -->
  <div *ngIf="isExpanded" class="chat-container">
    <!-- Header -->
    <div class="chat-header">
      <!-- Back button when in conversation view -->
      <button
        *ngIf="activeTab === 'chat' && showConversation"
        class="back-button"
        (click)="showConversationList()">
        <i class="mdi mdi-arrow-left"></i>
      </button>

      <!-- Title -->
      <div class="header-title">
        <span *ngIf="activeTab === 'home'">Accueil</span>
        <span *ngIf="activeTab === 'chat' && !showConversation">Conversations</span>
        <span *ngIf="activeTab === 'chat' && showConversation">
          {{ getCurrentConversationTitle() }}
        </span>
        <span *ngIf="activeTab === 'help'">Centre d'aide</span>
        <span *ngIf="activeTab === 'login'">Connexion MCP</span>
      </div>

      <!-- Tabs and close button -->
      <div class="header-actions">
        <div class="header-tabs">
          <div
            class="tab"
            [class.active]="activeTab === 'home'"
            (click)="setActiveTab('home')">
            <i class="mdi mdi-home"></i>
          </div>
          <div
            class="tab"
            [class.active]="activeTab === 'chat'"
            (click)="setActiveTab('chat')">
            <i class="mdi mdi-message-text"></i>
          </div>
          <div
            class="tab"
            [class.active]="activeTab === 'help'"
            (click)="setActiveTab('help')">
            <i class="mdi mdi-information"></i>
          </div>
        </div>
        <button class="close-button" (click)="toggleChat()">
          <i class="mdi mdi-close"></i>
        </button>
      </div>
    </div>

    <!-- Content -->
    <div class="chat-content">
      <!-- Login tab -->
      <div *ngIf="activeTab === 'login'" class="login-tab">
        <app-mcp-chat-login (loginSuccess)="handleLoginSuccess()"></app-mcp-chat-login>
      </div>

      <!-- Home tab -->
      <div *ngIf="activeTab === 'home'" class="home-tab">
        <app-mcp-chat-home
          (askQuestion)="handleAskQuestion($event)"
          (selectArticle)="handleSelectArticle($event)"
          (navigateToHelpTab)="setActiveTab('help')">
        </app-mcp-chat-home>
      </div>

      <!-- Chat tab -->
      <div *ngIf="activeTab === 'chat'" class="chat-tab">
        <!-- Conversation list -->
        <div *ngIf="!showConversation" class="conversation-list-container">
          <button class="new-chat-btn" (click)="startNewConversation()">
            <i class="mdi mdi-plus new-icon"></i> Nouvelle Conversation
          </button>

          <div class="conversation-list">
            <div
              *ngFor="let conv of conversations"
              class="conversation-item"
              [class.active]="currentConversationId === conv.id"
              (click)="loadAndShowConversation(conv.id!)">
              <div class="conv-info">
                <span class="conv-title">{{ conv.title }}</span>
                <span class="conv-date">{{ formatDate(conv.lastUpdated) }}</span>
              </div>
              <button class="delete-btn" (click)="deleteConversation(conv.id!, $event)">
                <i class="mdi mdi-trash"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Conversation view -->
        <div *ngIf="showConversation" class="conversation-view">
          <app-mcp-chat-conversation
            [messages]="currentMessages"
            [loading]="isLoading"
            (sendMessage)="sendMessage($event)">
          </app-mcp-chat-conversation>
        </div>
      </div>

      <!-- Help tab -->
      <div *ngIf="activeTab === 'help'" class="help-tab">
        <app-mcp-chat-help></app-mcp-chat-help>
      </div>
    </div>

    <!-- Footer navigation -->
    <div class="chat-footer">
      <div class="footer-nav">
        <div class="nav-item" [class.active]="activeTab === 'home'" (click)="setActiveTab('home')">
          <i class="mdi mdi-home"></i>
          <span>Accueil</span>
        </div>
        <div class="nav-item" [class.active]="activeTab === 'chat'" (click)="setActiveTab('chat')">
          <i class="mdi mdi-message-text"></i>
          <span>Messages</span>
        </div>
        <div class="nav-item" [class.active]="activeTab === 'help'" (click)="setActiveTab('help')">
          <i class="mdi mdi-information"></i>
          <span>Aide</span>
        </div>
      </div>
    </div>

    <!-- Error message -->
    <div *ngIf="error" class="error-message">
      {{ error }}
      <button class="close-error" (click)="error = null">×</button>
    </div>
  </div>
</div>
