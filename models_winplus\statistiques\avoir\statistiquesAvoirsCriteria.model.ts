import { TypeRechercheAvoir } from 'src/app/winpharm/enums/statistiques/TypeRechercheAvoir.enum';
import { CauseDemandeAvoir } from 'src/app/winpharm/enums/achat-avoir/CauseDemandeAvoir.enum';
import { Fournisseur } from '../../tiers/fournisseur/fournisseur.model';

// import { Moment } from 'moment';



export class StatistiquesAvoirsCriteria { 
    cause?: CauseDemandeAvoir;
    dateDebut?: any;
    dateFin?: any;
    fournisseurId?: number;
    numero?: number;
    numeroDemande?: number;
    typeRecherche?: TypeRechercheAvoir;
    fournisseur?:Fournisseur
}

