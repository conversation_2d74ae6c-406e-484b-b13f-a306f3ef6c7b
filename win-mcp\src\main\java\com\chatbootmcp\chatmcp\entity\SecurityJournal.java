package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * SecurityJournal entity representing security audit logs
 */
@Entity
@Table(name = "security_journal")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityJournal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "log_date")
    private LocalDateTime logDate;

    @Column(name = "src_ip", length = 50)
    private String srcIp;

    @Column(name = "type", length = 50)
    private String type;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private Operateur user;

    @Column(name = "tenant_principal_id")
    private Long tenantPrincipalId;
}
