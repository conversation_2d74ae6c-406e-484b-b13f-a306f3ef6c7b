package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.entity.Message;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;

@Service
public class AIService {

    private final String[] MOCK_RESPONSES = {
        "I understand your question. Based on your profile data, I can see you've been with us for {duration} months.",
        "According to your account statistics, you've completed {completedTasks} tasks this month.",
        "Your current performance metrics show {performanceRate}% improvement compared to last month.",
        "Based on your usage patterns, I'd recommend focusing on {recommendedArea} for better results.",
        "Your team's collaboration score is {collaborationScore}/10, which is {comparisonResult} than the average.",
        "Looking at your recent activities, I notice you've been particularly active in {activeArea}.",
        "Your account shows {pendingItems} pending items that require attention.",
        "Based on your historical data, your peak productivity time appears to be around {peakTime}.",
        "Your current subscription plan utilization is at {utilizationRate}%. Would you like recommendations to optimize this?",
        "I see from your profile that you're interested in {interests}. Would you like more information about this?"
    };
    
    private final Random random = new Random();

    public String generateResponse(String userMessage, List<Message> conversationHistory) {
        // In a real implementation, you would:
        // 1. Process the conversation history to build context
        // 2. Call an external AI service or use a local model
        // 3. Process the response
        
        // For this mock implementation, we'll return a template response with random data
        String templateResponse = MOCK_RESPONSES[random.nextInt(MOCK_RESPONSES.length)];
        
        // Replace placeholders with mock data
        return templateResponse
                .replace("{duration}", String.valueOf(random.nextInt(24) + 1))
                .replace("{completedTasks}", String.valueOf(random.nextInt(50) + 5))
                .replace("{performanceRate}", String.valueOf(random.nextInt(30) + 70))
                .replace("{recommendedArea}", getRandomArea())
                .replace("{collaborationScore}", String.valueOf(random.nextInt(5) + 5))
                .replace("{comparisonResult}", random.nextBoolean() ? "higher" : "lower")
                .replace("{activeArea}", getRandomArea())
                .replace("{pendingItems}", String.valueOf(random.nextInt(10)))
                .replace("{peakTime}", getRandomTime())
                .replace("{utilizationRate}", String.valueOf(random.nextInt(60) + 40))
                .replace("{interests}", getRandomInterests());
    }
    
    private String getRandomArea() {
        String[] areas = {"project management", "analytics", "communication", "documentation", "development", "design"};
        return areas[random.nextInt(areas.length)];
    }
    
    private String getRandomTime() {
        int hour = random.nextInt(12) + 8; // 8 AM to 8 PM
        return hour + (hour >= 12 ? " PM" : " AM");
    }
    
    private String getRandomInterests() {
        String[] interests = {"data visualization", "automation", "team collaboration", "AI integration", "productivity"};
        return interests[random.nextInt(interests.length)];
    }
}
