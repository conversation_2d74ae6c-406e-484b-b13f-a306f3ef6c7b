import { SousStatutEchange } from 'src/app/winpharm/enums/echange/SousStatutEchange.enum';
import { TypePrixEchange } from 'src/app/winpharm/enums/echange/TypePrixEchange.enum';
import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { SensEchange } from 'src/app/winpharm/enums/echange/SensEchange.enum';

// import { Moment } from 'moment';

import { Confrere } from 'src/app/winpharm/models/tiers/confrere/confrere.model';


export class EchangeCriteria { 
    codeProduit?: string;
    confrere?: Confrere;
    dateEchangeMax?: any;
    dateEchangeMin?: any;
    datePeremption?: string;
    designationProduit?: string;
    mntLigne?: number;
    numEchange?: string;
    numLot?: string;
    prixUnit?: number;
    qt?: number;
    qtUg?: number;
    raisonSociale?: string;
    resteSolde?: number;
    sensEchange?: SensEchange;
    sousStatutList?: SousStatutEchange[];
    statut?: Statut;
    tauxRemise?: number;
    typePrix?: TypePrixEchange;
}

