package com.example.mcp_microservice_chatboot_ai.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for authentication requests.
 */

/**
 * Role: Data Transfer Object for authentication requests
    Purpose:
    Carries username and password for authentication
    Used when communicating with the chat-mcp authentication API
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthRequest {
    
    /**
     * The username for authentication.
     */
    private String username;
    
    /**
     * The password for authentication.
     */
    private String password;
}
