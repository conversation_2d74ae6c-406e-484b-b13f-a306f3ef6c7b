package com.chatbootmcp.chatmcp.config;

import com.chatbootmcp.chatmcp.entity.*;
import com.chatbootmcp.chatmcp.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class DataInitializer {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProduitRepository produitRepository;

    @Autowired
    private EnteteVenteRepository enteteVenteRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Bean
    public CommandLineRunner initData() {
        return args -> {
            System.out.println("DataInitializer: Starting WinPlus data initialization...");

            // Create test users if they don't exist
            long userCount = userRepository.count();
            System.out.println("DataInitializer: Current user count: " + userCount);

            if (userCount == 0) {
                System.out.println("DataInitializer: Creating test users...");
                createTestUser("user1", "<EMAIL>", "password");
                createTestUser("user2", "<EMAIL>", "password");
                createTestUser("admin", "<EMAIL>", "admin", true);
                createTestUser("testuser", "<EMAIL>", "password");

                System.out.println("DataInitializer: Test users created successfully!");
            } else {
                System.out.println("DataInitializer: Users already exist, skipping initialization.");
            }

            // Initialize WinPlus sample data
            initializeWinPlusData();
        };
    }

    private void createTestUser(String username, String email, String password) {
        createTestUser(username, email, password, false);
    }

    private void createTestUser(String username, String email, String password, boolean isAdmin) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(password));
        user.setFullName("Test " + username.substring(0, 1).toUpperCase() + username.substring(1));
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        Set<String> roles = new HashSet<>();
        roles.add("USER");
        if (isAdmin) {
            roles.add("ADMIN");
        }
        user.setRoles(roles);

        userRepository.save(user);
    }

    private void initializeWinPlusData() {
        System.out.println("DataInitializer: Initializing WinPlus sample data...");

        // Create sample clients
        if (clientRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample clients...");
            createSampleClients();
        }

        // Create sample products
        if (produitRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample products...");
            createSampleProducts();
        }

        // Create sample sales
        if (enteteVenteRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample sales...");
            createSampleSales();
        }

        System.out.println("DataInitializer: WinPlus sample data initialization completed!");
    }

    private void createSampleClients() {
        // Client 1
        Client client1 = new Client();
        client1.setCodeClient("CLI001");
        client1.setNom("Dupont");
        client1.setPrenom("Jean");
        client1.setEmail("<EMAIL>");
        client1.setGsm("0612345678");
        client1.setAdr1("123 Rue de la Paix");
        client1.setAdr2("Apt 4B");
        client1.setEstActif(true);
        client1.setSoldeClient(new BigDecimal("1500.00"));
        client1.setPlafondCredit(new BigDecimal("5000.00"));
        client1.setCaClient(new BigDecimal("12500.00"));
        client1.setTauxRemise(new BigDecimal("5.0"));
        clientRepository.save(client1);

        // Client 2
        Client client2 = new Client();
        client2.setCodeClient("CLI002");
        client2.setNom("Martin");
        client2.setPrenom("Marie");
        client2.setEmail("<EMAIL>");
        client2.setGsm("0623456789");
        client2.setAdr1("456 Avenue des Champs");
        client2.setEstActif(true);
        client2.setSoldeClient(new BigDecimal("-250.00"));
        client2.setPlafondCredit(new BigDecimal("3000.00"));
        client2.setCaClient(new BigDecimal("8750.00"));
        client2.setTauxRemise(new BigDecimal("3.0"));
        clientRepository.save(client2);

        // Client 3 - testuser
        Client client3 = new Client();
        client3.setCodeClient("testuser");
        client3.setNom("Benali");
        client3.setPrenom("Ahmed");
        client3.setEmail("<EMAIL>");
        client3.setGsm("0634567890");
        client3.setAdr1("789 Boulevard Mohammed V");
        client3.setEstActif(true);
        client3.setSoldeClient(new BigDecimal("750.00"));
        client3.setPlafondCredit(new BigDecimal("4000.00"));
        client3.setCaClient(new BigDecimal("15200.00"));
        client3.setTauxRemise(new BigDecimal("7.5"));
        clientRepository.save(client3);
    }

    private void createSampleProducts() {
        // Product 1 - Paracetamol
        Produit produit1 = new Produit();
        produit1.setCodePrd("MED001");
        produit1.setDesignation("Paracétamol 500mg");
        produit1.setDosage("500mg");
        produit1.setPresentation("Boîte de 20 comprimés");
        produit1.setCodeBarre("3401234567890");
        produit1.setPrixVenteStd(new BigDecimal("25.50"));
        produit1.setPrixAchatStd(new BigDecimal("18.00"));
        produit1.setTotalStock(new BigDecimal("150"));
        produit1.setEstVendable(true);
        produit1.setEstStockable(true);
        produit1.setEstOblgPrescription(false);
        produit1.setEstPsychotrope(false);
        produit1.setTauxRemb(new BigDecimal("70.0"));
        produitRepository.save(produit1);

        // Product 2 - Antibiotique
        Produit produit2 = new Produit();
        produit2.setCodePrd("MED002");
        produit2.setDesignation("Amoxicilline 1g");
        produit2.setDosage("1g");
        produit2.setPresentation("Boîte de 12 gélules");
        produit2.setCodeBarre("3401234567891");
        produit2.setPrixVenteStd(new BigDecimal("45.80"));
        produit2.setPrixAchatStd(new BigDecimal("32.50"));
        produit2.setTotalStock(new BigDecimal("75"));
        produit2.setEstVendable(true);
        produit2.setEstStockable(true);
        produit2.setEstOblgPrescription(true);
        produit2.setEstPsychotrope(false);
        produit2.setTauxRemb(new BigDecimal("100.0"));
        produitRepository.save(produit2);

        // Product 3 - Vitamines
        Produit produit3 = new Produit();
        produit3.setCodePrd("VIT001");
        produit3.setDesignation("Vitamine C 1000mg");
        produit3.setDosage("1000mg");
        produit3.setPresentation("Boîte de 30 comprimés effervescents");
        produit3.setCodeBarre("3401234567892");
        produit3.setPrixVenteStd(new BigDecimal("35.20"));
        produit3.setPrixAchatStd(new BigDecimal("24.80"));
        produit3.setTotalStock(new BigDecimal("200"));
        produit3.setEstVendable(true);
        produit3.setEstStockable(true);
        produit3.setEstOblgPrescription(false);
        produit3.setEstPsychotrope(false);
        produit3.setTauxRemb(new BigDecimal("0.0"));
        produitRepository.save(produit3);
    }

    private void createSampleSales() {
        // Get sample data
        Client client1 = clientRepository.findByCodeClient("CLI001").orElse(null);
        Client client2 = clientRepository.findByCodeClient("CLI002").orElse(null);
        Client testClient = clientRepository.findByCodeClient("testuser").orElse(null);

        if (client1 != null) {
            // Sale 1
            EnteteVente vente1 = new EnteteVente();
            vente1.setClient(client1);
            vente1.setDateVente(LocalDateTime.now().minusDays(5));
            vente1.setNumVente(1001L);
            vente1.setMntBrutTtc(new BigDecimal("51.00"));
            vente1.setMntNetTtc(new BigDecimal("48.45"));
            vente1.setMntRemiseTtc(new BigDecimal("2.55"));
            vente1.setTauxRemise(new BigDecimal("5.0"));
            vente1.setTotalQte(new BigDecimal("2"));
            vente1.setNbrLignes(1);
            vente1.setNbrPrd(1);
            vente1.setMntEncaisse(new BigDecimal("48.45"));
            enteteVenteRepository.save(vente1);
        }

        if (client2 != null) {
            // Sale 2
            EnteteVente vente2 = new EnteteVente();
            vente2.setClient(client2);
            vente2.setDateVente(LocalDateTime.now().minusDays(2));
            vente2.setNumVente(1002L);
            vente2.setMntBrutTtc(new BigDecimal("45.80"));
            vente2.setMntNetTtc(new BigDecimal("44.42"));
            vente2.setMntRemiseTtc(new BigDecimal("1.38"));
            vente2.setTauxRemise(new BigDecimal("3.0"));
            vente2.setTotalQte(new BigDecimal("1"));
            vente2.setNbrLignes(1);
            vente2.setNbrPrd(1);
            vente2.setMntEncaisse(new BigDecimal("44.42"));
            enteteVenteRepository.save(vente2);
        }

        if (testClient != null) {
            // Sale 3 for testuser
            EnteteVente vente3 = new EnteteVente();
            vente3.setClient(testClient);
            vente3.setDateVente(LocalDateTime.now().minusDays(1));
            vente3.setNumVente(1003L);
            vente3.setMntBrutTtc(new BigDecimal("105.60"));
            vente3.setMntNetTtc(new BigDecimal("97.68"));
            vente3.setMntRemiseTtc(new BigDecimal("7.92"));
            vente3.setTauxRemise(new BigDecimal("7.5"));
            vente3.setTotalQte(new BigDecimal("3"));
            vente3.setNbrLignes(2);
            vente3.setNbrPrd(2);
            vente3.setMntEncaisse(new BigDecimal("97.68"));
            enteteVenteRepository.save(vente3);
        }
    }
}


