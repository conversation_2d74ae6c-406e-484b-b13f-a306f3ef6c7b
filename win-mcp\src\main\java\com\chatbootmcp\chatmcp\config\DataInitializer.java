package com.chatbootmcp.chatmcp.config;

import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class DataInitializer {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Bean
    public CommandLineRunner initData() {
        return args -> {
            System.out.println("DataInitializer: Starting user initialization...");

            // Create test users if they don't exist
            long userCount = userRepository.count();
            System.out.println("DataInitializer: Current user count: " + userCount);

            if (userCount == 0) {
                System.out.println("DataInitializer: Creating test users...");
                createTestUser("user1", "<EMAIL>", "password");
                createTestUser("user2", "<EMAIL>", "password");
                createTestUser("admin", "<EMAIL>", "admin", true);

                System.out.println("DataInitializer: Test users created successfully!");
            } else {
                System.out.println("DataInitializer: Users already exist, skipping initialization.");
            }
        };
    }

    private void createTestUser(String username, String email, String password) {
        createTestUser(username, email, password, false);
    }

    private void createTestUser(String username, String email, String password, boolean isAdmin) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(password));
        user.setFullName("Test " + username.substring(0, 1).toUpperCase() + username.substring(1));
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        Set<String> roles = new HashSet<>();
        roles.add("USER");
        if (isAdmin) {
            roles.add("ADMIN");
        }
        user.setRoles(roles);

        userRepository.save(user);
    }
}


