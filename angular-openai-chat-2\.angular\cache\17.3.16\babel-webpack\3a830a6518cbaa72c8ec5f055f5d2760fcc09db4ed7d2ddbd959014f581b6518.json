{"ast": null, "code": "/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_INITIALIZER, createPlatformFactory, platformCore, APP_ID, provideZoneChangeDetection, NgModule } from '@angular/core';\nimport { ɵBrowserDomAdapter, BrowserModule } from '@angular/platform-browser';\nfunction initBrowserTests() {\n  ɵBrowserDomAdapter.makeCurrent();\n}\nconst _TEST_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_INITIALIZER,\n  useValue: initBrowserTests,\n  multi: true\n}];\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n  static {\n    this.ɵfac = function BrowserTestingModule_Factory(t) {\n      return new (t || BrowserTestingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserTestingModule,\n      exports: [BrowserModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, provideZoneChangeDetection(), {\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }],\n      imports: [BrowserModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, provideZoneChangeDetection(), {\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserTestingModule, platformBrowserTesting };", "map": {"version": 3, "names": ["PlatformLocation", "MockPlatformLocation", "i0", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "APP_ID", "provideZoneChangeDetection", "NgModule", "ɵBrowserDomAdapter", "BrowserModule", "initBrowserTests", "makeCurrent", "_TEST_BROWSER_PLATFORM_PROVIDERS", "provide", "useValue", "multi", "platformBrowserTesting", "BrowserTestingModule", "ɵfac", "BrowserTestingModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "exports", "ɵinj", "ɵɵdefineInjector", "providers", "useClass", "imports", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/Downloads/Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/@angular/platform-browser/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_INITIALIZER, createPlatformFactory, platformCore, APP_ID, provideZoneChangeDetection, NgModule } from '@angular/core';\nimport { ɵBrowserDomAdapter, BrowserModule } from '@angular/platform-browser';\n\nfunction initBrowserTests() {\n    ɵBrowserDomAdapter.makeCurrent();\n}\nconst _TEST_BROWSER_PLATFORM_PROVIDERS = [{ provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true }];\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserTestingModule, exports: [BrowserModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserTestingModule, providers: [\n            { provide: APP_ID, useValue: 'a' },\n            provideZoneChangeDetection(),\n            { provide: PlatformLocation, useClass: MockPlatformLocation },\n        ], imports: [BrowserModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: [\n                        { provide: APP_ID, useValue: 'a' },\n                        provideZoneChangeDetection(),\n                        { provide: PlatformLocation, useClass: MockPlatformLocation },\n                    ]\n                }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserTestingModule, platformBrowserTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,0BAA0B,EAAEC,QAAQ,QAAQ,eAAe;AACvI,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,2BAA2B;AAE7E,SAASC,gBAAgBA,CAAA,EAAG;EACxBF,kBAAkB,CAACG,WAAW,CAAC,CAAC;AACpC;AACA,MAAMC,gCAAgC,GAAG,CAAC;EAAEC,OAAO,EAAEX,oBAAoB;EAAEY,QAAQ,EAAEJ,gBAAgB;EAAEK,KAAK,EAAE;AAAK,CAAC,CAAC;AACrH;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGb,qBAAqB,CAACC,YAAY,EAAE,gBAAgB,EAAEQ,gCAAgC,CAAC;AACtH;AACA;AACA;AACA;AACA;AACA,MAAMK,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,oBAAoB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACI,IAAI,kBAD+EpB,EAAE,CAAAqB,gBAAA;MAAAC,IAAA,EACSN,oBAAoB;MAAAO,OAAA,GAAYf,aAAa;IAAA,EAAI;EAAE;EAC/J;IAAS,IAAI,CAACgB,IAAI,kBAF+ExB,EAAE,CAAAyB,gBAAA;MAAAC,SAAA,EAE0C,CACrI;QAAEd,OAAO,EAAER,MAAM;QAAES,QAAQ,EAAE;MAAI,CAAC,EAClCR,0BAA0B,CAAC,CAAC,EAC5B;QAAEO,OAAO,EAAEd,gBAAgB;QAAE6B,QAAQ,EAAE5B;MAAqB,CAAC,CAChE;MAAA6B,OAAA,GAAYpB,aAAa;IAAA,EAAI;EAAE;AACxC;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KARqG7B,EAAE,CAAA8B,iBAAA,CAQXd,oBAAoB,EAAc,CAAC;IACnHM,IAAI,EAAEhB,QAAQ;IACdyB,IAAI,EAAE,CAAC;MACCR,OAAO,EAAE,CAACf,aAAa,CAAC;MACxBkB,SAAS,EAAE,CACP;QAAEd,OAAO,EAAER,MAAM;QAAES,QAAQ,EAAE;MAAI,CAAC,EAClCR,0BAA0B,CAAC,CAAC,EAC5B;QAAEO,OAAO,EAAEd,gBAAgB;QAAE6B,QAAQ,EAAE5B;MAAqB,CAAC;IAErE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASiB,oBAAoB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}