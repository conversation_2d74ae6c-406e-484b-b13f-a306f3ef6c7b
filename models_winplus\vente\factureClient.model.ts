import { Statut } from "../../enums/common/Statut.enum";
import { Operateur } from "../common/operateur.model";
import { Client } from "../tiers/client/client.model";
import { DetailFactureClient } from "./detailFactureClient.model";
import { EnteteBlVente } from "./enteteBlVente.model";


export class FactureClient {
    id?: number

    operateur?: Operateur;

    statut?: Statut;

    client?: Client;

    numFacture?: number;

    dateFacture?: string;

    dateEnvoi?: string;

    dateReglement?: string;

    montantFacTtc?: number;

    montantFacHt?: number;

    montantFacTva?: number;

    //ventes?: EnteteBlVente[]

    details?: DetailFactureClient[]


    constructor() {

    }
}