package com.example.mcp_microservice_chatboot_ai.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.theokanning.openai.service.OpenAiService;
import java.time.Duration;

/**
 * Configuration class for OpenAI.
 */
@Configuration
public class OpenAiConfig {

    @Value("${openai.api-key}")
    private String apiKey;

    @Value("${openai.timeout:30}")
    private int timeout;

    /**
     * Creates an OpenAiService bean for direct API access.
     *
     * @return An OpenAiService bean
     */
    @Bean
    public OpenAiService openAiService() {
        return new OpenAiService(apiKey, Duration.ofSeconds(timeout));
    }
}
