import { FluxFin } from "../../common/fluxFin.model"
import { CompteClient } from "../../tiers/client/compteClient.model"
import { StatistiqueVente } from "../vente/statistiqueVente.model"
import { ArreteCaisseParGroupe } from "./arreteCaisseParGroupe.model"
import { SyntheseArreteCaisseParPaiement } from "./syntheseArreteCaisseParPaiement.model"


export class ArreteCaisse {
    caissesParCategories: ArreteCaisseParGroupe[]
    caissesParTypes: ArreteCaisseParGroupe[]
    syntheseArreteCaisse: SyntheseArreteCaisseVente


    ///
    totalCat?: SyntheseArreteCaisseParPaiement
    totalType?: SyntheseArreteCaisseParPaiement

    depenses?: number
    listeDetailProduitVentesPrixModifie?: StatistiqueVente[]

    listeVentesTpa?: StatistiqueVente[];
    listeVentesNegatives?: StatistiqueVente[];
    listeVentesDifferees?: StatistiqueVente[];
    listeVentesCredit?: StatistiqueVente[];
    listeVentesAvecRemise?: StatistiqueVente[];
    listeReglementsDifferees?: FluxFin[];
    listeOperationsSoldeClient?: CompteClient[];
    listeEncaissementsClients?: CompteClient[];
    listeDetailProduitVentesAvecRemise?: StatistiqueVente[];
}






class SyntheseArreteCaisseVente {
    totalMontantRemiseVente: number
    totalMontantRemisesReglement: number

    totalMontantTpaPartClient: number
    totalMontantTpaPartOrganisme: number
    totalMontantTpaReglements: number


    totalMontantTva: number


    totalMontantVenteBrutTtc: number
    totalMontantVenteBrutTtcComptant: number
    totalMontantVenteBrutTtcCredit: number
    totalMontantVenteBrutTtcDiffere: number

    totalMontantVenteNetTtc: number


    totalMontantVenteNetTtcComptant: number
    totalMontantVenteNetTtcCredit: number
    totalMontantVenteNetTtcDiffere: number


    totalNombreVentes: number
}

