package com.example.mcp_microservice_chatboot_ai.controller;

import com.example.mcp_microservice_chatboot_ai.model.dto.AuthRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.AuthResponse;
import com.example.mcp_microservice_chatboot_ai.service.ChatMcpApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * Controller for handling authentication-related endpoints.
 */

/**
 * Role: REST controller for authentication endpoints
    Purpose:
    Exposes login endpoint for user authentication
    Delegates authentication to ChatMcpApiService
    Returns authentication responses to clients
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    private final ChatMcpApiService chatMcpApiService;

    @Autowired
    public AuthController(ChatMcpApiService chatMcpApiService) {
        this.chatMcpApiService = chatMcpApiService;
    }

    /**
     * Endpoint for user authentication.
     * 
     * @param authRequest The authentication request
     * @return A Mono containing the authentication response
     */
    @PostMapping(value = "/login", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<AuthResponse> login(@RequestBody AuthRequest authRequest) {
        return chatMcpApiService.authenticate(authRequest);
    }
}
