import { StatutInventaire } from 'src/app/winpharm/enums/inventaire/StatutInventaire.enum';
import { MethodeInventaire } from 'src/app/winpharm/enums/inventaire/MethodeInventaire.enum';

// import { Moment } from 'moment';

import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { DetailInventaireListe } from './detailInventaireListe.model';


export class EnteteInventaire { 
    audited?: boolean;
    dateCreation?: any;
    dateInventaire?: any;
    depot?: Depot;
    detailInventaireListes?: DetailInventaireListe[];
    id?: number;
    methode?: MethodeInventaire;
    numeroInventaire?: number;
    statutInventaire?: StatutInventaire;
    userModifiable?: boolean;
}

