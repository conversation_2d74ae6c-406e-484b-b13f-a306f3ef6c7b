package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * DetailBlAchat entity representing purchase order line items
 */
@Entity
@Table(name = "detail_bl_achat")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailBlAchat {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "qte", precision = 15, scale = 2)
    private BigDecimal qte;

    @Column(name = "prix_achat_ttc", precision = 15, scale = 2)
    private BigDecimal prixAchatTtc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "entete_bl_achat_id")
    private EnteteBlAchat enteteBlAchat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;
}
