# PowerShell script to check all Java files for BOM

$javaFiles = Get-ChildItem -Path "src" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    # Read the first few bytes of the file
    $bytes = [System.IO.File]::ReadAllBytes($file.FullName)
    
    # Check for UTF-8 BOM (EF BB BF)
    if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
        Write-Host "BOM found in: $($file.FullName)"
        
        # Create a new array without the BOM
        $newBytes = $bytes[3..($bytes.Length-1)]
        
        # Write the bytes back to the file
        [System.IO.File]::WriteAllBytes($file.FullName, $newBytes)
        
        Write-Host "BOM removed from: $($file.FullName)"
    }
}

Write-Host "All files checked and fixed!"
