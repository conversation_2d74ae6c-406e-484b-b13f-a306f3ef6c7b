package com.example.mcp_microservice_chatboot_ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Represents a resource in the system.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Resource {

    /**
     * The unique identifier of the resource.
     */
    private String id;

    /**
     * The title of the resource.
     */
    private String title;

    /**
     * The type of the resource.
     */
    private String type;

    /**
     * The language of the resource.
     */
    private String language;

    /**
     * The content of the resource.
     */
    private String content;

    /**
     * Additional metadata for the resource.
     */
    private Map<String, Object> metadata;
}
