@import "../../../assets/scss/custom/plugins/icons/materialdesignicons";
// Variables
$primary-color: #4a6cfa;
$primary-light: #eef1ff;
$primary-dark: #3452d9;
$secondary-color: #f8f9fd;
$text-color: #333333;
$text-light: #6e7191;
$border-color: #e4e8f7;
$shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
$shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
$success-color: #34d399;
$danger-color: #f87171;
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

// Breakpoints
$mobile-breakpoint: 480px;
$tablet-breakpoint: 768px;

.chat-widget {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000;
  font-family: $font-family;

  @media (max-width: $mobile-breakpoint) {
    bottom: 16px;
    right: 16px;
  }
}

// Chat button (collapsed state)
.chat-button {
  width: 50px; // Reduced width for icon-only view
  padding: 0;
  height: 50px;
  border-radius: 30px;
  background-color: #f0f7f9;
  color: #276578;
  border: 2px solid #276578;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $shadow-heavy;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative; // Added for better positioning

  @media (max-width: $mobile-breakpoint) {
    height: 45px;
  }

  &:hover {
    width: 180px; // Expand to full width on hover
    padding: 0 20px;
    justify-content: flex-start; // Changed to flex-start for better alignment

    svg {
      margin-right: 10px; // Add space between icon and text
    }

    .chat-button-text {
      opacity: 1;
      transform: translateX(0);
      visibility: visible;
      margin-left: 5px; // Add some space after the icon
    }
  }

  .chat-icon {
    font-size: 24px;

    @media (max-width: $mobile-breakpoint) {
      font-size: 20px;
    }
  }

  .chat-button-text {
    font-size: 16px;
    font-weight: 500;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    visibility: hidden;
    white-space: nowrap;
    position: absolute; // Position it absolutely
    left: 60px; // Position it to the right of the icon

    @media (max-width: $mobile-breakpoint) {
      font-size: 14px;
    }
  }

  svg {
    width: 35px;
    height: 35px;
    color: $text-light;
    transition: all 0.2s ease;
    visibility: visible !important; // Always visible
    opacity: 1 !important; // Always visible
    z-index: 1; // Ensure it's above other elements

    @media (max-width: $mobile-breakpoint) {
      width: 28px;
      height: 28px;
    }
  }
}

// Chat container (expanded state)
.chat-container {
  width: 450px;
  height: 750px;
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: $shadow-heavy;
  border: 1px solid $border-color;
  animation: slideUp 0.3s ease-out;

  @media (max-width: $tablet-breakpoint) {
    width: 90vw;
    max-width: 450px;
    height: 80vh;
    max-height: 750px;
  }

  @media (max-width: $mobile-breakpoint) {
    width: 100vw;
    height: 100vh;
    max-height: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    animation: mobileSlideUp 0.3s ease-out;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobileSlideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Chat header
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 20px;
  background-color: white;
  border-bottom: 1px solid $border-color;

  @media (max-width: $mobile-breakpoint) {
    padding: 10px 16px;
  }

  .back-button {
    background: none;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: $text-light;
    transition: all 0.2s ease;

    &:hover {
      background-color: $secondary-color;
      color: $text-color;
    }
  }

  .header-title {
    font-weight: 600;
    font-size: 16px;
    color: $text-color;
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;

    @media (max-width: $mobile-breakpoint) {
      font-size: 15px;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-tabs {
    display: flex;
    gap: 8px;

    @media (max-width: $mobile-breakpoint) {
      gap: 4px;
    }

    .tab {
      padding: 8px;
      width: 32px;
      height: 32px;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: all 0.2s ease;
      color: $text-light;

      &:hover:not(.active) {
        background-color: $secondary-color;
        color: $text-color;
      }

      &.active {
        background-color: $primary-light;
        color: $primary-color;
      }
    }
  }

  .close-button {
    background: hsla(11, 100%, 62.2%, 1);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: #fff;
    transition: all 0.2s ease;

    &:hover {
      background-color: $secondary-color;
      color: $text-color;
    }
  }
}

// Chat content
.chat-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
  height: calc(100% - 60px); // Account for footer
}

// Chat tab
.chat-tab {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;

  .conversation-list-container {
    width: 100%;
    display: flex;
    flex-direction: column;

    .new-chat-btn {
      margin: 16px;
      padding: 12px;
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.2s ease;

      @media (max-width: $mobile-breakpoint) {
        margin: 12px;
        padding: 10px;
      }

      &:hover {
        background-color: $primary-dark;
      }

      .new-icon {
        font-size: 14px;
      }
    }

    .conversation-list {
      flex: 1;
      overflow-y: auto;
      padding: 0 16px 16px;

      @media (max-width: $mobile-breakpoint) {
        padding: 0 12px 12px;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 10px;
      }

      .conversation-item {
        padding: 12px;
        margin-bottom: 8px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 8px;
        transition: all 0.2s ease;
        border: 1px solid $border-color;

        @media (max-width: $mobile-breakpoint) {
          padding: 10px;
        }

        &:hover {
          background-color: $secondary-color;
          transform: translateY(-1px);
        }

        &.active {
          background-color: $primary-light;
          border-color: rgba($primary-color, 0.3);

          .conv-title {
            color: $primary-color;
            font-weight: 500;
          }
        }

        .conv-info {
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .conv-title {
            font-size: 14px;
            font-weight: 500;
            color: $text-color;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 4px;

            @media (max-width: $mobile-breakpoint) {
              font-size: 13px;
            }
          }

          .conv-date {
            font-size: 12px;
            color: $text-light;

            @media (max-width: $mobile-breakpoint) {
              font-size: 11px;
            }
          }
        }

        .delete-btn {
          visibility: hidden;
          background: none;
          border: none;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $text-light;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(0, 0, 0, 0.06);
            color: $danger-color;
          }
        }

        &:hover .delete-btn {
          visibility: visible;
        }
      }
    }
  }

  .conversation-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// Help tab
.help-tab {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  background-color: white;

  @media (max-width: $mobile-breakpoint) {
    padding: 16px;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }
}

// Home tab
.home-tab {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// Footer navigation
.chat-footer {
  height: 60px;
  border-top: 1px solid $border-color;
  background-color: white;

  @media (max-width: $mobile-breakpoint) {
    height: 55px;
  }

  .footer-nav {
    display: flex;
    justify-content: space-around;
    height: 100%;

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      cursor: pointer;
      color: $text-light;
      font-size: 12px;
      transition: all 0.2s ease;

      @media (max-width: $mobile-breakpoint) {
        font-size: 11px;
      }

      i {
        font-size: 20px;
        margin-bottom: 4px;

        @media (max-width: $mobile-breakpoint) {
          font-size: 18px;
          margin-bottom: 2px;
        }
      }

      &:hover {
        color: $text-color;
      }

      &.active {
        color: $primary-color;
      }
    }
  }
}
