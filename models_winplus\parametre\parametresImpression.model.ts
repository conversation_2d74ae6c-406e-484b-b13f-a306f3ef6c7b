import { StructureTicket } from './structureTicket.model';

export class ParametresImpression {

  autoImpressionTicketVente?: boolean;

  ouvertureAutoTiroirCaisse?: boolean;

  afficheurActif?: boolean;

  structureTicket: StructureTicket;

  autoImpressionTicketEncaissClient: boolean


  constructor() {
    this.autoImpressionTicketVente = false;
    this.ouvertureAutoTiroirCaisse = false;
    this.afficheurActif = false;
    this.autoImpressionTicketEncaissClient = false

    this.structureTicket = new StructureTicket();
  }
}


