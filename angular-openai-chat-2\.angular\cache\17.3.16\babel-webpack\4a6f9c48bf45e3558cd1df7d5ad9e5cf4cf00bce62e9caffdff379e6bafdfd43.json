{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { MessageStorageService } from './message-storage.service';\ndescribe('MessageStorageService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({});\n    service = TestBed.inject(MessageStorageService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "MessageStorageService", "describe", "service", "beforeEach", "configureTestingModule", "inject", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\services\\message-storage.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\r\n\r\nimport { MessageStorageService } from './message-storage.service';\r\n\r\ndescribe('MessageStorageService', () => {\r\n  let service: MessageStorageService;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({});\r\n    service = TestBed.inject(MessageStorageService);\r\n  });\r\n\r\n  it('should be created', () => {\r\n    expect(service).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,qBAAqB,QAAQ,2BAA2B;AAEjEC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,OAA8B;EAElCC,UAAU,CAAC,MAAK;IACdJ,OAAO,CAACK,sBAAsB,CAAC,EAAE,CAAC;IAClCF,OAAO,GAAGH,OAAO,CAACM,MAAM,CAACL,qBAAqB,CAAC;EACjD,CAAC,CAAC;EAEFM,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACL,OAAO,CAAC,CAACM,UAAU,EAAE;EAC9B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}