import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { StatutVentilation } from 'src/app/winpharm/enums/common/StatutVentilation.enum';
import { SousStatutFluxFin } from 'src/app/winpharm/enums/common/SousStatutFluxFin.enum';
import { SensFlux } from 'src/app/winpharm/enums/common/SensFlux.enum';
import { ModePaiement } from 'src/app/winpharm/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { Banque } from './banque.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { Tiers } from 'src/app/winpharm/models/tiers/tiers.model';


export class FluxFin {
  audited?: boolean;
  banque?: Banque;
  dateEcheance?: any;
  dateFlux?: any;
  designationFlux?: string;
  id?: number;
  mntBrutFlux?: number;
  mntEscompte?: number;
  mntNetFlux?: number;
  mntTaxeParafiscale?: number;
  modeFlux?: ModePaiement;
  nomTiers?: string;
  numFlux?: string;
  numeroDocumentCause?: number;
  operateur?: Operateur;
  resteAVentiler?: number;
  sensFlux?: SensFlux;
  sousStatut?: SousStatutFluxFin;
  statut?: Statut;
  statutVentilation?: StatutVentilation;
  tauxEscompte?: number;
  tiers?: Tiers;
  userModifiable?: boolean;
  tauxRemise?: number

  //
  toSold?: boolean = false

  montantRemise?: number


  constructor() {
    this.modeFlux = ModePaiement.ESPECE;
    this.mntBrutFlux = 0;
    this.mntNetFlux = 0;
  }
}

