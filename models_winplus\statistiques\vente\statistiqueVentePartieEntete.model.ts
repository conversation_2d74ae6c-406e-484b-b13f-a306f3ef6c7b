import { TypeVente } from 'src/app/winpharm/enums/vente/TypeVente.enum';
import { TypeEncaissementVente } from 'src/app/winpharm/enums/vente/TypeEncaissementVente.enum';
import { ModePaiement } from 'src/app/winpharm/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { Client } from 'src/app/winpharm/models/tiers/client/client.model';
import { Medecin } from 'src/app/winpharm/models/common/medecin.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';


export class StatistiqueVentePartieEntete { 
    client?: Client;
    dateFacture?: any;
    dateVente?: any;
    id?: number;
    medecin?: Medecin;
    modePaiement?: ModePaiement;
    numeroFacture?: number;
    numeroVente?: number;
    operateur?: Operateur;
    typeEncaissement?: TypeEncaissementVente;
    typeVente?: TypeVente;
}

