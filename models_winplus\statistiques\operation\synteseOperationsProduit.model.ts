
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { SyntheseOperation } from './syntheseOperation.model';
import { SyntheseStock } from 'src/app/winpharm/models/statistiques/stock-inventaire/syntheseStock.model';


export class SynteseOperationsProduit { 
    codeProduit?: string;
    designationProduit?: string;
    forme?: FormeProduit;
    partieStock?: SyntheseStock;
    synteseAchats?: SyntheseOperation;
    synteseAvoirsFournisseur?: SyntheseOperation;
    synteseAvoirsInternes?: SyntheseOperation;
    synteseEchangesEntree?: SyntheseOperation;
    synteseEchangesSortie?: SyntheseOperation;
    synteseVentes?: SyntheseOperation;
}
