package com.chatbootmcp.chatmcp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chatbootmcp.chatmcp.dto.response.UserDataResponse;
import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.entity.UserData;
import com.chatbootmcp.chatmcp.exception.ResourceNotFoundException;
import com.chatbootmcp.chatmcp.repository.UserDataRepository;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import com.chatbootmcp.chatmcp.util.MockDataGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserDataService {

    @Autowired
    private UserDataRepository userDataRepository;

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MockDataGenerator mockDataGenerator;
    
    @Autowired
    private ObjectMapper objectMapper;

    public List<UserDataResponse> getUserData(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        List<UserData> userData = userDataRepository.findByUser(user);
        
        // If no data exists, generate mock data
        if (userData.isEmpty()) {
            generateMockUserData(user);
            userData = userDataRepository.findByUser(user);
        }
        
        return userData.stream()
                .map(UserDataResponse::new)
                .collect(Collectors.toList());
    }

    public UserDataResponse getUserDataByType(String username, String dataType) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        UserData userData = userDataRepository.findByUserAndDataType(user, dataType)
                .orElseThrow(() -> new ResourceNotFoundException("User data not found for type: " + dataType));
        
        return new UserDataResponse(userData);
    }
    
    private void generateMockUserData(User user) {
        try {
            // Usage statistics
            Map<String, Object> usageStats = mockDataGenerator.generateUsageStatistics();
            saveUserData(user, "usage_statistics", usageStats);
            
            // Performance metrics
            Map<String, Object> performanceMetrics = mockDataGenerator.generatePerformanceMetrics();
            saveUserData(user, "performance_metrics", performanceMetrics);
            
            // Account information
            Map<String, Object> accountInfo = mockDataGenerator.generateAccountInformation();
            saveUserData(user, "account_information", accountInfo);
            
            // Recent activities
            Map<String, Object> recentActivities = mockDataGenerator.generateRecentActivities();
            saveUserData(user, "recent_activities", recentActivities);
            
            // Recommendations
            Map<String, Object> recommendations = mockDataGenerator.generateRecommendations();
            saveUserData(user, "recommendations", recommendations);
        } catch (Exception e) {
            throw new RuntimeException("Error generating mock data", e);
        }
    }
    
    private void saveUserData(User user, String dataType, Map<String, Object> dataValue) throws JsonProcessingException {
        UserData userData = new UserData();
        userData.setUser(user);
        userData.setDataType(dataType);
        userData.setDataValue(objectMapper.writeValueAsString(dataValue));
        userData.setLastUpdated(LocalDateTime.now());
        
        userDataRepository.save(userData);
    }
}
