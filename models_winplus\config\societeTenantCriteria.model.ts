import { Pays } from "../common/pays.model";
import { Ville } from "../common/ville.model";



export class SocieteTenantCriteria {



    adr1?: string;
    adr2?: string;
    audited?: boolean;

    codePostal?: string;
    email?: string;
    fax?: string;
    gsm1?: string;
    gsm2?: string;
    id?: number;

    nom?: string;
    nomComplet?: string;
    numCin?: string;
    numIce?: string;
    numIf?: string;
    numRc?: string;
    numTelephone?: string;
    pays?: Pays;
    prenom?: string;
    raisonSociale?: string;
    userModifiable?: boolean;
    ville?: Ville;
    villeRc?: Ville;
    numPatente?: string;

    tenantId?;


    dateCreationDebut?: string;

    dateCreationFin?: string;


    code?;


    adresse?;



    gsm?;



    username?;



    constructor(raisonSociale, ville = null) {
        this.raisonSociale = raisonSociale
        this.ville = ville
    }



}