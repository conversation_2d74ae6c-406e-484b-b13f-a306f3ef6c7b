package com.chatbootmcp.chatmcp.util;

import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * Utility class for generating mock data for the application
 */
@Component
public class MockDataGenerator {

    private final Random random = new Random();

    public Map<String, Object> generateUsageStatistics() {
        Map<String, Object> usageStats = new HashMap<>();

        // Daily active time in minutes for the last 7 days
        List<Map<String, Object>> dailyUsage = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (int i = 6; i >= 0; i--) {
            Map<String, Object> dayData = new HashMap<>();
            LocalDate date = today.minusDays(i);
            dayData.put("date", date.format(DateTimeFormatter.ISO_DATE));
            dayData.put("minutes", random.nextInt(180) + 30); // 30-210 minutes
            dailyUsage.add(dayData);
        }
        usageStats.put("daily_usage", dailyUsage);

        // Feature usage percentages
        Map<String, Integer> featureUsage = new HashMap<>();
        featureUsage.put("chat", random.nextInt(40) + 20); // 20-60%
        featureUsage.put("analytics", random.nextInt(30) + 10); // 10-40%
        featureUsage.put("documents", random.nextInt(20) + 5); // 5-25%
        featureUsage.put("settings", random.nextInt(10) + 5); // 5-15%
        featureUsage.put("other", random.nextInt(10) + 5); // 5-15%
        usageStats.put("feature_usage", featureUsage);

        // Total sessions
        usageStats.put("total_sessions", random.nextInt(50) + 10); // 10-60 sessions

        // Average session duration in minutes
        usageStats.put("avg_session_duration", random.nextInt(30) + 10); // 10-40 minutes

        // Total time spent in hours
        usageStats.put("total_time_spent", random.nextInt(100) + 20); // 20-120 hours

        return usageStats;
    }

    public Map<String, Object> generatePerformanceMetrics() {
        Map<String, Object> performanceMetrics = new HashMap<>();

        // Monthly task completion rate
        List<Map<String, Object>> monthlyCompletion = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (int i = 5; i >= 0; i--) {
            Map<String, Object> monthData = new HashMap<>();
            LocalDate date = today.withDayOfMonth(1).minusMonths(i);
            monthData.put("month", date.format(DateTimeFormatter.ofPattern("MMM yyyy")));
            monthData.put("completion_rate", random.nextInt(30) + 70); // 70-100%
            monthlyCompletion.add(monthData);
        }
        performanceMetrics.put("monthly_completion", monthlyCompletion);

        // Current month statistics
        Map<String, Object> currentMonth = new HashMap<>();
        currentMonth.put("completed_tasks", random.nextInt(50) + 10); // 10-60 tasks
        currentMonth.put("pending_tasks", random.nextInt(20) + 5); // 5-25 tasks
        currentMonth.put("overdue_tasks", random.nextInt(10)); // 0-10 tasks
        performanceMetrics.put("current_month", currentMonth);

        // Productivity score (out of 100)
        performanceMetrics.put("productivity_score", random.nextInt(30) + 70); // 70-100

        // Efficiency rating (out of 5)
        performanceMetrics.put("efficiency_rating", (random.nextInt(20) + 30) / 10.0); // 3.0-5.0

        // Improvement from last month (percentage)
        performanceMetrics.put("improvement", random.nextInt(30) - 5); // -5% to +25%

        return performanceMetrics;
    }

    public Map<String, Object> generateAccountInformation() {
        Map<String, Object> accountInfo = new HashMap<>();

        // Account type
        String[] accountTypes = {"Basic", "Premium", "Enterprise", "Trial"};
        accountInfo.put("account_type", accountTypes[random.nextInt(accountTypes.length)]);

        // Subscription status
        String[] statuses = {"Active", "Pending Renewal", "Grace Period"};
        accountInfo.put("subscription_status", statuses[random.nextInt(statuses.length)]);

        // Join date (1-24 months ago)
        LocalDate joinDate = LocalDate.now().minusMonths(random.nextInt(24) + 1);
        accountInfo.put("join_date", joinDate.format(DateTimeFormatter.ISO_DATE));

        // Last billing date (0-30 days ago)
        LocalDate billingDate = LocalDate.now().minusDays(random.nextInt(31));
        accountInfo.put("last_billing_date", billingDate.format(DateTimeFormatter.ISO_DATE));

        // Next billing date (1-30 days in future)
        LocalDate nextBillingDate = LocalDate.now().plusDays(random.nextInt(30) + 1);
        accountInfo.put("next_billing_date", nextBillingDate.format(DateTimeFormatter.ISO_DATE));

        // Storage usage
        accountInfo.put("storage_used_gb", (random.nextInt(100) + 1) / 10.0); // 0.1-10.0 GB
        accountInfo.put("storage_limit_gb", 10.0); // 10 GB limit

        // Team members
        accountInfo.put("team_members", random.nextInt(10) + 1); // 1-10 members
        accountInfo.put("max_team_members", 10); // 10 max members

        return accountInfo;
    }

    public Map<String, Object> generateRecentActivities() {
        Map<String, Object> recentActivities = new HashMap<>();

        // List of recent activities
        List<Map<String, Object>> activities = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        String[] activityTypes = {
            "login", "document_create", "document_edit", "chat_session",
            "settings_change", "export_data", "invite_team_member"
        };

        for (int i = 0; i < random.nextInt(10) + 5; i++) { // 5-15 activities
            Map<String, Object> activity = new HashMap<>();

            // Random activity type
            String activityType = activityTypes[random.nextInt(activityTypes.length)];
            activity.put("type", activityType);

            // Random timestamp within last 7 days
            LocalDateTime timestamp = now.minusHours(random.nextInt(168)); // 0-168 hours (7 days)
            activity.put("timestamp", timestamp.format(DateTimeFormatter.ISO_DATE_TIME));

            // Activity details
            Map<String, Object> details = new HashMap<>();
            switch (activityType) {
                case "login":
                    details.put("ip_address", "192.168." + random.nextInt(256) + "." + random.nextInt(256));
                    details.put("device", random.nextBoolean() ? "Mobile" : "Desktop");
                    break;
                case "document_create":
                case "document_edit":
                    details.put("document_id", UUID.randomUUID().toString());
                    details.put("document_name", "Document " + (random.nextInt(100) + 1));
                    break;
                case "chat_session":
                    details.put("session_id", UUID.randomUUID().toString());
                    details.put("duration_minutes", random.nextInt(60) + 1);
                    details.put("messages_count", random.nextInt(50) + 1);
                    break;
                case "settings_change":
                    String[] settings = {"notification_preferences", "privacy_settings", "display_options"};
                    details.put("setting", settings[random.nextInt(settings.length)]);
                    break;
                case "export_data":
                    String[] formats = {"PDF", "CSV", "JSON"};
                    details.put("format", formats[random.nextInt(formats.length)]);
                    details.put("size_kb", random.nextInt(5000) + 100);
                    break;
                case "invite_team_member":
                    details.put("invited_email", "user" + random.nextInt(100) + "@example.com");
                    details.put("role", random.nextBoolean() ? "Admin" : "Member");
                    break;
            }
            activity.put("details", details);

            activities.add(activity);
        }

        // Sort by timestamp (most recent first)
        activities.sort((a, b) -> {
            String timeA = (String) a.get("timestamp");
            String timeB = (String) b.get("timestamp");
            return timeB.compareTo(timeA);
        });

        recentActivities.put("activities", activities);
        recentActivities.put("total_count", activities.size());

        return recentActivities;
    }

    public Map<String, Object> generateRecommendations() {
        Map<String, Object> recommendations = new HashMap<>();

        // Feature recommendations
        List<Map<String, Object>> featureRecs = new ArrayList<>();
        String[] features = {
            "Advanced Analytics", "Team Collaboration", "Document Automation",
            "Custom Integrations", "Mobile App", "API Access"
        };

        for (int i = 0; i < random.nextInt(3) + 1; i++) { // 1-3 feature recommendations
            Map<String, Object> rec = new HashMap<>();
            rec.put("feature", features[random.nextInt(features.length)]);
            rec.put("reason", "Based on your usage patterns");
            rec.put("benefit", "Could improve productivity by " + (random.nextInt(20) + 10) + "%");
            featureRecs.add(rec);
        }
        recommendations.put("feature_recommendations", featureRecs);

        // Usage optimization
        List<String> optimizationTips = new ArrayList<>();
        String[] tips = {
            "Schedule regular team check-ins",
            "Use keyboard shortcuts for faster navigation",
            "Set up custom notification filters",
            "Create templates for common tasks",
            "Enable two-factor authentication for better security",
            "Integrate with your calendar for better time management"
        };

        for (int i = 0; i < random.nextInt(3) + 2; i++) { // 2-4 tips
            optimizationTips.add(tips[random.nextInt(tips.length)]);
        }
        recommendations.put("optimization_tips", optimizationTips);

        // Personalized insights
        Map<String, Object> insights = new HashMap<>();
        insights.put("peak_productivity_day", getDayOfWeek());
        insights.put("peak_productivity_time", getTimeOfDay());
        insights.put("most_used_feature", getMostUsedFeature());
        insights.put("collaboration_score", random.nextInt(5) + 6); // 6-10 out of 10
        recommendations.put("personalized_insights", insights);

        return recommendations;
    }

    private String getDayOfWeek() {
        String[] days = {"Monday", "Tuesday", "Wednesday", "Thursday", "Friday"};
        return days[random.nextInt(days.length)];
    }

    private String getTimeOfDay() {
        String[] times = {"Morning (9-11 AM)", "Midday (11 AM-1 PM)", "Afternoon (1-4 PM)", "Evening (4-6 PM)"};
        return times[random.nextInt(times.length)];
    }

    private String getMostUsedFeature() {
        String[] features = {"Chat", "Document Editor", "Analytics Dashboard", "Task Manager", "Team Collaboration"};
        return features[random.nextInt(features.length)];
    }

    /**
     * Generate hourly usage data for a single day
     */
    public List<Map<String, Object>> generateHourlyUsageData() {
        List<Map<String, Object>> hourlyData = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // Generate data for the last 24 hours
        for (int i = 23; i >= 0; i--) {
            Map<String, Object> hourData = new HashMap<>();
            LocalDateTime hour = now.minusHours(i);

            hourData.put("hour", hour.format(DateTimeFormatter.ofPattern("HH:00")));
            hourData.put("timestamp", hour);

            // More activity during business hours (9 AM - 5 PM)
            int hourOfDay = hour.getHour();
            int baseActivity = (hourOfDay >= 9 && hourOfDay <= 17) ? 30 : 10;

            hourData.put("activeUsers", random.nextInt(baseActivity) + 5);
            hourData.put("messagesSent", random.nextInt(baseActivity * 5) + 10);
            hourData.put("queriesProcessed", random.nextInt(baseActivity * 10) + 20);

            hourlyData.add(hourData);
        }

        return hourlyData;
    }

    /**
     * Generate daily usage data for a week
     */
    public List<Map<String, Object>> generateDailyUsageData() {
        List<Map<String, Object>> dailyData = new ArrayList<>();
        LocalDate today = LocalDate.now();

        // Generate data for the last 7 days
        for (int i = 6; i >= 0; i--) {
            Map<String, Object> dayData = new HashMap<>();
            LocalDate date = today.minusDays(i);

            dayData.put("date", date.format(DateTimeFormatter.ISO_DATE));
            dayData.put("day", date.getDayOfWeek().toString().substring(0, 3));
            dayData.put("timestamp", date.atStartOfDay());

            // Less activity on weekends
            boolean isWeekend = date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY;
            int baseActivity = isWeekend ? 30 : 70;

            dayData.put("activeUsers", random.nextInt(baseActivity) + 20);
            dayData.put("messagesSent", random.nextInt(baseActivity * 5) + 50);
            dayData.put("queriesProcessed", random.nextInt(baseActivity * 10) + 100);
            dayData.put("averageResponseTime", (random.nextInt(30) + 10) / 10.0); // 1.0-4.0 seconds

            dailyData.add(dayData);
        }

        return dailyData;
    }

    /**
     * Generate weekly usage data for a month
     */
    public List<Map<String, Object>> generateWeeklyUsageData() {
        List<Map<String, Object>> weeklyData = new ArrayList<>();
        LocalDate today = LocalDate.now();

        // Generate data for the last 4 weeks
        for (int i = 4; i >= 0; i--) {
            Map<String, Object> weekData = new HashMap<>();

            // Get the start of the week (Monday)
            LocalDate weekStart = today.minusWeeks(i).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            LocalDate weekEnd = weekStart.plusDays(6);

            weekData.put("weekStart", weekStart.format(DateTimeFormatter.ISO_DATE));
            weekData.put("weekEnd", weekEnd.format(DateTimeFormatter.ISO_DATE));
            weekData.put("weekLabel", "Week " + (5 - i));
            weekData.put("timestamp", weekStart.atStartOfDay());

            weekData.put("activeUsers", random.nextInt(300) + 200);
            weekData.put("messagesSent", random.nextInt(2000) + 1000);
            weekData.put("queriesProcessed", random.nextInt(5000) + 2000);
            weekData.put("newUsers", random.nextInt(50) + 10);
            weekData.put("averageSessionDuration", random.nextInt(20) + 10); // 10-30 minutes

            weeklyData.add(weekData);
        }

        return weeklyData;
    }

    /**
     * Generate monthly usage data for a year
     */
    public List<Map<String, Object>> generateMonthlyUsageData() {
        List<Map<String, Object>> monthlyData = new ArrayList<>();
        LocalDate today = LocalDate.now();

        // Generate data for the last 12 months
        for (int i = 11; i >= 0; i--) {
            Map<String, Object> monthData = new HashMap<>();

            LocalDate monthStart = today.minusMonths(i).withDayOfMonth(1);

            monthData.put("month", monthStart.format(DateTimeFormatter.ofPattern("MMM yyyy")));
            monthData.put("timestamp", monthStart.atStartOfDay());

            monthData.put("activeUsers", random.nextInt(1000) + 500);
            monthData.put("messagesSent", random.nextInt(10000) + 5000);
            monthData.put("queriesProcessed", random.nextInt(20000) + 10000);
            monthData.put("newUsers", random.nextInt(200) + 50);
            monthData.put("churnRate", (random.nextInt(30) + 5) / 10.0); // 0.5-3.5%
            monthData.put("averageSessionsPerUser", random.nextInt(20) + 10);

            monthlyData.add(monthData);
        }

        return monthlyData;
    }

    /**
     * Generate feature usage data
     */
    public Map<String, Object> generateFeatureUsageData() {
        Map<String, Object> featureData = new HashMap<>();

        // Feature usage percentages
        Map<String, Integer> featureUsage = new HashMap<>();
        featureUsage.put("Chat", random.nextInt(30) + 30); // 30-60%
        featureUsage.put("Analytics", random.nextInt(20) + 10); // 10-30%
        featureUsage.put("Documents", random.nextInt(15) + 5); // 5-20%
        featureUsage.put("Settings", random.nextInt(10) + 5); // 5-15%
        featureUsage.put("Profile", random.nextInt(10) + 5); // 5-15%
        featureUsage.put("Other", random.nextInt(10) + 5); // 5-15%

        featureData.put("featureUsagePercentages", featureUsage);

        // Feature engagement scores (out of 10)
        Map<String, Double> featureScores = new HashMap<>();
        featureScores.put("Chat", (random.nextInt(30) + 70) / 10.0); // 7.0-10.0
        featureScores.put("Analytics", (random.nextInt(40) + 50) / 10.0); // 5.0-9.0
        featureScores.put("Documents", (random.nextInt(50) + 40) / 10.0); // 4.0-9.0
        featureScores.put("Settings", (random.nextInt(40) + 40) / 10.0); // 4.0-8.0
        featureScores.put("Profile", (random.nextInt(40) + 40) / 10.0); // 4.0-8.0

        featureData.put("featureEngagementScores", featureScores);

        // Most used features ranking
        List<Map<String, Object>> topFeatures = new ArrayList<>();
        String[] features = {"Message Sending", "Data Visualization", "Document Export", "User Search", "Profile Editing"};

        for (int i = 0; i < features.length; i++) {
            Map<String, Object> feature = new HashMap<>();
            feature.put("name", features[i]);
            feature.put("usageCount", random.nextInt(10000) + 1000 - (i * 1000));
            feature.put("percentChange", (random.nextInt(40) - 10)); // -10% to +30%
            topFeatures.add(feature);
        }

        featureData.put("topFeatures", topFeatures);

        return featureData;
    }

    /**
     * Generate user engagement metrics
     */
    public Map<String, Object> generateEngagementMetrics() {
        Map<String, Object> engagementData = new HashMap<>();

        // Overall engagement score
        engagementData.put("overallScore", (random.nextInt(30) + 70)); // 70-100

        // Engagement by category
        Map<String, Integer> categoryScores = new HashMap<>();
        categoryScores.put("Frequency", random.nextInt(30) + 70); // 70-100
        categoryScores.put("Duration", random.nextInt(30) + 70); // 70-100
        categoryScores.put("Interaction", random.nextInt(30) + 70); // 70-100
        categoryScores.put("Feedback", random.nextInt(30) + 70); // 70-100

        engagementData.put("categoryScores", categoryScores);

        // Engagement trends
        List<Map<String, Object>> trends = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (int i = 6; i >= 0; i--) {
            Map<String, Object> trend = new HashMap<>();
            LocalDate date = today.minusDays(i);

            trend.put("date", date.format(DateTimeFormatter.ISO_DATE));
            trend.put("score", random.nextInt(30) + 70); // 70-100

            trends.add(trend);
        }

        engagementData.put("trends", trends);

        // User segments
        List<Map<String, Object>> segments = new ArrayList<>();

        String[] segmentNames = {"Power Users", "Regular Users", "Occasional Users", "New Users", "Inactive Users"};
        int[] segmentSizes = {15, 30, 25, 20, 10}; // Percentages

        for (int i = 0; i < segmentNames.length; i++) {
            Map<String, Object> segment = new HashMap<>();
            segment.put("name", segmentNames[i]);
            segment.put("percentage", segmentSizes[i]);
            segment.put("count", (segmentSizes[i] * 10) + random.nextInt(50));

            segments.add(segment);
        }

        engagementData.put("userSegments", segments);

        return engagementData;
    }

    /**
     * Generate realistic user profile data
     */
    public Map<String, Object> generateUserProfileData(String username) {
        Map<String, Object> profileData = new HashMap<>();

        switch (username) {
            case "user1":
                profileData.put("fullName", "Jean Dupont 2");
                profileData.put("phoneNumber", "+33 1 23 45 67 89");
                profileData.put("address", "123 Rue de la Santé");
                profileData.put("city", "Paris");
                profileData.put("postalCode", "75014");
                profileData.put("country", "France");
                profileData.put("dateOfBirth", LocalDate.of(1985, 3, 15));
                profileData.put("jobTitle", "Pharmacien");
                profileData.put("company", "Pharmacie Centrale");
                profileData.put("department", "Vente");
                profileData.put("employeeId", "EMP001");
                profileData.put("accountType", "Premium");
                profileData.put("subscriptionStatus", "Active");
                profileData.put("subscriptionStartDate", LocalDate.now().minusMonths(6));
                profileData.put("subscriptionEndDate", LocalDate.now().plusMonths(6));
                profileData.put("subscriptionType", "Annuel");
                profileData.put("subscriptionPrice", "499€");
                profileData.put("currentBalance", 1250.00);
                profileData.put("creditLimit", 5000.00);
                profileData.put("paymentMethod", "Carte Bancaire");
                profileData.put("cardLastFour", "1234");
                profileData.put("cardExpiryDate", "12/2026");
                profileData.put("nextPaymentDue", LocalDate.now().plusDays(15));
                profileData.put("languagePreference", "French");
                profileData.put("timezone", "Europe/Paris");
                profileData.put("profileCompletionPercentage", 95);
                break;

            case "user2":
                profileData.put("fullName", "Marie Martin");
                profileData.put("phoneNumber", "+33 4 56 78 90 12");
                profileData.put("address", "45 Avenue des Fleurs");
                profileData.put("city", "Lyon");
                profileData.put("postalCode", "69002");
                profileData.put("country", "France");
                profileData.put("dateOfBirth", LocalDate.of(1990, 7, 22));
                profileData.put("jobTitle", "Assistant Pharmacien");
                profileData.put("company", "Pharmacie du Marché");
                profileData.put("department", "Assistance");
                profileData.put("employeeId", "EMP002");
                profileData.put("accountType", "Standard");
                profileData.put("subscriptionStatus", "Active");
                profileData.put("subscriptionStartDate", LocalDate.now().minusMonths(3));
                profileData.put("subscriptionEndDate", LocalDate.now().plusMonths(9));
                profileData.put("subscriptionType", "Mensuel");
                profileData.put("subscriptionPrice", "49€");
                profileData.put("currentBalance", 750.00);
                profileData.put("creditLimit", 2000.00);
                profileData.put("paymentMethod", "Carte Bancaire");
                profileData.put("cardLastFour", "5678");
                profileData.put("cardExpiryDate", "08/2025");
                profileData.put("nextPaymentDue", LocalDate.now().plusDays(10));
                profileData.put("languagePreference", "French");
                profileData.put("timezone", "Europe/Paris");
                profileData.put("profileCompletionPercentage", 85);
                break;

            default:
                profileData.put("fullName", "Utilisateur Test");
                profileData.put("phoneNumber", "+33 1 00 00 00 00");
                profileData.put("address", "1 Rue de Test");
                profileData.put("city", "Paris");
                profileData.put("postalCode", "75001");
                profileData.put("country", "France");
                profileData.put("dateOfBirth", LocalDate.of(1980, 1, 1));
                profileData.put("jobTitle", "Utilisateur");
                profileData.put("company", "Test Company");
                profileData.put("accountType", "Trial");
                profileData.put("subscriptionStatus", "Trial");
                profileData.put("subscriptionStartDate", LocalDate.now().minusDays(7));
                profileData.put("subscriptionEndDate", LocalDate.now().plusDays(7));
                profileData.put("subscriptionType", "Essai");
                profileData.put("subscriptionPrice", "0€");
                profileData.put("currentBalance", 0.00);
                profileData.put("creditLimit", 100.00);
                profileData.put("paymentMethod", "Aucun");
                profileData.put("languagePreference", "French");
                profileData.put("timezone", "Europe/Paris");
                profileData.put("profileCompletionPercentage", 50);
        }

        return profileData;
    }
}
