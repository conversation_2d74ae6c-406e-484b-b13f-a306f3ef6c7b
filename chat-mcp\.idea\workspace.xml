<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f8fc4de5-e16f-472a-b1a3-f3434b1df095" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="chat-mcp" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="chat-mcp" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2x2Uu4ul15dpXq8P9xw3gAvWnmz" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Downloaded.Files.Path.Enabled&quot;: &quot;false&quot;,
    &quot;Gradle.Build chat-mcp.executor&quot;: &quot;Run&quot;,
    &quot;Repository.Attach.Annotations&quot;: &quot;false&quot;,
    &quot;Repository.Attach.JavaDocs&quot;: &quot;false&quot;,
    &quot;Repository.Attach.Sources&quot;: &quot;false&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ChatMcpApplication (1).executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ChatMcpApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Downloads/Work __Abderrahmane_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/chat-mcp&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;h2&quot;
    ]
  }
}</component>
  <component name="RunManager">
    <configuration name="ChatMcpApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="chat-mcp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chatbootmcp.chatmcp.ChatMcpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.26775.15" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.26775.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f8fc4de5-e16f-472a-b1a3-f3434b1df095" name="Changes" comment="" />
      <created>1747135884871</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747135884871</updated>
      <workItem from="1747135885929" duration="115000" />
      <workItem from="1747136018166" duration="1027000" />
      <workItem from="1747137410621" duration="14000" />
      <workItem from="1747137438385" duration="9436000" />
      <workItem from="1747745483488" duration="439000" />
      <workItem from="1747746162512" duration="1061000" />
      <workItem from="1747748853952" duration="6000" />
      <workItem from="1747749747565" duration="939000" />
      <workItem from="1747824509184" duration="898000" />
      <workItem from="1747918837940" duration="1271000" />
      <workItem from="1747991270797" duration="6549000" />
      <workItem from="1748450730377" duration="496000" />
      <workItem from="1748524919520" duration="733000" />
      <workItem from="1748858105716" duration="24000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>