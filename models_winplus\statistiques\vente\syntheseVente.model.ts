


export class SyntheseVente {
    tauxRemiseVente?: number;
    totalMontantCredit?: number;
    totalMontantMarge?: number;
    totalMontantPartClient?: number;
    totalMontantPartOrganisme?: number;
    totalMontantRemise?: number;
    totalMontantTva?: number;
    totalMontantVenteBrutHt?: number;
    totalMontantVenteBrutTtc?: number;
    totalMontantVenteNetAPayer?: number;
    totalMontantVenteNetHt?: number;
    totalMontantVenteNetTtc?: number;
    totalMontantVentePaye?: number;
    totalNombreLignesVentes?: number;
    totalNombreProduits?: number;
    totalNombreVentes?: number;
    totalQuantitesVendues?: number;
}
