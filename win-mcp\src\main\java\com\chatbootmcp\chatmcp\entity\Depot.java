package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * Depot entity representing warehouse/storage locations
 */
@Entity
@Table(name = "depot")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Depot {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code_depot", length = 50)
    private String codeDepot;

    @Column(name = "libelle_depot", length = 100)
    private String libelleDepot;

    @Column(name = "adr1")
    private String adr1;

    @Column(name = "adr2")
    private String adr2;

    @Column(name = "code_postal", length = 20)
    private String codePostal;

    @Column(name = "primaire")
    private Boolean primaire;

    @Column(name = "est_actif")
    private Boolean estActif = true;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;
}
