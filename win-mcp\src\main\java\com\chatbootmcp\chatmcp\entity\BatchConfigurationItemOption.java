package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * BatchConfigurationItemOption entity representing batch configuration item options
 */
@Entity
@Table(name = "batch_configuration_item_option")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchConfigurationItemOption {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "label")
    private String label;

    @Column(name = "value")
    private String value;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "batch_configuration_item_id")
    private BatchConfigurationItem batchConfigurationItem;
}
