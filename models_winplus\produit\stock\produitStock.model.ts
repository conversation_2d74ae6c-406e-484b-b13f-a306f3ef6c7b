import { NumFlagEmpl } from 'src/app/winpharm/enums/produit/NumFlagEmpl.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { Dci } from 'src/app/winpharm/models/produit/medical/dci.model';
import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { ProduitBase } from 'src/app/winpharm/models/produit/base/produitBase.model';
import { ProduitCodebarre } from 'src/app/winpharm/models/produit/base/produitCodebarre.model';
import { ProduitParametre } from 'src/app/winpharm/models/produit/base/produitParametre.model';
import { Taxe } from 'src/app/winpharm/models/common/taxe.model';
import { DetailRemboursementProduit } from '../base/detailRemboursementProduit.model';


export class ProduitStock { 
    audited?: boolean;
    categorie?: CategorieProduit;
    codeBarre?: string;
    codeBarres?: ProduitCodebarre[];
    codePrd?: string;
    dateFinCom?: any;
    datePeremption?: string;
    dci?: Dci;
    depot?: Depot;
    designation?: string;
    dosage?: string;
    estFabrique?: boolean;
    estMarche?: boolean;
    estOblgPrescription?: boolean;
    estPrinceps?: boolean;
    estPrixmarque?: boolean;
    estPsychotrope?: boolean;
    estRbrsblBase?: boolean;
    estStockable?: boolean;
    estToxique?: boolean;
    estTpaBase?: boolean;
    estVendable?: boolean;
    familleTarifaire?: FamilleTarifaire;
    flagEmpl?: NumFlagEmpl;
    formeGalenique?: FormeProduit;
    id?: number;
    idhash?: string;
    laboratoire?: Fournisseur;
    nomRacine?: string;
    numeroLot?: string;
    pbrH?: number;
    pbrP?: number;
    presentation?: string;
    prixAchatStd?: number;
    prixAchatTtc?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixValoTtc?: number;
    prixVenteStd?: number;
    prixVenteTtc?: number;
    produitBase?: ProduitBase;
    produitParametre?: ProduitParametre;
    totalStock?: number;
    stockId?: string;
    stockIndicateur?: string;
    tauxRemb?: number;
    tva?: Taxe;
    typeProcess?: string;
    userModifiable?: boolean;



    
    // Transient fields
    detailsRemboursements?: DetailRemboursementProduit[];
}

