package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.dto.request.LoginRequest;
import com.chatbootmcp.chatmcp.dto.response.AuthResponse;
import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import com.chatbootmcp.chatmcp.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    public AuthResponse login(LoginRequest loginRequest) {
        System.out.println("AuthService: Login attempt for user: " + loginRequest.getUsername());

        try {
            // Check if user exists in the database
            boolean userExists = userRepository.existsByUsername(loginRequest.getUsername());
            System.out.println("AuthService: User exists in database: " + userExists);

            if (userExists) {
                User user = userRepository.findByUsername(loginRequest.getUsername()).get();
                System.out.println("AuthService: Found user: " + user.getUsername() + ", ID: " + user.getId());
            }

            // Attempt authentication
            System.out.println("AuthService: Attempting authentication...");
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            System.out.println("AuthService: Authentication successful");
            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();

            // Generate JWT token
            System.out.println("AuthService: Generating JWT token...");
            String jwt = jwtUtil.generateToken(userDetails);

            User user = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            System.out.println("AuthService: Login successful for user: " + user.getUsername());
            return new AuthResponse(jwt, user.getUsername(), user.getId(), user.getEmail());
        } catch (Exception e) {
            System.out.println("AuthService: Login failed - " + e.getClass().getName() + ": " + e.getMessage());
            throw e;
        }
    }
}
