package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * BatchAdmin entity representing batch administration configuration
 */
@Entity
@Table(name = "batch_admin")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchAdmin {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", length = 50)
    private String code;

    @Column(name = "title")
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "statut")
    private Boolean statut;
}
