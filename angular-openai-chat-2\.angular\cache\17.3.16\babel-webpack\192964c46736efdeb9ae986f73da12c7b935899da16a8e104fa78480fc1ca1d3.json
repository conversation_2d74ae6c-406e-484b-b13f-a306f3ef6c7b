{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-conversation.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./chat-conversation.component.scss?ngResource\";\n// chat-conversation.component.ts\nimport { Component, Input, Output, EventEmitter, ViewChild } from '@angular/core';\nlet ChatConversationComponent = class ChatConversationComponent {\n  constructor() {\n    this.messages = [];\n    this.loading = false;\n    this.sendMessage = new EventEmitter();\n  }\n  ngOnChanges(changes) {\n    if (changes['messages']) {\n      setTimeout(() => {\n        this.scrollToBottom();\n      }, 0);\n    }\n  }\n  onEnterPress(event, inputElement) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessageManually(inputElement);\n    }\n  }\n  sendMessageManually(inputElement) {\n    if (inputElement.value.trim()) {\n      this.sendMessage.emit(inputElement.value);\n      inputElement.value = '';\n      // Reset textarea height\n      inputElement.style.height = '24px';\n    }\n  }\n  formatMessage(content) {\n    // Enhanced markdown-like formatting\n    let formatted = content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/`(.*?)`/g, '<code>$1</code>').replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\" target=\"_blank\">$1</a>').replace(/\\n/g, '<br>');\n    return formatted;\n  }\n  // Auto-resize textarea as user types\n  adjustTextareaHeight(textarea) {\n    textarea.style.height = '24px';\n    textarea.style.height = textarea.scrollHeight + 'px';\n  }\n  scrollToBottom() {\n    try {\n      const container = document.querySelector('.messages-container');\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n  static {\n    this.propDecorators = {\n      messages: [{\n        type: Input\n      }],\n      loading: [{\n        type: Input\n      }],\n      sendMessage: [{\n        type: Output\n      }],\n      messageInput: [{\n        type: ViewChild,\n        args: ['messageInput']\n      }]\n    };\n  }\n};\nChatConversationComponent = __decorate([Component({\n  selector: 'app-chat-conversation',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ChatConversationComponent);\nexport { ChatConversationComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ViewChild", "ChatConversationComponent", "constructor", "messages", "loading", "sendMessage", "ngOnChanges", "changes", "setTimeout", "scrollToBottom", "onEnterPress", "event", "inputElement", "key", "shift<PERSON>ey", "preventDefault", "sendMessageManually", "value", "trim", "emit", "style", "height", "formatMessage", "content", "formatted", "replace", "adjustTextareaHeight", "textarea", "scrollHeight", "container", "document", "querySelector", "scrollTop", "err", "console", "error", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-conversation\\chat-conversation.component.ts"], "sourcesContent": ["// chat-conversation.component.ts\r\nimport { Component, Input, Output, EventEmitter, ElementRef, ViewChild, OnChanges, SimpleChanges, ViewEncapsulation } from '@angular/core';\r\nimport { ChatMessage } from '../services/message-storage.service';\r\n\r\n@Component({\r\n  selector: 'app-chat-conversation',\r\n  templateUrl: './chat-conversation.component.html',\r\n  styleUrls: ['./chat-conversation.component.scss'],\r\n  // encapsulation: ViewEncapsulation.ShadowDom\r\n})\r\nexport class ChatConversationComponent implements OnChanges {\r\n  @Input() messages: ChatMessage[] = [];\r\n  @Input() loading = false;\r\n  @Output() sendMessage = new EventEmitter<string>();\r\n  \r\n  @ViewChild('messageInput') messageInput: ElementRef | undefined;\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['messages']) {\r\n      setTimeout(() => {\r\n        this.scrollToBottom();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  onEnterPress(event: KeyboardEvent, inputElement: HTMLTextAreaElement) {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessageManually(inputElement);\r\n    }\r\n  }\r\n\r\n  sendMessageManually(inputElement: HTMLTextAreaElement) {\r\n    if (inputElement.value.trim()) {\r\n      this.sendMessage.emit(inputElement.value);\r\n      inputElement.value = '';\r\n      \r\n      // Reset textarea height\r\n      inputElement.style.height = '24px';\r\n    }\r\n  }\r\n\r\n  formatMessage(content: string): string {\r\n    // Enhanced markdown-like formatting\r\n    let formatted = content\r\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n      .replace(/`(.*?)`/g, '<code>$1</code>')\r\n      .replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<a href=\"$2\" target=\"_blank\">$1</a>')\r\n      .replace(/\\n/g, '<br>');\r\n    \r\n    return formatted;\r\n  }\r\n\r\n  // Auto-resize textarea as user types\r\n  adjustTextareaHeight(textarea: HTMLTextAreaElement) {\r\n    textarea.style.height = '24px';\r\n    textarea.style.height = textarea.scrollHeight + 'px';\r\n  }\r\n\r\n  private scrollToBottom(): void {\r\n    try {\r\n      const container = document.querySelector('.messages-container');\r\n      if (container) {\r\n        container.scrollTop = container.scrollHeight;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA;AACA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAcC,SAAS,QAAqD,eAAe;AASnI,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAA/BC,YAAA;IACI,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,OAAO,GAAG,KAAK;IACd,KAAAC,WAAW,GAAG,IAAIN,YAAY,EAAU;EAyDpD;EArDEO,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACvBC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAC,YAAYA,CAACC,KAAoB,EAAEC,YAAiC;IAClE,IAAID,KAAK,CAACE,GAAG,KAAK,OAAO,IAAI,CAACF,KAAK,CAACG,QAAQ,EAAE;MAC5CH,KAAK,CAACI,cAAc,EAAE;MACtB,IAAI,CAACC,mBAAmB,CAACJ,YAAY,CAAC;IACxC;EACF;EAEAI,mBAAmBA,CAACJ,YAAiC;IACnD,IAAIA,YAAY,CAACK,KAAK,CAACC,IAAI,EAAE,EAAE;MAC7B,IAAI,CAACb,WAAW,CAACc,IAAI,CAACP,YAAY,CAACK,KAAK,CAAC;MACzCL,YAAY,CAACK,KAAK,GAAG,EAAE;MAEvB;MACAL,YAAY,CAACQ,KAAK,CAACC,MAAM,GAAG,MAAM;IACpC;EACF;EAEAC,aAAaA,CAACC,OAAe;IAC3B;IACA,IAAIC,SAAS,GAAGD,OAAO,CACpBE,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAChDA,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC,CACpCA,OAAO,CAAC,UAAU,EAAE,iBAAiB,CAAC,CACtCA,OAAO,CAAC,0BAA0B,EAAE,qCAAqC,CAAC,CAC1EA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IAEzB,OAAOD,SAAS;EAClB;EAEA;EACAE,oBAAoBA,CAACC,QAA6B;IAChDA,QAAQ,CAACP,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BM,QAAQ,CAACP,KAAK,CAACC,MAAM,GAAGM,QAAQ,CAACC,YAAY,GAAG,IAAI;EACtD;EAEQnB,cAAcA,CAAA;IACpB,IAAI;MACF,MAAMoB,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;MAC/D,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,SAAS,GAAGH,SAAS,CAACD,YAAY;MAC9C;IACF,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;IAClD;EACF;;;;cA1DCpC;MAAK;;cACLA;MAAK;;cACLC;MAAM;;cAENE,SAAS;QAAAoC,IAAA,GAAC,cAAc;MAAA;;;;AALdnC,yBAAyB,GAAAoC,UAAA,EANrCzC,SAAS,CAAC;EACT0C,QAAQ,EAAE,uBAAuB;EACjCC,QAAA,EAAAC,oBAAiD;;CAGlD,CAAC,C,EACWvC,yBAAyB,CA4DrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}