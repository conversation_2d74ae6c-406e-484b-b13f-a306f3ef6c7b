import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { ModePaiement } from 'src/app/winpharm/enums/common/ModePaiement.enum';

// import { Moment } from 'moment';

import { SyntheseDecaissement } from './syntheseDecaissement.model';
import { Banque } from '../../common/banque.model';


export class SituationDecaissement { 
    dateEcheance?: any;
    banque?: Banque;
    idDecaissement?: number;
    modeDecaissement?: ModePaiement;
    numeroFacture?: number;
    refDecaissement?: string;
    statut?: Statut;
    syntheseDecaissement?: SyntheseDecaissement;
}

