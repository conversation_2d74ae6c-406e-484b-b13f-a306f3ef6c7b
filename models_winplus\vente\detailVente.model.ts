
import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { DetailVenteExtTpa } from './detailVenteExtTpa.model';
import { TypeRemiseVente } from '../../enums/vente/TypeRemiseVente.enum';
import { Produit } from '../produit/base/produit.model';
import { LogCalculRemiseVenteEnum } from '../../enums/vente/LogCalculRemiseVente.enum';
import { TypeTableau } from '../../enums/produit/TypeTableau.enum';


export class DetailVente { 
    audited?: boolean;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codePrd?: string;
    datePeremption?: string;
    depot?: Depot;
    detailExtTPA?: DetailVenteExtTpa;
    dsgnPrd?: string;
    id?: number;
    mntLigneBrutHt?: number;
    mntLigneBrutTtc?: number;
    mntLigneEncaisse?: number;
    mntLigneMargeEffHt?: number;
    mntLigneMargeEffTtc?: number;
    mntLigneMargeStdHt?: number;
    mntLigneMargeStdTtc?: number;
    mntLigneNetHt?: number;
    mntLigneNetTtc?: number;
    mntLigneRemiseHt?: number;
    mntLigneRemiseTtc?: number;
    mntLigneTva?: number;
    mntLigneVenteDu?: number;
    mntUnitRemiseHt?: number;
    mntUnitRemiseTtc?: number;
    numLigne?: number;
    numLot?: string;
    numOrdonnancier?: number;
    numVente?: number;
    ordonnancier?: boolean;
    pbrH?: number;
    pbrP?: number;
    prd?: Produit;
    prixAchatHt?: number;
    prixAchatStd?: number;
    prixAchatTtc?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixUnitHt?: number;
    prixUnitTtc?: number;
    prixValoTtc?: number;
    prixVenteBrutOriginalTtc?: number;
    prixVenteBrutTtc?: number;
    prixVenteStd?: number;
    qtLivre?: number;
    qtVente?: number;
    stockId?: string;
    tauxMarge?: number;
    tauxRemise?: number;
    tauxTva?: number;
    userModifiable?: boolean;

    tauxRembPrd?: number;






	typeRemiseAttr?: TypeRemiseVente;

	logCalculRemise?: LogCalculRemiseVenteEnum;   // du taux remise
	
	estRemiseTronq?: boolean;       // cas tiers payant et mnt remise depasse mnt net à payer





    

    /***************************  transient/calculable fields  *********************************/

    tableau?: TypeTableau;      // TODO: a supprimer  et à utiliser celle produit.produitBase

    stockIndicateur?: string;
    imageQteStock?: number;


    qtePromis?: number;

    promisSolde?: boolean;



    autoriseSaisieRemise?: boolean;


    lockedForRemiseDispatch?: boolean;

    // backup
    backupTauxRemiseAutomatique?: number;
    backupTypeRemiseAttr?: TypeRemiseVente;
    backupLogCalculRemise?: LogCalculRemiseVenteEnum;   // du taux remise auto


}
