package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Produit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProduitRepository extends JpaRepository<Produit, Long> {
    
    Optional<Produit> findByCodePrd(String codePrd);
    
    Optional<Produit> findByCodeBarre(String codeBarre);
    
    List<Produit> findByEstVendableTrue();
    
    Page<Produit> findByEstVendableTrue(Pageable pageable);
    
    @Query("SELECT p FROM Produit p WHERE p.estVendable = true AND " +
           "(LOWER(p.designation) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.codePrd) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.codeBarre) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Produit> findVendableProductsBySearchTerm(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT p FROM Produit p WHERE p.totalStock <= :seuil AND p.estStockable = true")
    List<Produit> findProductsWithLowStock(@Param("seuil") java.math.BigDecimal seuil);
    
    @Query("SELECT p FROM Produit p WHERE p.estPsychotrope = true")
    List<Produit> findPsychotropicProducts();
    
    @Query("SELECT p FROM Produit p WHERE p.estOblgPrescription = true")
    List<Produit> findPrescriptionRequiredProducts();
    
    @Query("SELECT p FROM Produit p ORDER BY p.totalStock DESC")
    Page<Produit> findProductsOrderByStockDesc(Pageable pageable);
}
