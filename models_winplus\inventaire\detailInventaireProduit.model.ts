import { NumFlagEmpl } from 'src/app/winpharm/enums/produit/NumFlagEmpl.enum';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Produit } from 'src/app/winpharm/models/produit/base/produit.model';
import { Rayon } from 'src/app/winpharm/models/produit/base/rayon.model';
import { Stock } from 'src/app/winpharm/models/produit/stock/stock.model';


export class DetailInventaireProduit {
    audited?: boolean;
    categorie?: CategorieProduit;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    datePeremptionFin?: string;
    datePeremptionInit?: string;
    depot?: Depot;
    dsgnPrd?: string;
    flagEmpl?: NumFlagEmpl;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    laboratoire?: Fournisseur;
    numeroLotFin?: string;
    numeroLotInit?: string;
    prixAchatTtc?: number;
    prixVenteTtcFin?: number;
    prixVenteTtcInit?: number;
    produit?: Produit;
    qteStkFin?: number;
    qteStkInit?: number;
    rayon?: Rayon;
    seuilStkMax?: number;
    seuilStkMin?: number;
    stock?: Stock;
    tauxMarge?: number;
    userModifiable?: boolean;

    //? transiant attribute for selection handling (edit Inventaire)
    checked?: boolean;

    dateDernModif?: string
}

