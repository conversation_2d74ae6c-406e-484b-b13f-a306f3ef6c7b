package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * Operateur entity representing system operators/users
 */
@Entity
@Table(name = "operateur")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Operateur {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "username", length = 50)
    private String username;

    @Column(name = "firstname", length = 100)
    private String firstname;

    @Column(name = "lastname", length = 100)
    private String lastname;

    @Column(name = "email", length = 100)
    private String email;

    @Column(name = "password")
    private String password;

    @Column(name = "actif")
    private Boolean actif;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;
}
