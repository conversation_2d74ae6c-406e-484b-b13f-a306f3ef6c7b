{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-history.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./chat-history.component.scss?ngResource\";\n// chat-history.component.ts\nimport { Component } from '@angular/core';\nlet ChatHistoryComponent = class ChatHistoryComponent {};\nChatHistoryComponent = __decorate([Component({\n  selector: 'app-chat-history',\n  standalone: true,\n  imports: [],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ChatHistoryComponent);\nexport { ChatHistoryComponent };", "map": {"version": 3, "names": ["Component", "ChatHistoryComponent", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-history\\chat-history.component.ts"], "sourcesContent": ["// chat-history.component.ts\r\nimport { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-chat-history',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './chat-history.component.html',\r\n  styleUrl: './chat-history.component.scss'\r\n})\r\nexport class ChatHistoryComponent {\r\n\r\n}\r\n"], "mappings": ";;;AAAA;AACA,SAASA,SAAS,QAAQ,eAAe;AASlC,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB,GAEhC;AAFYA,oBAAoB,GAAAC,UAAA,EAPhCF,SAAS,CAAC;EACTG,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,EAAE;EACXC,QAAA,EAAAC,oBAA4C;;CAE7C,CAAC,C,EACWN,oBAAoB,CAEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}