package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * ProduitBase entity representing base product information
 */
@Entity
@Table(name = "produit_base")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProduitBase {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "nom_racine")
    private String nomRacine;

    @Column(name = "psychotrope")
    private Boolean psychotrope;

    @Column(name = "tableau", length = 50)
    private String tableau;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @Column(name = "atc_id")
    private Long atcId;

    @Column(name = "famille_medicamenteuse_id")
    private Long familleMedicamenteuseId;
}
