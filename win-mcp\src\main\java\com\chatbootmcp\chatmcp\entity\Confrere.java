package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * Confrere entity representing pharmacy colleagues/partners
 */
@Entity
@Table(name = "confrere")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Confrere {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "nom")
    private String nom;

    @Column(name = "prenom")
    private String prenom;

    @Column(name = "raison_sociale")
    private String raisonSociale;

    @Column(name = "nom_complet")
    private String nomComplet;

    @Column(name = "adr1")
    private String adr1;

    @Column(name = "adr2")
    private String adr2;

    @Column(name = "num_telephone", length = 50)
    private String numTelephone;

    @Column(name = "gsm", length = 50)
    private String gsm;

    @Column(name = "email", length = 100)
    private String email;

    @Column(name = "num_ice", length = 50)
    private String numIce;

    @Column(name = "solde", precision = 15, scale = 2)
    private BigDecimal solde;

    @Column(name = "type_confere", length = 50)
    private String typeConfere;

    @Column(name = "type_tiers", length = 50)
    private String typeTiers;

    @Column(name = "est_actif")
    private Boolean estActif;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @Column(name = "ville_id")
    private Long villeId;
}
