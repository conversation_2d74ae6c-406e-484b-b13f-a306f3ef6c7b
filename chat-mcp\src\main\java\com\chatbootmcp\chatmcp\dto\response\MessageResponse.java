package com.chatbootmcp.chatmcp.dto.response;

import com.chatbootmcp.chatmcp.entity.Message;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class MessageResponse {
    private Long id;
    private Long conversationId;
    private String content;
    private String role;
    private LocalDateTime timestamp;
    
    public MessageResponse(Message message) {
        this.id = message.getId();
        this.conversationId = message.getConversation().getId();
        this.content = message.getContent();
        this.role = message.getRole().toString();
        this.timestamp = message.getTimestamp();
    }
}
