


export class SyntheseFactureAchat { 
    tauxRemiseFacture?: number;
    totalMontantFactureAchatBrutCalculeTtc?: number;
    totalMontantFactureAchatBrutEffectifTtc?: number;
    totalMontantFactureAchatBrutHt?: number;
    totalMontantFactureAchatNetCalculeTtc?: number;
    totalMontantFactureAchatNetEffectifTtc?: number;
    totalMontantFactureAchatNetHt?: number;
    totalMontantFactureAchatRegle?: number;
    totalMontantFactureRemise?: number;
    totalMontantFactureTva?: number;
    totalNombreFactureAchats?: number;
}
