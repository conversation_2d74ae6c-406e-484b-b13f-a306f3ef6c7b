
import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Taxe } from 'src/app/winpharm/models/common/taxe.model';
import { Dci } from '../../produit/medical/dci.model';


export class StatistiqueVentePartieProduit { 
    categorie?: CategorieProduit;
    codeProduit?: string;
    datePeremption?: string;
    designationProduit?: string;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    dci?: Dci;
    numeroLot?: string;
    ppv?: number;
    stock?: number;
    tva?: Taxe;
}
