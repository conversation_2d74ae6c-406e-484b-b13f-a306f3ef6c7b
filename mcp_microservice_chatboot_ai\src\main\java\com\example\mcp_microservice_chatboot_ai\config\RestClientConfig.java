package com.example.mcp_microservice_chatboot_ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestClient;

/**
 * Configuration class for RestClient.
 */
@Configuration
public class RestClientConfig {

    /**
     * Creates a RestClient.Builder bean.
     * 
     * @return A RestClient.Builder bean
     */
    @Bean
    public RestClient.Builder restClientBuilder() {
        return RestClient.builder();
    }
}
