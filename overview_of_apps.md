# Chat MCP Backend

This is a Spring Boot MCP (Microservice Control Plane) backend for a chat application with mock statistical data.
A Spring Boot backend that serves as the core application server. It handles user authentication, data storage, and conversation management. This service maintains the database of users, conversations, and messages, providing REST APIs for the frontend to interact with.


Overview:

Core Backend Service: Handles user data, authentication, and conversation management
Technology: Spring Boot application with REST APIs
Port: 8080 with /api context path
Database: Stores users, conversations, and messages
Key Components:

Authentication System: JWT-based auth with login endpoints
User Management: User profiles and permissions
Conversation System: Creates and manages chat conversations
Message Storage: Persists all chat messages
Mock AI Service: Currently uses template responses with random data
WebSocket Support: For real-time message delivery


Package Structure:

config: Application configurations (CORS, security, WebSocket)
controller: REST endpoints for auth, conversations, messages
dto: Data transfer objects for requests/responses
entity: Database models (User, Conversation, Message)
repository: Data access layer
service: Business logic including AIService with mock responses
exception: Custom exception handling


# MCP Microservice Chatboot AI

An AI-focused microservice built with Spring Boot and Spring AI. It integrates with OpenAI's GPT models to provide intelligent chat capabilities. This service offers tools and resources through the Microservice Control Plane (MCP) architecture and communicates with the main backend to enhance conversations with AI capabilities.

Overview:

AI Microservice: Provides AI capabilities to the main backend
Technology: Spring Boot with Spring AI integration
Port: 8081
AI Model: OpenAI GPT-4o with configurable parameters
Key Components:

OpenAI Integration: Connects to OpenAI's models via Spring AI
MCP Server: Implements Spring AI's Microservice Control Plane
Tool System: Provides AI function calling capabilities (getWeather, getUserInfo)
Resource Endpoints: System info and help documentation
Chat Processing: Handles AI chat requests with system prompts
API Client: WebClient for communicating with chat-mcp
Package Structure:

config: MCP configurations (tools, resources, prompts, OpenAI)
controller: REST endpoints for chat interactions
model: Data models and DTOs
service: Business logic including AiChatService and ChatMcpApiService
resources: Application properties and configurations


# Angular OpenAI Chat Client

An Angular 17 frontend application that provides the user interface for the chat system. It features a modern chat widget with conversation management and connects to the backend services. The UI supports different conversation views, suggested questions, and direct interaction with the chat system.

Overview:

Frontend Application: User interface for the chat system
Technology: Angular 17
Port: Likely 4200 (Angular default)
Features: Chat widget, conversation management, message display
Key Components:

Chat Widget: Main UI component for user interactions
OpenAI Service: Direct integration with OpenAI Assistants API
Thread Management: Maintains conversation threads with OpenAI
Message Storage: Caches messages for display and history
UI Components: Modern interface with conversation tabs
Architecture Details:

Uses Angular's service architecture for API communication
Implements reactive programming with RxJS observables
Maintains conversation state and history
Supports thread reuse for continuous conversations
Handles loading states and error conditions






+------------------------+         +---------------------------+         +-------------------+
|                        |  HTTP   |                           |  HTTP   |                   |
|  angular-openai-chat-2 | ------> | mcp_microservice_chatboot_ai | ------> |     chat-mcp      |
|  (Angular Frontend)    | <------ |    (AI Microservice)     | <------ |  (Core Backend)   |
|                        |         |                           |         |                   |
+------------------------+         +---------------------------+         +-------------------+
         |                                  |                              |
         | User Interface                   | AI Processing                | Data Storage
         |                                  |                              |
         v                                  v                              v
+------------------------+         +---------------------------+         +-------------------+
| - Login/Authentication | <-----> | - OpenAI Integration      | <-----> | - User Database   |
| - Chat UI              |         | - GPT-4o Model            |         | - Conversations   |
| - Message Display      |         | - Tool Callbacks          |         | - Messages        |
| - Conversation History |         | - Resource Endpoints      |         | - Authentication  |
+------------------------+         +---------------------------+         +-------------------+

# Flow Description:

1. User Interaction:
    User interacts with the Angular frontend
    Frontend sends messages to mcp_microservice_chatboot_ai
2. AI Processing:
    mcp_microservice_chatboot_ai processes the message
    If user data is needed, it communicates with chat-mcp
    AI generates a response using OpenAI integration
3. Data Retrieval:
    mcp_microservice_chatboot_ai requests user data from chat-mcp
    chat-mcp provides the necessary data from its database
    This data helps contextualize AI responses
4. Response Flow:
    mcp_microservice_chatboot_ai sends the final response to the frontend
    Frontend displays the response to the user