# 🧪 Complete MCP System Test Script
# This script tests all possible questions with the MCP system

param(
    [string]$BaseUrl = "http://localhost:8081/api/chat",
    [string]$Username = "user1"
)

Write-Host "🚀 Starting MCP System Comprehensive Test" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "Username: $Username" -ForegroundColor Yellow
Write-Host "=" * 60

$headers = @{"Content-Type" = "application/json"}
$testCounter = 1

function Test-Question {
    param(
        [string]$Question,
        [string]$Category,
        [int]$TestNumber
    )

    $conversationId = "test$('{0:D3}' -f $TestNumber)"

    Write-Host "🔍 Test $TestNumber - $Category" -ForegroundColor Cyan
    Write-Host "Question: $Question" -ForegroundColor White

    try {
        # Ask the question
        $body1 = @{
            content = $Question
            conversationId = $conversationId
            username = $Username
        } | ConvertTo-Json

        $response1 = Invoke-RestMethod -Uri $BaseUrl -Method POST -Headers $headers -Body $body1
        Write-Host "Response: $($response1.content)" -ForegroundColor Gray

        # If it asks for source selection, choose option 1 (database)
        if ($response1.content -like "*1️⃣*" -or $response1.content -like "*Souhaitez-vous*") {
            Start-Sleep -Seconds 1

            $body2 = @{
                content = "1"
                conversationId = $conversationId
                username = $Username
            } | ConvertTo-Json

            $response2 = Invoke-RestMethod -Uri $BaseUrl -Method POST -Headers $headers -Body $body2
            Write-Host "Final Answer: $($response2.content)" -ForegroundColor Green
        }

        Write-Host "✅ Test $TestNumber completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Test $TestNumber failed: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host "-" * 60
    Start-Sleep -Seconds 2
}

# 👤 PERSONAL INFORMATION TESTS
Write-Host "👤 TESTING PERSONAL INFORMATION" -ForegroundColor Magenta

Test-Question "Quel est mon nom ?" "Personal Info - Name" $testCounter++
Test-Question "Quel est mon nom complet ?" "Personal Info - Full Name" $testCounter++
Test-Question "Quelle est mon adresse email ?" "Personal Info - Email" $testCounter++
Test-Question "Quelle est mon adresse ?" "Personal Info - Address" $testCounter++
Test-Question "Quel est mon numéro de téléphone ?" "Personal Info - Phone" $testCounter++
Test-Question "Dans quelle ville j'habite ?" "Personal Info - City" $testCounter++
Test-Question "Quel est mon code postal ?" "Personal Info - Postal Code" $testCounter++

# 💼 PROFESSIONAL INFORMATION TESTS
Write-Host "💼 TESTING PROFESSIONAL INFORMATION" -ForegroundColor Magenta

Test-Question "Quel est mon métier ?" "Professional - Job Title" $testCounter++
Test-Question "Où je travaille ?" "Professional - Company" $testCounter++
Test-Question "Quel est mon département ?" "Professional - Department" $testCounter++
Test-Question "Quel est mon ID employé ?" "Professional - Employee ID" $testCounter++

# 💰 FINANCIAL INFORMATION TESTS
Write-Host "💰 TESTING FINANCIAL INFORMATION" -ForegroundColor Magenta

Test-Question "Quel est mon solde ?" "Financial - Balance" $testCounter++
Test-Question "Quel est mon solde actuel ?" "Financial - Current Balance" $testCounter++
Test-Question "Quelle est ma limite de crédit ?" "Financial - Credit Limit" $testCounter++
Test-Question "Quelle est ma méthode de paiement ?" "Financial - Payment Method" $testCounter++
Test-Question "Quand expire ma carte ?" "Financial - Card Expiry" $testCounter++
Test-Question "Quand est ma prochaine échéance ?" "Financial - Next Payment" $testCounter++

# 📄 SUBSCRIPTION TESTS
Write-Host "📄 TESTING SUBSCRIPTION INFORMATION" -ForegroundColor Magenta

Test-Question "Quel est mon statut d'abonnement ?" "Subscription - Status" $testCounter++
Test-Question "Quel type d'abonnement j'ai ?" "Subscription - Type" $testCounter++
Test-Question "Combien coûte mon abonnement ?" "Subscription - Price" $testCounter++
Test-Question "Quand a commencé mon abonnement ?" "Subscription - Start Date" $testCounter++
Test-Question "Quand se termine mon abonnement ?" "Subscription - End Date" $testCounter++

# 🧾 TRANSACTION TESTS
Write-Host "🧾 TESTING TRANSACTION INFORMATION" -ForegroundColor Magenta

Test-Question "Quelles sont mes dernières transactions ?" "Transactions - Recent" $testCounter++
Test-Question "Mes transactions récentes ?" "Transactions - Recent Alt" $testCounter++
Test-Question "Combien j'ai dépensé ?" "Transactions - Total Spent" $testCounter++
Test-Question "Total de mes débits ?" "Transactions - Total Debits" $testCounter++
Test-Question "Total de mes crédits ?" "Transactions - Total Credits" $testCounter++

# 📋 INVOICE TESTS
Write-Host "📋 TESTING INVOICE INFORMATION" -ForegroundColor Magenta

Test-Question "Quelles sont mes factures ?" "Invoices - List" $testCounter++
Test-Question "Mes dernières factures ?" "Invoices - Recent" $testCounter++
Test-Question "Combien je dois ?" "Invoices - Amount Due" $testCounter++
Test-Question "Mes factures impayées ?" "Invoices - Unpaid" $testCounter++
Test-Question "Total de mes factures payées ?" "Invoices - Paid Total" $testCounter++

# 🛒 ORDER TESTS
Write-Host "🛒 TESTING ORDER INFORMATION" -ForegroundColor Magenta

Test-Question "Quelles sont mes commandes ?" "Orders - List" $testCounter++
Test-Question "Mes dernières commandes ?" "Orders - Recent" $testCounter++
Test-Question "Combien de commandes j'ai ?" "Orders - Count" $testCounter++
Test-Question "Total de mes commandes ?" "Orders - Total Amount" $testCounter++
Test-Question "Mes commandes actives ?" "Orders - Active" $testCounter++

# 📊 ACCOUNT STATISTICS TESTS
Write-Host "📊 TESTING ACCOUNT STATISTICS" -ForegroundColor Magenta

Test-Question "Quel type de compte j'ai ?" "Account - Type" $testCounter++
Test-Question "Mon profil est-il complet ?" "Account - Profile Completion" $testCounter++
Test-Question "Quelle est ma langue préférée ?" "Account - Language" $testCounter++
Test-Question "Quel est mon fuseau horaire ?" "Account - Timezone" $testCounter++
Test-Question "Ma date de naissance ?" "Account - Birth Date" $testCounter++

# 🔍 COMPLEX QUESTIONS TESTS
Write-Host "🔍 TESTING COMPLEX QUESTIONS" -ForegroundColor Magenta

Test-Question "Résumé de mon compte ?" "Complex - Account Summary" $testCounter++
Test-Question "Toutes mes informations ?" "Complex - All Info" $testCounter++
Test-Question "Mon profil complet ?" "Complex - Complete Profile" $testCounter++
Test-Question "Vue d'ensemble de mon compte ?" "Complex - Account Overview" $testCounter++

# 🎯 FRIENDLY MESSAGES TESTS (Should not ask for source)
Write-Host "🎯 TESTING FRIENDLY MESSAGES" -ForegroundColor Magenta

function Test-FriendlyMessage {
    param(
        [string]$Message,
        [int]$TestNumber
    )

    $conversationId = "friendly$('{0:D3}' -f $TestNumber)"

    Write-Host "🔍 Test $TestNumber - Friendly Message" -ForegroundColor Cyan
    Write-Host "Message: $Message" -ForegroundColor White

    try {
        $body = @{
            content = $Message
            conversationId = $conversationId
            username = $Username
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri $BaseUrl -Method POST -Headers $headers -Body $body
        Write-Host "Response: $($response.content)" -ForegroundColor Green

        if ($response.content -like "*1️⃣*" -or $response.content -like "*Souhaitez-vous*") {
            Write-Host "⚠️  Warning: Friendly message triggered source selection" -ForegroundColor Yellow
        } else {
            Write-Host "✅ Correctly identified as friendly message" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "❌ Test $TestNumber failed: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host "-" * 60
    Start-Sleep -Seconds 1
}

Test-FriendlyMessage "Bonjour" $testCounter++
Test-FriendlyMessage "Salut" $testCounter++
Test-FriendlyMessage "Hello" $testCounter++
Test-FriendlyMessage "Bonsoir" $testCounter++
Test-FriendlyMessage "Comment allez-vous ?" $testCounter++

Write-Host "🎉 ALL TESTS COMPLETED!" -ForegroundColor Green
Write-Host "Total tests run: $($testCounter - 1)" -ForegroundColor Yellow
Write-Host "Check the responses above to verify real database integration." -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 EXPECTED REAL DATA RESPONSES:" -ForegroundColor Magenta
Write-Host "- Name: Test User1" -ForegroundColor White
Write-Host "- Balance: 1250.0 €" -ForegroundColor White
Write-Host "- Company: Pharmacie Centrale" -ForegroundColor White
Write-Host "- Job Title: Pharmacien" -ForegroundColor White
Write-Host "- Email: <EMAIL>" -ForegroundColor White
Write-Host "- Address: 123 Rue de la Santé" -ForegroundColor White
Write-Host "- City: Paris" -ForegroundColor White
Write-Host "- Subscription: Active" -ForegroundColor White
Write-Host ""
Write-Host "🔍 If you see these values, the real database integration is working!" -ForegroundColor Green
