package com.example.mcp_microservice_chatboot_ai.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for conversation creation requests.
 */

/**
 * Role: Data Transfer Object for conversation creation requests
    Purpose:
    Carries conversation title and user information
    Used when creating new conversations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationRequest {
    
    /**
     * The title of the conversation.
     */
    private String title;
    
    /**
     * The username of the user creating the conversation.
     */
    private String username;
}
