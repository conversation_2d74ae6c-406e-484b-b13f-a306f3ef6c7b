package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "fournisseurs")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Fournisseur {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "code_fournisseur")
    private String codeFournisseur;
    
    @Column(name = "raison_sociale")
    private String raisonSociale;
    
    @Column(name = "nom_commercial")
    private String nomCommercial;
    
    @Column(name = "adresse1")
    private String adresse1;
    
    @Column(name = "adresse2")
    private String adresse2;
    
    @Column(name = "ville")
    private String ville;
    
    @Column(name = "code_postal")
    private String codePostal;
    
    @Column(name = "pays")
    private String pays;
    
    @Column(name = "telephone")
    private String telephone;
    
    @Column(name = "fax")
    private String fax;
    
    @Column(name = "email")
    private String email;
    
    @Column(name = "site_web")
    private String siteWeb;
    
    @Column(name = "num_ice")
    private String numIce;
    
    @Column(name = "num_rc")
    private String numRc;
    
    @Column(name = "num_patente")
    private String numPatente;
    
    @Column(name = "num_cnss")
    private String numCnss;
    
    @Column(name = "est_actif")
    private Boolean estActif;
    
    @Column(name = "est_laboratoire")
    private Boolean estLaboratoire;
    
    @Column(name = "delai_livraison")
    private Integer delaiLivraison;
    
    @Column(name = "delai_paiement")
    private Integer delaiPaiement;
    
    @Column(name = "taux_remise_generale", precision = 5, scale = 2)
    private BigDecimal tauxRemiseGenerale;
    
    @Column(name = "plafond_credit", precision = 19, scale = 2)
    private BigDecimal plafondCredit;
    
    @Column(name = "solde_fournisseur", precision = 19, scale = 2)
    private BigDecimal soldeFournisseur;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // Relationships
    @OneToMany(mappedBy = "fournisseur", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<FactureAchat> facturesAchat;
}
