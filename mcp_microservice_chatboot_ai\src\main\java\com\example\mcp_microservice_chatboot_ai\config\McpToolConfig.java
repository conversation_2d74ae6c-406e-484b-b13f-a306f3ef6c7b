package com.example.mcp_microservice_chatboot_ai.config;

import com.example.mcp_microservice_chatboot_ai.service.AiChatService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Configuration class for MCP tools.
 * This class sets up the tool callbacks for the MCP server.
 */

/**
 * Role: Configuration class for MCP tools
 *    Purpose:
 *    Sets up tool callbacks for the MCP server
 *    Registers AI chat service's tools with the MCP server
 *    Enables tool invocation by language models
 */
@Configuration
public class McpToolConfig {

    private final AiChatService aiChatService;

    @Autowired
    public McpToolConfig(AiChatService aiChatService) {
        this.aiChatService = aiChatService;
    }

    /**
     * Creates OpenAI chat options with function calling enabled.
     *
     * @return OpenAiChatOptions with function calling
     */
    @Bean
    public OpenAiChatOptions openAiChatOptions() {
        // Define the weather function
        Map<String, Object> weatherFunction = new HashMap<>();
        weatherFunction.put("name", "getWeather");
        weatherFunction.put("description", "Get weather information for a location");

        Map<String, Object> weatherParams = new HashMap<>();
        weatherParams.put("type", "object");

        Map<String, Object> weatherProperties = new HashMap<>();
        Map<String, Object> locationProp = new HashMap<>();
        locationProp.put("type", "string");
        locationProp.put("description", "The location to get weather for");
        weatherProperties.put("location", locationProp);

        weatherParams.put("properties", weatherProperties);
        weatherParams.put("required", new String[]{"location"});
        weatherFunction.put("parameters", weatherParams);

        // Define the user info function
        Map<String, Object> userInfoFunction = new HashMap<>();
        userInfoFunction.put("name", "getUserInfo");
        userInfoFunction.put("description", "Get information about a user");

        Map<String, Object> userInfoParams = new HashMap<>();
        userInfoParams.put("type", "object");

        Map<String, Object> userInfoProperties = new HashMap<>();
        Map<String, Object> usernameProp = new HashMap<>();
        usernameProp.put("type", "string");
        usernameProp.put("description", "The username to get information for");
        userInfoProperties.put("username", usernameProp);

        userInfoParams.put("properties", userInfoProperties);
        userInfoParams.put("required", new String[]{"username"});
        userInfoFunction.put("parameters", userInfoParams);

        // Create OpenAI chat options with functions
        // Convert the function maps to JSON strings
        ObjectMapper objectMapper = new ObjectMapper();
        Set<String> functionDefinitions = new HashSet<>();
        try {
            functionDefinitions.add(objectMapper.writeValueAsString(weatherFunction));
            functionDefinitions.add(objectMapper.writeValueAsString(userInfoFunction));
        } catch (Exception e) {
            throw new RuntimeException("Error converting functions to JSON", e);
        }

        return OpenAiChatOptions.builder()
                .withModel("gpt-4o")
                .withTemperature(0.7f)
                .withMaxTokens(2000)
                .withFunctions(functionDefinitions)
                .build();
    }
}
