package com.chatbootmcp.chatmcp.dto.response;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chatbootmcp.chatmcp.entity.UserData;
import lombok.Data;
import java.io.IOException;
import java.time.LocalDateTime;

@Data
public class UserDataResponse {
    private Long id;
    private String dataType;
    private JsonNode dataValue;
    private LocalDateTime lastUpdated;
    
    public UserDataResponse(UserData userData) {
        this.id = userData.getId();
        this.dataType = userData.getDataType();
        this.lastUpdated = userData.getLastUpdated();
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.dataValue = mapper.readTree(userData.getDataValue());
        } catch (IOException e) {
            this.dataValue = null;
        }
    }
}
