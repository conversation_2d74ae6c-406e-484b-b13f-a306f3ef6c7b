package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Order;
import com.chatbootmcp.chatmcp.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    List<Order> findByUserOrderByOrderDateDesc(User user);
    List<Order> findByUserAndStatusOrderByOrderDateDesc(User user, String status);
    Optional<Order> findByOrderNumber(String orderNumber);

    @Query("SELECT o FROM Order o WHERE o.user = :user AND o.orderDate >= :startDate ORDER BY o.orderDate DESC")
    List<Order> findByUserAndOrderDateAfterOrderByOrderDateDesc(@Param("user") User user, @Param("startDate") LocalDateTime startDate);

    @Query("SELECT SUM(o.totalAmount) FROM Order o WHERE o.user = :user AND o.paymentStatus = 'PAID'")
    Double getTotalOrderAmountForUser(@Param("user") User user);

    @Query("SELECT COUNT(o) FROM Order o WHERE o.user = :user")
    Long getTotalOrderCountForUser(@Param("user") User user);

    @Query("SELECT AVG(o.totalAmount) FROM Order o WHERE o.user = :user AND o.paymentStatus = 'PAID'")
    Double getAverageOrderValueForUser(@Param("user") User user);

    List<Order> findTop5ByUserOrderByOrderDateDesc(User user);

    @Query("SELECT o FROM Order o WHERE o.user = :user AND o.status IN ('PENDING', 'PROCESSING', 'SHIPPED') ORDER BY o.orderDate DESC")
    List<Order> findActiveOrdersForUser(@Param("user") User user);
}
