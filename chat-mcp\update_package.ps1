# PowerShell script to update package names

# Create directories
$directories = @(
    "config",
    "controller",
    "dto/request",
    "dto/response",
    "entity",
    "exception",
    "repository",
    "security",
    "service",
    "util"
)

foreach ($dir in $directories) {
    $path = "src/main/java/com/chatbootmcp/chatmcp/$dir"
    if (-not (Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force
    }
}

# Get all Java files from the old package
$javaFiles = Get-ChildItem -Path "src/main/java/com/chatbootmcp/chatmcp" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    # Skip the main application class as we've already created it
    if ($file.Name -eq "ChatMcpApplication.java") {
        continue
    }
    
    # Get relative path from the old package root
    $relativePath = $file.FullName -replace [regex]::Escape("$PWD\src\main\java\com\chatbootmcp\chatmcp\"), ""
    
    # Create the target directory if it doesn't exist
    $targetDir = [System.IO.Path]::GetDirectoryName("$PWD\src\main\java\com\chatbootmcp\chatmcp\$relativePath")
    if (-not (Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force
    }
    
    # Read the file content
    $content = Get-Content $file.FullName -Raw
    
    # Replace the package declaration
    $content = $content -replace "package com\.chatbootmcp\.chatmcp", "package com.chatbootmcp.chatmcp"
    
    # Replace import statements
    $content = $content -replace "import com\.chatbootmcp\.chatmcp", "import com.chatbootmcp.chatmcp"
    
    # Write the updated content to the new file
    $targetPath = "$PWD\src\main\java\com\chatbootmcp\chatmcp\$relativePath"
    $content | Out-File -FilePath $targetPath -Encoding utf8
}

Write-Host "Package update completed successfully!"
