package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.math.BigDecimal;

@Entity
@Table(name = "detail_facture_achats")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailFactureAchat {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "qte_commandee", precision = 19, scale = 3)
    private BigDecimal qteCommandee;
    
    @Column(name = "qte_livree", precision = 19, scale = 3)
    private BigDecimal qteLivree;
    
    @Column(name = "prix_unitaire_ht", precision = 19, scale = 2)
    private BigDecimal prixUnitaireHt;
    
    @Column(name = "prix_unitaire_ttc", precision = 19, scale = 2)
    private BigDecimal prixUnitaireTtc;
    
    @Column(name = "montant_ht", precision = 19, scale = 2)
    private BigDecimal montantHt;
    
    @Column(name = "montant_ttc", precision = 19, scale = 2)
    private BigDecimal montantTtc;
    
    @Column(name = "taux_remise", precision = 5, scale = 2)
    private BigDecimal tauxRemise;
    
    @Column(name = "montant_remise", precision = 19, scale = 2)
    private BigDecimal montantRemise;
    
    @Column(name = "taux_tva", precision = 5, scale = 2)
    private BigDecimal tauxTva;
    
    @Column(name = "montant_tva", precision = 19, scale = 2)
    private BigDecimal montantTva;
    
    @Column(name = "num_ligne")
    private Integer numLigne;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facture_achat_id")
    private FactureAchat factureAchat;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;
}
