import { SensEchange } from 'src/app/winpharm/enums/echange/SensEchange.enum';
import { SousStatutEchange } from 'src/app/winpharm/enums/echange/SousStatutEchange.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Produit } from '../produit/base/produit.model';


export class DetailEchangeProduit {
    audited?: boolean;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codePrd?: string;
    ctgr?: CategorieProduit;
    dateEchange?: any;
    datePeremption?: string;
    depot?: Depot;
    dsgnPrd?: string;
    frm?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    labo?: Fournisseur;
    mntLigneEchangeHt?: number;
    mntLigneEchangeTtc?: number;
    mntLignePrixAchatStd?: number;
    mntLignePrixVenteStd?: number;
    mntLigneRemiseHt?: number;
    mntLigneRemiseTtc?: number;
    numLot?: string;
    pbrH?: number;
    pbrP?: number;
    prixAchatStd?: number;
    prixAchatTtc?: number
    prixFabHt?: number;
    prixHosp?: number;
    prixUnit?: number;
    prixVenteStd?: number;
    prixVenteTtc?: number;
    produit?: Produit;
    qt?: number;
    qtUg?: number;
    sensEchange?: SensEchange;
    sousStatut?: SousStatutEchange;
    stockId?: string;
    tauxMarge?: number;
    tauxRemise?: number;
    tauxTva?: number;
    userModifiable?: boolean;

    //
    typePrix?: string
}
