package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * OrdreProduction entity representing production orders
 */
@Entity
@Table(name = "ordre_production")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrdreProduction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "date_deb")
    private LocalDateTime dateDeb;

    @Column(name = "date_fin")
    private LocalDateTime dateFin;

    @Column(name = "qte_fab", precision = 15, scale = 2)
    private BigDecimal qteFab;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @Column(name = "process_id")
    private Long processId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sortie_depot_id")
    private Depot sortieDepot;
}
