package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import com.chatbootmcp.chatmcp.service.UserDataService;
import com.chatbootmcp.chatmcp.util.MockDataGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Controller for providing mock data for the MCP web application
 */
@RestController
@RequestMapping("/mock-data")
public class MockDataController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserDataService userDataService;

    @Autowired
    private MockDataGenerator mockDataGenerator;

    /**
     * Get user dashboard statistics
     */
    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardData(
            @AuthenticationPrincipal UserDetails userDetails) {
        
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        Map<String, Object> dashboardData = new HashMap<>();
        
        // Add user info
        dashboardData.put("user", getUserInfo(user));
        
        // Add summary statistics
        dashboardData.put("summary", getSummaryStatistics(user));
        
        // Add recent activity
        dashboardData.put("recentActivity", getRecentActivity(user));
        
        // Add performance metrics
        dashboardData.put("performance", mockDataGenerator.generatePerformanceMetrics());
        
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get user analytics data
     */
    @GetMapping("/analytics")
    public ResponseEntity<Map<String, Object>> getAnalyticsData(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestParam(required = false) String period) {
        
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Default to "week" if period is not specified
        if (period == null) {
            period = "week";
        }
        
        Map<String, Object> analyticsData = new HashMap<>();
        
        switch (period.toLowerCase()) {
            case "day":
                analyticsData.put("usageData", mockDataGenerator.generateHourlyUsageData());
                break;
            case "week":
                analyticsData.put("usageData", mockDataGenerator.generateDailyUsageData());
                break;
            case "month":
                analyticsData.put("usageData", mockDataGenerator.generateWeeklyUsageData());
                break;
            case "year":
                analyticsData.put("usageData", mockDataGenerator.generateMonthlyUsageData());
                break;
            default:
                analyticsData.put("usageData", mockDataGenerator.generateDailyUsageData());
        }
        
        // Add feature usage
        analyticsData.put("featureUsage", mockDataGenerator.generateFeatureUsageData());
        
        // Add user engagement metrics
        analyticsData.put("engagement", mockDataGenerator.generateEngagementMetrics());
        
        return ResponseEntity.ok(analyticsData);
    }

    /**
     * Get user notifications
     */
    @GetMapping("/notifications")
    public ResponseEntity<Map<String, Object>> getNotifications(
            @AuthenticationPrincipal UserDetails userDetails) {
        
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        Map<String, Object> notificationsData = new HashMap<>();
        
        // Generate notifications
        List<Map<String, Object>> notifications = new ArrayList<>();
        
        // Add some system notifications
        notifications.add(createNotification(
            "system", 
            "System Maintenance", 
            "Scheduled maintenance will occur on " + 
                LocalDate.now().plusDays(5).format(DateTimeFormatter.ISO_DATE) + 
                " from 2:00 AM to 4:00 AM UTC.",
            LocalDateTime.now().minusHours(2)
        ));
        
        notifications.add(createNotification(
            "update", 
            "New Features Available", 
            "Check out our new analytics dashboard with improved visualizations!",
            LocalDateTime.now().minusDays(1)
        ));
        
        // Add some user-specific notifications
        notifications.add(createNotification(
            "alert", 
            "Security Alert", 
            "Your account was accessed from a new device. If this wasn't you, please change your password.",
            LocalDateTime.now().minusHours(5)
        ));
        
        notifications.add(createNotification(
            "message", 
            "New Message", 
            "You have a new message from the support team.",
            LocalDateTime.now().minusMinutes(30)
        ));
        
        // Add some random notifications
        Random random = new Random();
        int additionalNotifications = random.nextInt(3) + 2; // 2-4 additional notifications
        
        String[] types = {"system", "update", "alert", "message"};
        String[] titles = {
            "Account Update", "Subscription Renewal", "Weekly Report", 
            "Usage Limit", "New Integration", "Feature Request"
        };
        String[] contents = {
            "Your account has been updated with new permissions.",
            "Your subscription will renew in 7 days.",
            "Your weekly usage report is now available.",
            "You've reached 80% of your monthly usage limit.",
            "A new integration with Google Calendar is now available.",
            "Your feature request has been approved and scheduled for development."
        };
        
        for (int i = 0; i < additionalNotifications; i++) {
            notifications.add(createNotification(
                types[random.nextInt(types.length)],
                titles[random.nextInt(titles.length)],
                contents[random.nextInt(contents.length)],
                LocalDateTime.now().minusDays(random.nextInt(7)).minusHours(random.nextInt(24))
            ));
        }
        
        // Sort notifications by timestamp (newest first)
        notifications.sort((a, b) -> {
            LocalDateTime timeA = (LocalDateTime) a.get("timestamp");
            LocalDateTime timeB = (LocalDateTime) b.get("timestamp");
            return timeB.compareTo(timeA);
        });
        
        notificationsData.put("notifications", notifications);
        notificationsData.put("unreadCount", random.nextInt(notifications.size() + 1));
        
        return ResponseEntity.ok(notificationsData);
    }

    /**
     * Get user recommendations
     */
    @GetMapping("/recommendations")
    public ResponseEntity<Map<String, Object>> getRecommendations(
            @AuthenticationPrincipal UserDetails userDetails) {
        
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        return ResponseEntity.ok(mockDataGenerator.generateRecommendations());
    }

    // Helper methods
    
    private Map<String, Object> getUserInfo(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("fullName", user.getFullName());
        userInfo.put("roles", user.getRoles());
        userInfo.put("createdAt", user.getCreatedAt());
        return userInfo;
    }
    
    private Map<String, Object> getSummaryStatistics(User user) {
        Map<String, Object> summary = new HashMap<>();
        Random random = new Random();
        
        // Total conversations
        summary.put("totalConversations", random.nextInt(50) + 10);
        
        // Total messages
        summary.put("totalMessages", random.nextInt(500) + 100);
        
        // Active time (in hours)
        summary.put("activeTimeHours", random.nextInt(100) + 20);
        
        // Average response time (in seconds)
        summary.put("avgResponseTime", random.nextInt(10) + 1);
        
        // Satisfaction score (out of 100)
        summary.put("satisfactionScore", random.nextInt(20) + 80);
        
        return summary;
    }
    
    private List<Map<String, Object>> getRecentActivity(User user) {
        List<Map<String, Object>> activities = new ArrayList<>();
        Random random = new Random();
        
        String[] activityTypes = {
            "login", "conversation_created", "message_sent", "data_exported", 
            "settings_changed", "profile_updated"
        };
        
        // Generate 5-10 recent activities
        int numActivities = random.nextInt(6) + 5;
        
        for (int i = 0; i < numActivities; i++) {
            Map<String, Object> activity = new HashMap<>();
            
            String type = activityTypes[random.nextInt(activityTypes.length)];
            activity.put("type", type);
            
            // Set timestamp (between now and 7 days ago)
            activity.put("timestamp", LocalDateTime.now().minusDays(random.nextInt(7)).minusHours(random.nextInt(24)));
            
            // Add activity-specific details
            Map<String, Object> details = new HashMap<>();
            
            switch (type) {
                case "login":
                    details.put("ipAddress", "192.168." + random.nextInt(256) + "." + random.nextInt(256));
                    details.put("device", random.nextBoolean() ? "Mobile" : "Desktop");
                    details.put("browser", random.nextBoolean() ? "Chrome" : "Firefox");
                    break;
                case "conversation_created":
                    details.put("conversationId", random.nextInt(100) + 1);
                    details.put("title", "Conversation " + (random.nextInt(100) + 1));
                    break;
                case "message_sent":
                    details.put("conversationId", random.nextInt(100) + 1);
                    details.put("messageId", random.nextInt(1000) + 1);
                    details.put("length", random.nextInt(200) + 10);
                    break;
                case "data_exported":
                    details.put("format", random.nextBoolean() ? "CSV" : "JSON");
                    details.put("size", random.nextInt(1000) + "KB");
                    details.put("records", random.nextInt(1000) + 100);
                    break;
                case "settings_changed":
                    details.put("setting", random.nextBoolean() ? "Notifications" : "Privacy");
                    details.put("oldValue", "Disabled");
                    details.put("newValue", "Enabled");
                    break;
                case "profile_updated":
                    details.put("field", random.nextBoolean() ? "Email" : "Password");
                    break;
            }
            
            activity.put("details", details);
            activities.add(activity);
        }
        
        // Sort by timestamp (newest first)
        activities.sort((a, b) -> {
            LocalDateTime timeA = (LocalDateTime) a.get("timestamp");
            LocalDateTime timeB = (LocalDateTime) b.get("timestamp");
            return timeB.compareTo(timeA);
        });
        
        return activities;
    }
    
    private Map<String, Object> createNotification(String type, String title, String content, LocalDateTime timestamp) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("id", UUID.randomUUID().toString());
        notification.put("type", type);
        notification.put("title", title);
        notification.put("content", content);
        notification.put("timestamp", timestamp);
        notification.put("read", false);
        return notification;
    }
}
