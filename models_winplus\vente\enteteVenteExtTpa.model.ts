import { TypeFacturationTPA } from 'src/app/winpharm/enums/vente/TypeFacturationTPA.enum';


import { ConventionAssurance } from 'src/app/winpharm/models/assurance/conventionAssurance.model';
import { FactureTpa } from './factureTpa.model';
import { OrganismeAssurance } from 'src/app/winpharm/models/tiers/organisme/organismeAssurance.model';


export class EnteteVenteExtTpa { 
    audited?: boolean;
    convention?: ConventionAssurance;
    factureTpa?: FactureTpa;
    id?: number;
    mntTtcTpaPartClient?: number;
    mntTtcTpaPartOrga?: number;
    nomConvention?: string;
    nomOrganisme?: string;
    numAffiliation?: string;
    numDossier?: string;
    numImmatriculation?: string;
    numPriseEnCharge?: string;
    organisme?: OrganismeAssurance;
    plafondRembTpa?: number;
    tauxRembTpa?: number;
    typeFacturationTpa?: TypeFacturationTPA;
    userModifiable?: boolean;



    

    constructor() {
        this.mntTtcTpaPartOrga = 0;
        this.tauxRembTpa = 0;
    }
}

