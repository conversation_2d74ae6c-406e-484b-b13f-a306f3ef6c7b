// src/app/chat/components/chat-home.component.scss

$primary-color: #4a6cfa;
$primary-light: #eef1ff;
$secondary-color: #f8f9fd;
$text-color: #333333;
$text-light: #6e7191;
$border-color: #e4e8f7;
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

.home-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  background-color: #f9f9f9;
  font-family: $font-family;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }
}

.welcome-section {
  padding: 20px 20px 12px;
  background-color: #1a1a1a;
  color: white;
  
  .greeting {
    font-size: 18px;
    font-weight: 400;
    margin: 0;
    color: #a0a0a0;
  }
  
  .subgreeting {
    font-size: 22px;
    font-weight: 600;
    margin: 4px 0 0;
  }
}

.question-input {
  margin: 12px 16px;
  padding: 12px 16px;
  background-color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  .input-placeholder {
    color: $text-light;
    font-size: 14px;
  }
  
  .input-icon {
    width: 28px;
    height: 28px;
    background-color: #1a1a1a;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
  }
}

.articles-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 16px;
  
  .article-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .article-image {
      height: 180px;
      background-size: cover;
      background-position: center;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      
      .article-subtitle {
        font-size: 14px;
        font-weight: 500;
      }
      
      .article-title-overlay {
        font-size: 28px;
        font-weight: 700;
        text-align: center;
        padding: 0 20px;
        text-transform: uppercase;
      }
    }
    
    .article-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      padding: 16px 16px 0;
      text-transform: uppercase;
    }
    
    .article-content {
      padding: 16px;
      
      .article-description {
        font-size: 15px;
        font-weight: 600;
        margin: 0 0 8px;
        color: $text-color;
      }
      
      .article-text {
        font-size: 14px;
        line-height: 1.5;
        color: $text-light;
        margin: 0;
      }
    }
  }
}

.search-section {
  padding: 16px;
  border-top: 1px solid #e5e5e5;
  margin-top: 8px;
  
  .search-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .search-placeholder {
      color: $text-light;
      font-size: 14px;
    }
    
    .search-icon {
      color: $text-light;
      font-size: 14px;
    }
  }
}

.suggested-questions {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  
  .question-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e5e5e5;
    cursor: pointer;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    .question-text {
      font-size: 14px;
      color: $text-color;
    }
    
    .question-arrow {
      color: $text-light;
      font-size: 12px;
    }
  }
}


.bg-image {
  position: relative;
  margin-bottom: 1rem;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}