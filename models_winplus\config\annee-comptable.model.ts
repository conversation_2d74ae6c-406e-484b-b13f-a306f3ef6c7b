import { TenantPrincipal } from "src/app/shared/models/tenantPrincipal.model"
import { Operateur } from "../common/operateur.model"


export class AnneeComptableModel {
    annee?: number
    dateCreation?: string
    dateOuverture?: string
    id?: number
    statut?: StatutAnneeComptable
    tenantId?: number
    userOuverture?: Operateur
    societeTenant?: TenantPrincipal
}


export enum StatutAnneeComptable {
    OPEN = "O",
    NOT_OPEN = "N"
}