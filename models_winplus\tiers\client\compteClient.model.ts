import { Statut } from "src/app/winpharm/enums/common/Statut.enum";
import { FluxFin } from "../../common/fluxFin.model";
import { DetailBlVente } from "../../vente/detailBlVente.model";
import { EnteteBlVente } from "../../vente/enteteBlVente.model";
import { Client } from "./client.model";
import { Operateur } from "../../common/operateur.model";



export enum TypeOperationCompteClient {
    VENTE_CREDIT = "V",
    ENCAISSEMENT = "E",
    SOLDE = "S",
    AVANCE = "A",
    VENTE_COMPTANT = "C",
    ENCAISSEMENT_COMPTANT = "R",
    SOLDE_DEPART = 'D',
    DECAISSEMENT_CREDIT = "N"
}
export class CompteClient {

    client: Client;
    dateArchive: string;
    dateOperation: string;
    detailVente: DetailBlVente;
    fluxFin: FluxFin;
    id: number
    libelle: string;

    nomClient: string
    prixLigneVt: number
    qteLigneVt: number
    soldeClientApres: number
    soldeClientAvant: number
    tauxRemiseOperation: number
    typeOperation: TypeOperationCompteClient
    vente: EnteteBlVente
    statut: Statut

    operateur?: Operateur

    montantAppliqueTtc: number
    montantBrutHt: number
    montantBrutTtc: number

}