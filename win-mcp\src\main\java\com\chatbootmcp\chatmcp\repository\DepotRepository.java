package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Depot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Depot entity
 */
@Repository
public interface DepotRepository extends JpaRepository<Depot, Long> {
    
    Optional<Depot> findByCodeDepot(String codeDepot);
    
    List<Depot> findByEstActifTrue();
    
    Optional<Depot> findByPrimaireTrue();
    
    @Query("SELECT d FROM Depot d WHERE d.estActif = true ORDER BY d.libelleDepot")
    List<Depot> findActiveDepotsOrderByLibelle();
}
