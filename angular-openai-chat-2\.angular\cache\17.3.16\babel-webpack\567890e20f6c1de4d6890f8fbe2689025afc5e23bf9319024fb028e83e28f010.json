{"ast": null, "code": "'use strict';\n\n/**\n * @license Angular v<unknown>\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\nfunction patchJasmine(Zone) {\n  Zone.__load_patch('jasmine', (global, Zone, api) => {\n    const __extends = function (d, b) {\n      for (const p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      function __() {\n        this.constructor = d;\n      }\n      d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n    // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n    // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n    if (!Zone) throw new Error('Missing: zone.js');\n    if (typeof jest !== 'undefined') {\n      // return if jasmine is a light implementation inside jest\n      // in this case, we are running inside jest not jasmine\n      return;\n    }\n    if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n      return;\n    }\n    jasmine['__zone_patch__'] = true;\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    if (!SyncTestZoneSpec) throw new Error('Missing: SyncTestZoneSpec');\n    if (!ProxyZoneSpec) throw new Error('Missing: ProxyZoneSpec');\n    const ambientZone = Zone.current;\n    const symbol = Zone.__symbol__;\n    // whether patch jasmine clock when in fakeAsync\n    const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n    // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n    // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we\n    // also automatically disable the auto jump into fakeAsync feature\n    const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock && (global[symbol('fakeAsyncPatchLock')] === true || global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true);\n    const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n    if (!ignoreUnhandledRejection) {\n      const globalErrors = jasmine.GlobalErrors;\n      if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n        jasmine[symbol('GlobalErrors')] = globalErrors;\n        jasmine.GlobalErrors = function () {\n          const instance = new globalErrors();\n          const originalInstall = instance.install;\n          if (originalInstall && !instance[symbol('install')]) {\n            instance[symbol('install')] = originalInstall;\n            instance.install = function () {\n              const isNode = typeof process !== 'undefined' && !!process.on;\n              // Note: Jasmine checks internally if `process` and `process.on` is defined.\n              // Otherwise, it installs the browser rejection handler through the\n              // `global.addEventListener`. This code may be run in the browser environment where\n              // `process` is not defined, and this will lead to a runtime exception since Webpack 5\n              // removed automatic Node.js polyfills. Note, that events are named differently, it's\n              // `unhandledRejection` in Node.js and `unhandledrejection` in the browser.\n              const originalHandlers = isNode ? process.listeners('unhandledRejection') : global.eventListeners('unhandledrejection');\n              const result = originalInstall.apply(this, arguments);\n              isNode ? process.removeAllListeners('unhandledRejection') : global.removeAllListeners('unhandledrejection');\n              if (originalHandlers) {\n                originalHandlers.forEach(handler => {\n                  if (isNode) {\n                    process.on('unhandledRejection', handler);\n                  } else {\n                    global.addEventListener('unhandledrejection', handler);\n                  }\n                });\n              }\n              return result;\n            };\n          }\n          return instance;\n        };\n      }\n    }\n    // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n    const jasmineEnv = jasmine.getEnv();\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n      let originalJasmineFn = jasmineEnv[methodName];\n      jasmineEnv[methodName] = function (description, specDefinitions) {\n        return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n      };\n    });\n    ['it', 'xit', 'fit'].forEach(methodName => {\n      let originalJasmineFn = jasmineEnv[methodName];\n      jasmineEnv[symbol(methodName)] = originalJasmineFn;\n      jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n        arguments[1] = wrapTestInZone(specDefinitions);\n        return originalJasmineFn.apply(this, arguments);\n      };\n    });\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n      let originalJasmineFn = jasmineEnv[methodName];\n      jasmineEnv[symbol(methodName)] = originalJasmineFn;\n      jasmineEnv[methodName] = function (specDefinitions, timeout) {\n        arguments[0] = wrapTestInZone(specDefinitions);\n        return originalJasmineFn.apply(this, arguments);\n      };\n    });\n    if (!disablePatchingJasmineClock) {\n      // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n      // they can work properly in FakeAsyncTest\n      const originalClockFn = jasmine[symbol('clock')] = jasmine['clock'];\n      jasmine['clock'] = function () {\n        const clock = originalClockFn.apply(this, arguments);\n        if (!clock[symbol('patched')]) {\n          clock[symbol('patched')] = symbol('patched');\n          const originalTick = clock[symbol('tick')] = clock.tick;\n          clock.tick = function () {\n            const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            if (fakeAsyncZoneSpec) {\n              return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n            }\n            return originalTick.apply(this, arguments);\n          };\n          const originalMockDate = clock[symbol('mockDate')] = clock.mockDate;\n          clock.mockDate = function () {\n            const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            if (fakeAsyncZoneSpec) {\n              const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n              return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function' ? [dateTime.getTime()] : arguments);\n            }\n            return originalMockDate.apply(this, arguments);\n          };\n          // for auto go into fakeAsync feature, we need the flag to enable it\n          if (enableAutoFakeAsyncWhenClockPatched) {\n            ['install', 'uninstall'].forEach(methodName => {\n              const originalClockFn = clock[symbol(methodName)] = clock[methodName];\n              clock[methodName] = function () {\n                const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n                if (FakeAsyncTestZoneSpec) {\n                  jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                  return;\n                }\n                return originalClockFn.apply(this, arguments);\n              };\n            });\n          }\n        }\n        return clock;\n      };\n    }\n    // monkey patch createSpyObj to make properties enumerable to true\n    if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n      const originalCreateSpyObj = jasmine.createSpyObj;\n      jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n      jasmine.createSpyObj = function () {\n        const args = Array.prototype.slice.call(arguments);\n        const propertyNames = args.length >= 3 ? args[2] : null;\n        let spyObj;\n        if (propertyNames) {\n          const defineProperty = Object.defineProperty;\n          Object.defineProperty = function (obj, p, attributes) {\n            return defineProperty.call(this, obj, p, {\n              ...attributes,\n              configurable: true,\n              enumerable: true\n            });\n          };\n          try {\n            spyObj = originalCreateSpyObj.apply(this, args);\n          } finally {\n            Object.defineProperty = defineProperty;\n          }\n        } else {\n          spyObj = originalCreateSpyObj.apply(this, args);\n        }\n        return spyObj;\n      };\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(description, describeBody) {\n      return function () {\n        // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n        // error if any asynchronous operations are attempted inside of a `describe`.\n        const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n        return syncZone.run(describeBody, this, arguments);\n      };\n    }\n    function runInTestZone(testBody, applyThis, queueRunner, done) {\n      const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n      queueRunner.testProxyZoneSpec;\n      const testProxyZone = queueRunner.testProxyZone;\n      if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n        // auto run a fakeAsync\n        const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n        if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n          testBody = fakeAsyncModule.fakeAsync(testBody);\n        }\n      }\n      if (done) {\n        return testProxyZone.run(testBody, applyThis, [done]);\n      } else {\n        return testProxyZone.run(testBody, applyThis);\n      }\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n     */\n    function wrapTestInZone(testBody) {\n      // The `done` callback is only passed through if the function expects at least one argument.\n      // Note we have to make a function with correct number of arguments, otherwise jasmine will\n      // think that all functions are sync or async.\n      return testBody && (testBody.length ? function (done) {\n        return runInTestZone(testBody, this, this.queueRunner, done);\n      } : function () {\n        return runInTestZone(testBody, this, this.queueRunner);\n      });\n    }\n    const QueueRunner = jasmine.QueueRunner;\n    jasmine.QueueRunner = function (_super) {\n      __extends(ZoneQueueRunner, _super);\n      function ZoneQueueRunner(attrs) {\n        if (attrs.onComplete) {\n          attrs.onComplete = (fn => () => {\n            // All functions are done, clear the test zone.\n            this.testProxyZone = null;\n            this.testProxyZoneSpec = null;\n            ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n          })(attrs.onComplete);\n        }\n        const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n        const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n        if (nativeSetTimeout) {\n          // should run setTimeout inside jasmine outside of zone\n          attrs.timeout = {\n            setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n            clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout\n          };\n        }\n        // create a userContext to hold the queueRunner itself\n        // so we can access the testProxy in it/xit/beforeEach ...\n        if (jasmine.UserContext) {\n          if (!attrs.userContext) {\n            attrs.userContext = new jasmine.UserContext();\n          }\n          attrs.userContext.queueRunner = this;\n        } else {\n          if (!attrs.userContext) {\n            attrs.userContext = {};\n          }\n          attrs.userContext.queueRunner = this;\n        }\n        // patch attrs.onException\n        const onException = attrs.onException;\n        attrs.onException = function (error) {\n          if (error && error.message === 'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n            // jasmine timeout, we can make the error message more\n            // reasonable to tell what tasks are pending\n            const proxyZoneSpec = this && this.testProxyZoneSpec;\n            if (proxyZoneSpec) {\n              const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n              try {\n                // try catch here in case error.message is not writable\n                error.message += pendingTasksInfo;\n              } catch (err) {}\n            }\n          }\n          if (onException) {\n            onException.call(this, error);\n          }\n        };\n        _super.call(this, attrs);\n      }\n      ZoneQueueRunner.prototype.execute = function () {\n        let zone = Zone.current;\n        let isChildOfAmbientZone = false;\n        while (zone) {\n          if (zone === ambientZone) {\n            isChildOfAmbientZone = true;\n            break;\n          }\n          zone = zone.parent;\n        }\n        if (!isChildOfAmbientZone) throw new Error('Unexpected Zone: ' + Zone.current.name);\n        // This is the zone which will be used for running individual tests.\n        // It will be a proxy zone, so that the tests function can retroactively install\n        // different zones.\n        // Example:\n        //   - In beforeEach() do childZone = Zone.current.fork(...);\n        //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n        //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n        //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n        //     fakeAsync behavior to the childZone.\n        this.testProxyZoneSpec = new ProxyZoneSpec();\n        this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n        if (!Zone.currentTask) {\n          // if we are not running in a task then if someone would register a\n          // element.addEventListener and then calling element.click() the\n          // addEventListener callback would think that it is the top most task and would\n          // drain the microtask queue on element.click() which would be incorrect.\n          // For this reason we always force a task when running jasmine tests.\n          Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n        } else {\n          _super.prototype.execute.call(this);\n        }\n      };\n      return ZoneQueueRunner;\n    }(QueueRunner);\n  });\n}\nfunction patchJest(Zone) {\n  Zone.__load_patch('jest', (context, Zone, api) => {\n    if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n      return;\n    }\n    // From jest 29 and jest-preset-angular v13, the module transform logic\n    // changed, and now jest-preset-angular use the use the tsconfig target\n    // other than the hardcoded one, https://github.com/thymikee/jest-preset-angular/issues/2010\n    // But jest-angular-preset doesn't introduce the @babel/plugin-transform-async-to-generator\n    // which is needed by angular since `async/await` still need to be transformed\n    // to promise for ES2017+ target.\n    // So for now, we disable to output the uncaught error console log for a temp solution,\n    // until jest-preset-angular find a proper solution.\n    Zone[api.symbol('ignoreConsoleErrorUncaughtError')] = true;\n    jest['__zone_patch__'] = true;\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n      throw new Error('Missing ProxyZoneSpec');\n    }\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n    const proxyZoneSpec = new ProxyZoneSpec();\n    const proxyZone = rootZone.fork(proxyZoneSpec);\n    function wrapDescribeFactoryInZone(originalJestFn) {\n      return function (...tableArgs) {\n        const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n        return function (...args) {\n          args[1] = wrapDescribeInZone(args[1]);\n          return originalDescribeFn.apply(this, args);\n        };\n      };\n    }\n    function wrapTestFactoryInZone(originalJestFn) {\n      return function (...tableArgs) {\n        return function (...args) {\n          args[1] = wrapTestInZone(args[1]);\n          return originalJestFn.apply(this, tableArgs).apply(this, args);\n        };\n      };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(describeBody) {\n      return function (...args) {\n        return syncZone.run(describeBody, this, args);\n      };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in the `proxyZone`.\n     */\n    function wrapTestInZone(testBody, isTestFunc = false) {\n      if (typeof testBody !== 'function') {\n        return testBody;\n      }\n      const wrappedFunc = function () {\n        if (Zone[api.symbol('useFakeTimersCalled')] === true && testBody && !testBody.isFakeAsync) {\n          // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n          const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n          if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n            testBody = fakeAsyncModule.fakeAsync(testBody);\n          }\n        }\n        proxyZoneSpec.isTestFunc = isTestFunc;\n        return proxyZone.run(testBody, null, arguments);\n      };\n      // Update the length of wrappedFunc to be the same as the length of the testBody\n      // So jest core can handle whether the test function has `done()` or not correctly\n      Object.defineProperty(wrappedFunc, 'length', {\n        configurable: true,\n        writable: true,\n        enumerable: false\n      });\n      wrappedFunc.length = testBody.length;\n      return wrappedFunc;\n    }\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n      let originalJestFn = context[methodName];\n      if (context[Zone.__symbol__(methodName)]) {\n        return;\n      }\n      context[Zone.__symbol__(methodName)] = originalJestFn;\n      context[methodName] = function (...args) {\n        args[1] = wrapDescribeInZone(args[1]);\n        return originalJestFn.apply(this, args);\n      };\n      context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n    });\n    context.describe.only = context.fdescribe;\n    context.describe.skip = context.xdescribe;\n    ['it', 'xit', 'fit', 'test', 'xtest'].forEach(methodName => {\n      let originalJestFn = context[methodName];\n      if (context[Zone.__symbol__(methodName)]) {\n        return;\n      }\n      context[Zone.__symbol__(methodName)] = originalJestFn;\n      context[methodName] = function (...args) {\n        args[1] = wrapTestInZone(args[1], true);\n        return originalJestFn.apply(this, args);\n      };\n      context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n      context[methodName].todo = originalJestFn.todo;\n    });\n    context.it.only = context.fit;\n    context.it.skip = context.xit;\n    context.test.only = context.fit;\n    context.test.skip = context.xit;\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n      let originalJestFn = context[methodName];\n      if (context[Zone.__symbol__(methodName)]) {\n        return;\n      }\n      context[Zone.__symbol__(methodName)] = originalJestFn;\n      context[methodName] = function (...args) {\n        args[0] = wrapTestInZone(args[0]);\n        return originalJestFn.apply(this, args);\n      };\n    });\n    Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n      // check whether currently the test is inside fakeAsync()\n      function isPatchingFakeTimer() {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        return !!fakeAsyncZoneSpec;\n      }\n      // check whether the current function is inside `test/it` or other methods\n      // such as `describe/beforeEach`\n      function isInTestFunc() {\n        const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n        return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n      }\n      if (Timer[api.symbol('fakeTimers')]) {\n        return;\n      }\n      Timer[api.symbol('fakeTimers')] = true;\n      // patch jest fakeTimer internal method to make sure no console.warn print out\n      api.patchMethod(Timer, '_checkFakeTimers', delegate => {\n        return function (self, args) {\n          if (isPatchingFakeTimer()) {\n            return true;\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n      api.patchMethod(Timer, 'useFakeTimers', delegate => {\n        return function (self, args) {\n          Zone[api.symbol('useFakeTimersCalled')] = true;\n          if (isModern || isInTestFunc()) {\n            return delegate.apply(self, args);\n          }\n          return self;\n        };\n      });\n      // patch useRealTimers(), unset useFakeTimers flag\n      api.patchMethod(Timer, 'useRealTimers', delegate => {\n        return function (self, args) {\n          Zone[api.symbol('useFakeTimersCalled')] = false;\n          if (isModern || isInTestFunc()) {\n            return delegate.apply(self, args);\n          }\n          return self;\n        };\n      });\n      // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n      api.patchMethod(Timer, 'setSystemTime', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n            fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n      api.patchMethod(Timer, 'getRealSystemTime', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n            return fakeAsyncZoneSpec.getRealSystemTime();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch runAllTicks(), run all microTasks inside fakeAsync\n      api.patchMethod(Timer, 'runAllTicks', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.flushMicrotasks();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch runAllTimers(), run all macroTasks inside fakeAsync\n      api.patchMethod(Timer, 'runAllTimers', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.flush(100, true);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n      api.patchMethod(Timer, 'advanceTimersByTime', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.tick(args[0]);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n      api.patchMethod(Timer, 'runOnlyPendingTimers', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.flushOnlyPendingTimers();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n      api.patchMethod(Timer, 'advanceTimersToNextTimer', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.tickToNext(args[0]);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n      api.patchMethod(Timer, 'clearAllTimers', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.removeAllTimers();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n      api.patchMethod(Timer, 'getTimerCount', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            return fakeAsyncZoneSpec.getTimerCount();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n    };\n  });\n}\nfunction patchMocha(Zone) {\n  Zone.__load_patch('mocha', (global, Zone) => {\n    const Mocha = global.Mocha;\n    if (typeof Mocha === 'undefined') {\n      // return if Mocha is not available, because now zone-testing\n      // will load mocha patch with jasmine/jest patch\n      return;\n    }\n    if (typeof Zone === 'undefined') {\n      throw new Error('Missing Zone.js');\n    }\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n      throw new Error('Missing ProxyZoneSpec');\n    }\n    if (Mocha['__zone_patch__']) {\n      throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n    }\n    Mocha['__zone_patch__'] = true;\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n    let testZone = null;\n    const suiteZone = rootZone.fork(new ProxyZoneSpec());\n    const mochaOriginal = {\n      after: global.after,\n      afterEach: global.afterEach,\n      before: global.before,\n      beforeEach: global.beforeEach,\n      describe: global.describe,\n      it: global.it\n    };\n    function modifyArguments(args, syncTest, asyncTest) {\n      for (let i = 0; i < args.length; i++) {\n        let arg = args[i];\n        if (typeof arg === 'function') {\n          // The `done` callback is only passed through if the function expects at\n          // least one argument.\n          // Note we have to make a function with correct number of arguments,\n          // otherwise mocha will\n          // think that all functions are sync or async.\n          args[i] = arg.length === 0 ? syncTest(arg) : asyncTest(arg);\n          // Mocha uses toString to view the test body in the result list, make sure we return the\n          // correct function body\n          args[i].toString = function () {\n            return arg.toString();\n          };\n        }\n      }\n      return args;\n    }\n    function wrapDescribeInZone(args) {\n      const syncTest = function (fn) {\n        return function () {\n          return syncZone.run(fn, this, arguments);\n        };\n      };\n      return modifyArguments(args, syncTest);\n    }\n    function wrapTestInZone(args) {\n      const asyncTest = function (fn) {\n        return function (done) {\n          return testZone.run(fn, this, [done]);\n        };\n      };\n      const syncTest = function (fn) {\n        return function () {\n          return testZone.run(fn, this);\n        };\n      };\n      return modifyArguments(args, syncTest, asyncTest);\n    }\n    function wrapSuiteInZone(args) {\n      const asyncTest = function (fn) {\n        return function (done) {\n          return suiteZone.run(fn, this, [done]);\n        };\n      };\n      const syncTest = function (fn) {\n        return function () {\n          return suiteZone.run(fn, this);\n        };\n      };\n      return modifyArguments(args, syncTest, asyncTest);\n    }\n    global.describe = global.suite = function () {\n      return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.xdescribe = global.suite.skip = global.describe.skip = function () {\n      return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.describe.only = global.suite.only = function () {\n      return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.it = global.specify = global.test = function () {\n      return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n    };\n    global.xit = global.xspecify = global.it.skip = function () {\n      return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n    };\n    global.it.only = global.test.only = function () {\n      return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n    };\n    global.after = global.suiteTeardown = function () {\n      return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.afterEach = global.teardown = function () {\n      return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n    };\n    global.before = global.suiteSetup = function () {\n      return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.beforeEach = global.setup = function () {\n      return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n    };\n    ((originalRunTest, originalRun) => {\n      Mocha.Runner.prototype.runTest = function (fn) {\n        Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n          originalRunTest.call(this, fn);\n        });\n      };\n      Mocha.Runner.prototype.run = function (fn) {\n        this.on('test', e => {\n          testZone = rootZone.fork(new ProxyZoneSpec());\n        });\n        this.on('fail', (test, err) => {\n          const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n          if (proxyZoneSpec && err) {\n            try {\n              // try catch here in case err.message is not writable\n              err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n            } catch (error) {}\n          }\n        });\n        return originalRun.call(this, fn);\n      };\n    })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n  });\n}\nconst global$2 = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n  const symbolPrefix = global$2['__Zone_symbol_prefix'] || '__zone_symbol__';\n  return symbolPrefix + name;\n}\nconst __global = typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global;\nclass AsyncTestZoneSpec {\n  // Needs to be a getter and not a plain property in order run this just-in-time. Otherwise\n  // `__symbol__` would be evaluated during top-level execution prior to the Zone prefix being\n  // changed for tests.\n  static get symbolParentUnresolved() {\n    return __symbol__('parentUnresolved');\n  }\n  constructor(finishCallback, failCallback, namePrefix) {\n    this.finishCallback = finishCallback;\n    this.failCallback = failCallback;\n    this._pendingMicroTasks = false;\n    this._pendingMacroTasks = false;\n    this._alreadyErrored = false;\n    this._isSync = false;\n    this._existingFinishTimer = null;\n    this.entryFunction = null;\n    this.runZone = Zone.current;\n    this.unresolvedChainedPromiseCount = 0;\n    this.supportWaitUnresolvedChainedPromise = false;\n    this.name = 'asyncTestZone for ' + namePrefix;\n    this.properties = {\n      'AsyncTestZoneSpec': this\n    };\n    this.supportWaitUnresolvedChainedPromise = __global[__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n  }\n  isUnresolvedChainedPromisePending() {\n    return this.unresolvedChainedPromiseCount > 0;\n  }\n  _finishCallbackIfDone() {\n    // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n    // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n    // microtasks in the proxy zone that now complete as part of this async zone run.\n    // Consider the following scenario:\n    //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n    //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n    //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n    //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n    //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n    //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n    // If the finish timeout from below is already scheduled, terminate the existing scheduled\n    // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n    // want to schedule a new finish callback in case the task state changes again.\n    if (this._existingFinishTimer !== null) {\n      clearTimeout(this._existingFinishTimer);\n      this._existingFinishTimer = null;\n    }\n    if (!(this._pendingMicroTasks || this._pendingMacroTasks || this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending())) {\n      // We wait until the next tick because we would like to catch unhandled promises which could\n      // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n      this.runZone.run(() => {\n        this._existingFinishTimer = setTimeout(() => {\n          if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n            this.finishCallback();\n          }\n        }, 0);\n      });\n    }\n  }\n  patchPromiseForTest() {\n    if (!this.supportWaitUnresolvedChainedPromise) {\n      return;\n    }\n    const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n    if (patchPromiseForTest) {\n      patchPromiseForTest();\n    }\n  }\n  unPatchPromiseForTest() {\n    if (!this.supportWaitUnresolvedChainedPromise) {\n      return;\n    }\n    const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n    if (unPatchPromiseForTest) {\n      unPatchPromiseForTest();\n    }\n  }\n  onScheduleTask(delegate, current, target, task) {\n    if (task.type !== 'eventTask') {\n      this._isSync = false;\n    }\n    if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n      // check whether the promise is a chained promise\n      if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n        // chained promise is being scheduled\n        this.unresolvedChainedPromiseCount--;\n      }\n    }\n    return delegate.scheduleTask(target, task);\n  }\n  onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n    if (task.type !== 'eventTask') {\n      this._isSync = false;\n    }\n    return delegate.invokeTask(target, task, applyThis, applyArgs);\n  }\n  onCancelTask(delegate, current, target, task) {\n    if (task.type !== 'eventTask') {\n      this._isSync = false;\n    }\n    return delegate.cancelTask(target, task);\n  }\n  // Note - we need to use onInvoke at the moment to call finish when a test is\n  // fully synchronous. TODO(juliemr): remove this when the logic for\n  // onHasTask changes and it calls whenever the task queues are dirty.\n  // updated by(JiaLiPassion), only call finish callback when no task\n  // was scheduled/invoked/canceled.\n  onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n    if (!this.entryFunction) {\n      this.entryFunction = delegate;\n    }\n    try {\n      this._isSync = true;\n      return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n    } finally {\n      // We need to check the delegate is the same as entryFunction or not.\n      // Consider the following case.\n      //\n      // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n      //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n      //   });\n      // });\n      //\n      // We only want to check whether there are async tasks scheduled\n      // for the entry function.\n      if (this._isSync && this.entryFunction === delegate) {\n        this._finishCallbackIfDone();\n      }\n    }\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    // Let the parent try to handle the error.\n    const result = parentZoneDelegate.handleError(targetZone, error);\n    if (result) {\n      this.failCallback(error);\n      this._alreadyErrored = true;\n    }\n    return false;\n  }\n  onHasTask(delegate, current, target, hasTaskState) {\n    delegate.hasTask(target, hasTaskState);\n    // We should only trigger finishCallback when the target zone is the AsyncTestZone\n    // Consider the following cases.\n    //\n    // const childZone = asyncTestZone.fork({\n    //   name: 'child',\n    //   onHasTask: ...\n    // });\n    //\n    // So we have nested zones declared the onHasTask hook, in this case,\n    // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n    // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n    // when the current zone is the same as the target zone.\n    if (current !== target) {\n      return;\n    }\n    if (hasTaskState.change == 'microTask') {\n      this._pendingMicroTasks = hasTaskState.microTask;\n      this._finishCallbackIfDone();\n    } else if (hasTaskState.change == 'macroTask') {\n      this._pendingMacroTasks = hasTaskState.macroTask;\n      this._finishCallbackIfDone();\n    }\n  }\n}\nfunction patchAsyncTest(Zone) {\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n  Zone.__load_patch('asynctest', (global, Zone, api) => {\n    /**\n     * Wraps a test function in an asynchronous test zone. The test will automatically\n     * complete when all asynchronous calls within this zone are done.\n     */\n    Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n      // If we're running using the Jasmine test framework, adapt to call the 'done'\n      // function when asynchronous activity is finished.\n      if (global.jasmine) {\n        // Not using an arrow function to preserve context passed from call site\n        return function (done) {\n          if (!done) {\n            // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n            // fake it here and assume sync.\n            done = function () {};\n            done.fail = function (e) {\n              throw e;\n            };\n          }\n          runInTestZone(fn, this, done, err => {\n            if (typeof err === 'string') {\n              return done.fail(new Error(err));\n            } else {\n              done.fail(err);\n            }\n          });\n        };\n      }\n      // Otherwise, return a promise which will resolve when asynchronous activity\n      // is finished. This will be correctly consumed by the Mocha framework with\n      // it('...', async(myFn)); or can be used in a custom framework.\n      // Not using an arrow function to preserve context passed from call site\n      return function () {\n        return new Promise((finishCallback, failCallback) => {\n          runInTestZone(fn, this, finishCallback, failCallback);\n        });\n      };\n    };\n    function runInTestZone(fn, context, finishCallback, failCallback) {\n      const currentZone = Zone.current;\n      const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n      if (AsyncTestZoneSpec === undefined) {\n        throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/async-test');\n      }\n      const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n      if (!ProxyZoneSpec) {\n        throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n      }\n      const proxyZoneSpec = ProxyZoneSpec.get();\n      ProxyZoneSpec.assertPresent();\n      // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n      // If we do it in ProxyZone then we will get to infinite recursion.\n      const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n      const previousDelegate = proxyZoneSpec.getDelegate();\n      proxyZone.parent.run(() => {\n        const testZoneSpec = new AsyncTestZoneSpec(() => {\n          // Need to restore the original zone.\n          if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n            // Only reset the zone spec if it's\n            // still this one. Otherwise, assume\n            // it's OK.\n            proxyZoneSpec.setDelegate(previousDelegate);\n          }\n          testZoneSpec.unPatchPromiseForTest();\n          currentZone.run(() => {\n            finishCallback();\n          });\n        }, error => {\n          // Need to restore the original zone.\n          if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n            // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n            proxyZoneSpec.setDelegate(previousDelegate);\n          }\n          testZoneSpec.unPatchPromiseForTest();\n          currentZone.run(() => {\n            failCallback(error);\n          });\n        }, 'test');\n        proxyZoneSpec.setDelegate(testZoneSpec);\n        testZoneSpec.patchPromiseForTest();\n      });\n      return Zone.current.runGuarded(fn, context);\n    }\n  });\n}\nconst global$1 = typeof window === 'object' && window || typeof self === 'object' && self || globalThis.global;\nconst OriginalDate = global$1.Date;\n// Since when we compile this file to `es2015`, and if we define\n// this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n// there will be an error which is `Cannot assign to read only property 'prototype'`\n// so we need to use function implementation here.\nfunction FakeDate() {\n  if (arguments.length === 0) {\n    const d = new OriginalDate();\n    d.setTime(FakeDate.now());\n    return d;\n  } else {\n    const args = Array.prototype.slice.call(arguments);\n    return new OriginalDate(...args);\n  }\n}\nFakeDate.now = function () {\n  const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n  if (fakeAsyncTestZoneSpec) {\n    return fakeAsyncTestZoneSpec.getFakeSystemTime();\n  }\n  return OriginalDate.now.apply(this, arguments);\n};\nFakeDate.UTC = OriginalDate.UTC;\nFakeDate.parse = OriginalDate.parse;\n// keep a reference for zone patched timer function\nlet patchedTimers;\nconst timeoutCallback = function () {};\nclass Scheduler {\n  // Next scheduler id.\n  static {\n    this.nextNodeJSId = 1;\n  }\n  static {\n    this.nextId = -1;\n  }\n  constructor() {\n    // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n    this._schedulerQueue = [];\n    // Current simulated time in millis.\n    this._currentTickTime = 0;\n    // Current fake system base time in millis.\n    this._currentFakeBaseSystemTime = OriginalDate.now();\n    // track requeuePeriodicTimer\n    this._currentTickRequeuePeriodicEntries = [];\n  }\n  static getNextId() {\n    const id = patchedTimers.nativeSetTimeout.call(global$1, timeoutCallback, 0);\n    patchedTimers.nativeClearTimeout.call(global$1, id);\n    if (typeof id === 'number') {\n      return id;\n    }\n    // in NodeJS, we just use a number for fakeAsync, since it will not\n    // conflict with native TimeoutId\n    return Scheduler.nextNodeJSId++;\n  }\n  getCurrentTickTime() {\n    return this._currentTickTime;\n  }\n  getFakeSystemTime() {\n    return this._currentFakeBaseSystemTime + this._currentTickTime;\n  }\n  setFakeBaseSystemTime(fakeBaseSystemTime) {\n    this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n  }\n  getRealSystemTime() {\n    return OriginalDate.now();\n  }\n  scheduleFunction(cb, delay, options) {\n    options = {\n      ...{\n        args: [],\n        isPeriodic: false,\n        isRequestAnimationFrame: false,\n        id: -1,\n        isRequeuePeriodic: false\n      },\n      ...options\n    };\n    let currentId = options.id < 0 ? Scheduler.nextId : options.id;\n    Scheduler.nextId = Scheduler.getNextId();\n    let endTime = this._currentTickTime + delay;\n    // Insert so that scheduler queue remains sorted by end time.\n    let newEntry = {\n      endTime: endTime,\n      id: currentId,\n      func: cb,\n      args: options.args,\n      delay: delay,\n      isPeriodic: options.isPeriodic,\n      isRequestAnimationFrame: options.isRequestAnimationFrame\n    };\n    if (options.isRequeuePeriodic) {\n      this._currentTickRequeuePeriodicEntries.push(newEntry);\n    }\n    let i = 0;\n    for (; i < this._schedulerQueue.length; i++) {\n      let currentEntry = this._schedulerQueue[i];\n      if (newEntry.endTime < currentEntry.endTime) {\n        break;\n      }\n    }\n    this._schedulerQueue.splice(i, 0, newEntry);\n    return currentId;\n  }\n  removeScheduledFunctionWithId(id) {\n    for (let i = 0; i < this._schedulerQueue.length; i++) {\n      if (this._schedulerQueue[i].id == id) {\n        this._schedulerQueue.splice(i, 1);\n        break;\n      }\n    }\n  }\n  removeAll() {\n    this._schedulerQueue = [];\n  }\n  getTimerCount() {\n    return this._schedulerQueue.length;\n  }\n  tickToNext(step = 1, doTick, tickOptions) {\n    if (this._schedulerQueue.length < step) {\n      return;\n    }\n    // Find the last task currently queued in the scheduler queue and tick\n    // till that time.\n    const startTime = this._currentTickTime;\n    const targetTask = this._schedulerQueue[step - 1];\n    this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n  }\n  tick(millis = 0, doTick, tickOptions) {\n    let finalTime = this._currentTickTime + millis;\n    let lastCurrentTime = 0;\n    tickOptions = Object.assign({\n      processNewMacroTasksSynchronously: true\n    }, tickOptions);\n    // we need to copy the schedulerQueue so nested timeout\n    // will not be wrongly called in the current tick\n    // https://github.com/angular/angular/issues/33799\n    const schedulerQueue = tickOptions.processNewMacroTasksSynchronously ? this._schedulerQueue : this._schedulerQueue.slice();\n    if (schedulerQueue.length === 0 && doTick) {\n      doTick(millis);\n      return;\n    }\n    while (schedulerQueue.length > 0) {\n      // clear requeueEntries before each loop\n      this._currentTickRequeuePeriodicEntries = [];\n      let current = schedulerQueue[0];\n      if (finalTime < current.endTime) {\n        // Done processing the queue since it's sorted by endTime.\n        break;\n      } else {\n        // Time to run scheduled function. Remove it from the head of queue.\n        let current = schedulerQueue.shift();\n        if (!tickOptions.processNewMacroTasksSynchronously) {\n          const idx = this._schedulerQueue.indexOf(current);\n          if (idx >= 0) {\n            this._schedulerQueue.splice(idx, 1);\n          }\n        }\n        lastCurrentTime = this._currentTickTime;\n        this._currentTickTime = current.endTime;\n        if (doTick) {\n          doTick(this._currentTickTime - lastCurrentTime);\n        }\n        let retval = current.func.apply(global$1, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n        if (!retval) {\n          // Uncaught exception in the current scheduled function. Stop processing the queue.\n          break;\n        }\n        // check is there any requeue periodic entry is added in\n        // current loop, if there is, we need to add to current loop\n        if (!tickOptions.processNewMacroTasksSynchronously) {\n          this._currentTickRequeuePeriodicEntries.forEach(newEntry => {\n            let i = 0;\n            for (; i < schedulerQueue.length; i++) {\n              const currentEntry = schedulerQueue[i];\n              if (newEntry.endTime < currentEntry.endTime) {\n                break;\n              }\n            }\n            schedulerQueue.splice(i, 0, newEntry);\n          });\n        }\n      }\n    }\n    lastCurrentTime = this._currentTickTime;\n    this._currentTickTime = finalTime;\n    if (doTick) {\n      doTick(this._currentTickTime - lastCurrentTime);\n    }\n  }\n  flushOnlyPendingTimers(doTick) {\n    if (this._schedulerQueue.length === 0) {\n      return 0;\n    }\n    // Find the last task currently queued in the scheduler queue and tick\n    // till that time.\n    const startTime = this._currentTickTime;\n    const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n    this.tick(lastTask.endTime - startTime, doTick, {\n      processNewMacroTasksSynchronously: false\n    });\n    return this._currentTickTime - startTime;\n  }\n  flush(limit = 20, flushPeriodic = false, doTick) {\n    if (flushPeriodic) {\n      return this.flushPeriodic(doTick);\n    } else {\n      return this.flushNonPeriodic(limit, doTick);\n    }\n  }\n  flushPeriodic(doTick) {\n    if (this._schedulerQueue.length === 0) {\n      return 0;\n    }\n    // Find the last task currently queued in the scheduler queue and tick\n    // till that time.\n    const startTime = this._currentTickTime;\n    const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n    this.tick(lastTask.endTime - startTime, doTick);\n    return this._currentTickTime - startTime;\n  }\n  flushNonPeriodic(limit, doTick) {\n    const startTime = this._currentTickTime;\n    let lastCurrentTime = 0;\n    let count = 0;\n    while (this._schedulerQueue.length > 0) {\n      count++;\n      if (count > limit) {\n        throw new Error('flush failed after reaching the limit of ' + limit + ' tasks. Does your code use a polling timeout?');\n      }\n      // flush only non-periodic timers.\n      // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n      if (this._schedulerQueue.filter(task => !task.isPeriodic && !task.isRequestAnimationFrame).length === 0) {\n        break;\n      }\n      const current = this._schedulerQueue.shift();\n      lastCurrentTime = this._currentTickTime;\n      this._currentTickTime = current.endTime;\n      if (doTick) {\n        // Update any secondary schedulers like Jasmine mock Date.\n        doTick(this._currentTickTime - lastCurrentTime);\n      }\n      const retval = current.func.apply(global$1, current.args);\n      if (!retval) {\n        // Uncaught exception in the current scheduled function. Stop processing the queue.\n        break;\n      }\n    }\n    return this._currentTickTime - startTime;\n  }\n}\nclass FakeAsyncTestZoneSpec {\n  static assertInZone() {\n    if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n      throw new Error('The code should be running in the fakeAsync zone to call this function');\n    }\n  }\n  constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n    this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n    this.macroTaskOptions = macroTaskOptions;\n    this._scheduler = new Scheduler();\n    this._microtasks = [];\n    this._lastError = null;\n    this._uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n    this.pendingPeriodicTimers = [];\n    this.pendingTimers = [];\n    this.patchDateLocked = false;\n    this.properties = {\n      'FakeAsyncTestZoneSpec': this\n    };\n    this.name = 'fakeAsyncTestZone for ' + namePrefix;\n    // in case user can't access the construction of FakeAsyncTestSpec\n    // user can also define macroTaskOptions by define a global variable.\n    if (!this.macroTaskOptions) {\n      this.macroTaskOptions = global$1[Zone.__symbol__('FakeAsyncTestMacroTask')];\n    }\n  }\n  _fnAndFlush(fn, completers) {\n    return (...args) => {\n      fn.apply(global$1, args);\n      if (this._lastError === null) {\n        // Success\n        if (completers.onSuccess != null) {\n          completers.onSuccess.apply(global$1);\n        }\n        // Flush microtasks only on success.\n        this.flushMicrotasks();\n      } else {\n        // Failure\n        if (completers.onError != null) {\n          completers.onError.apply(global$1);\n        }\n      }\n      // Return true if there were no errors, false otherwise.\n      return this._lastError === null;\n    };\n  }\n  static _removeTimer(timers, id) {\n    let index = timers.indexOf(id);\n    if (index > -1) {\n      timers.splice(index, 1);\n    }\n  }\n  _dequeueTimer(id) {\n    return () => {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n    };\n  }\n  _requeuePeriodicTimer(fn, interval, args, id) {\n    return () => {\n      // Requeue the timer callback if it's not been canceled.\n      if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n        this._scheduler.scheduleFunction(fn, interval, {\n          args,\n          isPeriodic: true,\n          id,\n          isRequeuePeriodic: true\n        });\n      }\n    };\n  }\n  _dequeuePeriodicTimer(id) {\n    return () => {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n    };\n  }\n  _setTimeout(fn, delay, args, isTimer = true) {\n    let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n    // Queue the callback and dequeue the timer on success and error.\n    let cb = this._fnAndFlush(fn, {\n      onSuccess: removeTimerFn,\n      onError: removeTimerFn\n    });\n    let id = this._scheduler.scheduleFunction(cb, delay, {\n      args,\n      isRequestAnimationFrame: !isTimer\n    });\n    if (isTimer) {\n      this.pendingTimers.push(id);\n    }\n    return id;\n  }\n  _clearTimeout(id) {\n    FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n    this._scheduler.removeScheduledFunctionWithId(id);\n  }\n  _setInterval(fn, interval, args) {\n    let id = Scheduler.nextId;\n    let completers = {\n      onSuccess: null,\n      onError: this._dequeuePeriodicTimer(id)\n    };\n    let cb = this._fnAndFlush(fn, completers);\n    // Use the callback created above to requeue on success.\n    completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n    // Queue the callback and dequeue the periodic timer only on error.\n    this._scheduler.scheduleFunction(cb, interval, {\n      args,\n      isPeriodic: true\n    });\n    this.pendingPeriodicTimers.push(id);\n    return id;\n  }\n  _clearInterval(id) {\n    FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n    this._scheduler.removeScheduledFunctionWithId(id);\n  }\n  _resetLastErrorAndThrow() {\n    let error = this._lastError || this._uncaughtPromiseErrors[0];\n    this._uncaughtPromiseErrors.length = 0;\n    this._lastError = null;\n    throw error;\n  }\n  getCurrentTickTime() {\n    return this._scheduler.getCurrentTickTime();\n  }\n  getFakeSystemTime() {\n    return this._scheduler.getFakeSystemTime();\n  }\n  setFakeBaseSystemTime(realTime) {\n    this._scheduler.setFakeBaseSystemTime(realTime);\n  }\n  getRealSystemTime() {\n    return this._scheduler.getRealSystemTime();\n  }\n  static patchDate() {\n    if (!!global$1[Zone.__symbol__('disableDatePatching')]) {\n      // we don't want to patch global Date\n      // because in some case, global Date\n      // is already being patched, we need to provide\n      // an option to let user still use their\n      // own version of Date.\n      return;\n    }\n    if (global$1['Date'] === FakeDate) {\n      // already patched\n      return;\n    }\n    global$1['Date'] = FakeDate;\n    FakeDate.prototype = OriginalDate.prototype;\n    // try check and reset timers\n    // because jasmine.clock().install() may\n    // have replaced the global timer\n    FakeAsyncTestZoneSpec.checkTimerPatch();\n  }\n  static resetDate() {\n    if (global$1['Date'] === FakeDate) {\n      global$1['Date'] = OriginalDate;\n    }\n  }\n  static checkTimerPatch() {\n    if (!patchedTimers) {\n      throw new Error('Expected timers to have been patched.');\n    }\n    if (global$1.setTimeout !== patchedTimers.setTimeout) {\n      global$1.setTimeout = patchedTimers.setTimeout;\n      global$1.clearTimeout = patchedTimers.clearTimeout;\n    }\n    if (global$1.setInterval !== patchedTimers.setInterval) {\n      global$1.setInterval = patchedTimers.setInterval;\n      global$1.clearInterval = patchedTimers.clearInterval;\n    }\n  }\n  lockDatePatch() {\n    this.patchDateLocked = true;\n    FakeAsyncTestZoneSpec.patchDate();\n  }\n  unlockDatePatch() {\n    this.patchDateLocked = false;\n    FakeAsyncTestZoneSpec.resetDate();\n  }\n  tickToNext(steps = 1, doTick, tickOptions = {\n    processNewMacroTasksSynchronously: true\n  }) {\n    if (steps <= 0) {\n      return;\n    }\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    this._scheduler.tickToNext(steps, doTick, tickOptions);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n  }\n  tick(millis = 0, doTick, tickOptions = {\n    processNewMacroTasksSynchronously: true\n  }) {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    this._scheduler.tick(millis, doTick, tickOptions);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n  }\n  flushMicrotasks() {\n    FakeAsyncTestZoneSpec.assertInZone();\n    const flushErrors = () => {\n      if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n        // If there is an error stop processing the microtask queue and rethrow the error.\n        this._resetLastErrorAndThrow();\n      }\n    };\n    while (this._microtasks.length > 0) {\n      let microtask = this._microtasks.shift();\n      microtask.func.apply(microtask.target, microtask.args);\n    }\n    flushErrors();\n  }\n  flush(limit, flushPeriodic, doTick) {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n    return elapsed;\n  }\n  flushOnlyPendingTimers(doTick) {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n    return elapsed;\n  }\n  removeAllTimers() {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this._scheduler.removeAll();\n    this.pendingPeriodicTimers = [];\n    this.pendingTimers = [];\n  }\n  getTimerCount() {\n    return this._scheduler.getTimerCount() + this._microtasks.length;\n  }\n  onScheduleTask(delegate, current, target, task) {\n    switch (task.type) {\n      case 'microTask':\n        let args = task.data && task.data.args;\n        // should pass additional arguments to callback if have any\n        // currently we know process.nextTick will have such additional\n        // arguments\n        let additionalArgs;\n        if (args) {\n          let callbackIndex = task.data.cbIdx;\n          if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n            additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n          }\n        }\n        this._microtasks.push({\n          func: task.invoke,\n          args: additionalArgs,\n          target: task.data && task.data.target\n        });\n        break;\n      case 'macroTask':\n        switch (task.source) {\n          case 'setTimeout':\n            task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n            break;\n          case 'setImmediate':\n            task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n            break;\n          case 'setInterval':\n            task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n            break;\n          case 'XMLHttpRequest.send':\n            throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' + task.data['url']);\n          case 'requestAnimationFrame':\n          case 'webkitRequestAnimationFrame':\n          case 'mozRequestAnimationFrame':\n            // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n            // (60 frames per second)\n            task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n            break;\n          default:\n            // user can define which macroTask they want to support by passing\n            // macroTaskOptions\n            const macroTaskOption = this.findMacroTaskOption(task);\n            if (macroTaskOption) {\n              const args = task.data && task.data['args'];\n              const delay = args && args.length > 1 ? args[1] : 0;\n              let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n              if (!!macroTaskOption.isPeriodic) {\n                // periodic macroTask, use setInterval to simulate\n                task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                task.data.isPeriodic = true;\n              } else {\n                // not periodic, use setTimeout to simulate\n                task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n              }\n              break;\n            }\n            throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n        }\n        break;\n      case 'eventTask':\n        task = delegate.scheduleTask(target, task);\n        break;\n    }\n    return task;\n  }\n  onCancelTask(delegate, current, target, task) {\n    switch (task.source) {\n      case 'setTimeout':\n      case 'requestAnimationFrame':\n      case 'webkitRequestAnimationFrame':\n      case 'mozRequestAnimationFrame':\n        return this._clearTimeout(task.data['handleId']);\n      case 'setInterval':\n        return this._clearInterval(task.data['handleId']);\n      default:\n        // user can define which macroTask they want to support by passing\n        // macroTaskOptions\n        const macroTaskOption = this.findMacroTaskOption(task);\n        if (macroTaskOption) {\n          const handleId = task.data['handleId'];\n          return macroTaskOption.isPeriodic ? this._clearInterval(handleId) : this._clearTimeout(handleId);\n        }\n        return delegate.cancelTask(target, task);\n    }\n  }\n  onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n    try {\n      FakeAsyncTestZoneSpec.patchDate();\n      return delegate.invoke(target, callback, applyThis, applyArgs, source);\n    } finally {\n      if (!this.patchDateLocked) {\n        FakeAsyncTestZoneSpec.resetDate();\n      }\n    }\n  }\n  findMacroTaskOption(task) {\n    if (!this.macroTaskOptions) {\n      return null;\n    }\n    for (let i = 0; i < this.macroTaskOptions.length; i++) {\n      const macroTaskOption = this.macroTaskOptions[i];\n      if (macroTaskOption.source === task.source) {\n        return macroTaskOption;\n      }\n    }\n    return null;\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    this._lastError = error;\n    return false; // Don't propagate error to parent zone.\n  }\n}\nlet _fakeAsyncTestZoneSpec = null;\nfunction getProxyZoneSpec() {\n  return Zone && Zone['ProxyZoneSpec'];\n}\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nfunction resetFakeAsyncZone() {\n  if (_fakeAsyncTestZoneSpec) {\n    _fakeAsyncTestZoneSpec.unlockDatePatch();\n  }\n  _fakeAsyncTestZoneSpec = null;\n  // in node.js testing we may not have ProxyZoneSpec in which case there is nothing to reset.\n  getProxyZoneSpec() && getProxyZoneSpec().assertPresent().resetDelegate();\n}\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * When flush is `false`, if there are any pending timers at the end of the function,\n * an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @param options\n *     flush: when true, will drain the macrotask queue after the test function completes.\n * @returns The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nfunction fakeAsync(fn, options = {}) {\n  const {\n    flush = false\n  } = options;\n  // Not using an arrow function to preserve context passed from call site\n  const fakeAsyncFn = function (...args) {\n    const ProxyZoneSpec = getProxyZoneSpec();\n    if (!ProxyZoneSpec) {\n      throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n    }\n    const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n    if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n      throw new Error('fakeAsync() calls can not be nested');\n    }\n    try {\n      // in case jasmine.clock init a fakeAsyncTestZoneSpec\n      if (!_fakeAsyncTestZoneSpec) {\n        const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n        if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n          throw new Error('fakeAsync() calls can not be nested');\n        }\n        _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n      }\n      let res;\n      const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n      proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n      _fakeAsyncTestZoneSpec.lockDatePatch();\n      try {\n        res = fn.apply(this, args);\n        if (flush) {\n          _fakeAsyncTestZoneSpec.flush(20, true);\n        } else {\n          flushMicrotasks();\n        }\n      } finally {\n        proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n      }\n      if (!flush) {\n        if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` + `periodic timer(s) still in the queue.`);\n        }\n        if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n        }\n      }\n      return res;\n    } finally {\n      resetFakeAsyncZone();\n    }\n  };\n  fakeAsyncFn.isFakeAsync = true;\n  return fakeAsyncFn;\n}\nfunction _getFakeAsyncZoneSpec() {\n  if (_fakeAsyncTestZoneSpec == null) {\n    _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n    if (_fakeAsyncTestZoneSpec == null) {\n      throw new Error('The code should be running in the fakeAsync zone to call this function');\n    }\n  }\n  return _fakeAsyncTestZoneSpec;\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer\n * callback has been executed.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nfunction tick(millis = 0, ignoreNestedTimeout = false) {\n  _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns The simulated time elapsed, in millis.\n *\n * @experimental\n */\nfunction flush(maxTurns) {\n  return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nfunction discardPeriodicTasks() {\n  const zoneSpec = _getFakeAsyncZoneSpec();\n  zoneSpec.pendingPeriodicTimers;\n  zoneSpec.pendingPeriodicTimers.length = 0;\n}\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nfunction flushMicrotasks() {\n  _getFakeAsyncZoneSpec().flushMicrotasks();\n}\nfunction patchFakeAsyncTest(Zone) {\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n  Zone.__load_patch('fakeasync', (global, Zone, api) => {\n    Zone[api.symbol('fakeAsyncTest')] = {\n      resetFakeAsyncZone,\n      flushMicrotasks,\n      discardPeriodicTasks,\n      tick,\n      flush,\n      fakeAsync\n    };\n  }, true);\n  patchedTimers = {\n    setTimeout: global$1.setTimeout,\n    setInterval: global$1.setInterval,\n    clearTimeout: global$1.clearTimeout,\n    clearInterval: global$1.clearInterval,\n    nativeSetTimeout: global$1[Zone.__symbol__('setTimeout')],\n    nativeClearTimeout: global$1[Zone.__symbol__('clearTimeout')]\n  };\n  Scheduler.nextId = Scheduler.getNextId();\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction patchLongStackTrace(Zone) {\n  const NEWLINE = '\\n';\n  const IGNORE_FRAMES = {};\n  const creationTrace = '__creationTrace__';\n  const ERROR_TAG = 'STACKTRACE TRACKING';\n  const SEP_TAG = '__SEP_TAG__';\n  let sepTemplate = SEP_TAG + '@[native]';\n  class LongStackTrace {\n    constructor() {\n      this.error = getStacktrace();\n      this.timestamp = new Date();\n    }\n  }\n  function getStacktraceWithUncaughtError() {\n    return new Error(ERROR_TAG);\n  }\n  function getStacktraceWithCaughtError() {\n    try {\n      throw getStacktraceWithUncaughtError();\n    } catch (err) {\n      return err;\n    }\n  }\n  // Some implementations of exception handling don't create a stack trace if the exception\n  // isn't thrown, however it's faster not to actually throw the exception.\n  const error = getStacktraceWithUncaughtError();\n  const caughtError = getStacktraceWithCaughtError();\n  const getStacktrace = error.stack ? getStacktraceWithUncaughtError : caughtError.stack ? getStacktraceWithCaughtError : getStacktraceWithUncaughtError;\n  function getFrames(error) {\n    return error.stack ? error.stack.split(NEWLINE) : [];\n  }\n  function addErrorStack(lines, error) {\n    let trace = getFrames(error);\n    for (let i = 0; i < trace.length; i++) {\n      const frame = trace[i];\n      // Filter out the Frames which are part of stack capturing.\n      if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n        lines.push(trace[i]);\n      }\n    }\n  }\n  function renderLongStackTrace(frames, stack) {\n    const longTrace = [stack ? stack.trim() : ''];\n    if (frames) {\n      let timestamp = new Date().getTime();\n      for (let i = 0; i < frames.length; i++) {\n        const traceFrames = frames[i];\n        const lastTime = traceFrames.timestamp;\n        let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n        separator = separator.replace(/[^\\w\\d]/g, '_');\n        longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n        addErrorStack(longTrace, traceFrames.error);\n        timestamp = lastTime.getTime();\n      }\n    }\n    return longTrace.join(NEWLINE);\n  }\n  // if Error.stackTraceLimit is 0, means stack trace\n  // is disabled, so we don't need to generate long stack trace\n  // this will improve performance in some test(some test will\n  // set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\n  function stackTracesEnabled() {\n    // Cast through any since this property only exists on Error in the nodejs\n    // typings.\n    return Error.stackTraceLimit > 0;\n  }\n  Zone['longStackTraceZoneSpec'] = {\n    name: 'long-stack-trace',\n    longStackTraceLimit: 10,\n    // Max number of task to keep the stack trace for.\n    // add a getLongStackTrace method in spec to\n    // handle handled reject promise error.\n    getLongStackTrace: function (error) {\n      if (!error) {\n        return undefined;\n      }\n      const trace = error[Zone.__symbol__('currentTaskTrace')];\n      if (!trace) {\n        return error.stack;\n      }\n      return renderLongStackTrace(trace, error.stack);\n    },\n    onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n      if (stackTracesEnabled()) {\n        const currentTask = Zone.currentTask;\n        let trace = currentTask && currentTask.data && currentTask.data[creationTrace] || [];\n        trace = [new LongStackTrace()].concat(trace);\n        if (trace.length > this.longStackTraceLimit) {\n          trace.length = this.longStackTraceLimit;\n        }\n        if (!task.data) task.data = {};\n        if (task.type === 'eventTask') {\n          // Fix issue https://github.com/angular/zone.js/issues/1195,\n          // For event task of browser, by default, all task will share a\n          // singleton instance of data object, we should create a new one here\n          // The cast to `any` is required to workaround a closure bug which wrongly applies\n          // URL sanitization rules to .data access.\n          task.data = {\n            ...task.data\n          };\n        }\n        task.data[creationTrace] = trace;\n      }\n      return parentZoneDelegate.scheduleTask(targetZone, task);\n    },\n    onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n      if (stackTracesEnabled()) {\n        const parentTask = Zone.currentTask || error.task;\n        if (error instanceof Error && parentTask) {\n          const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n          try {\n            error.stack = error.longStack = longStack;\n          } catch (err) {}\n        }\n      }\n      return parentZoneDelegate.handleError(targetZone, error);\n    }\n  };\n  function captureStackTraces(stackTraces, count) {\n    if (count > 0) {\n      stackTraces.push(getFrames(new LongStackTrace().error));\n      captureStackTraces(stackTraces, count - 1);\n    }\n  }\n  function computeIgnoreFrames() {\n    if (!stackTracesEnabled()) {\n      return;\n    }\n    const frames = [];\n    captureStackTraces(frames, 2);\n    const frames1 = frames[0];\n    const frames2 = frames[1];\n    for (let i = 0; i < frames1.length; i++) {\n      const frame1 = frames1[i];\n      if (frame1.indexOf(ERROR_TAG) == -1) {\n        let match = frame1.match(/^\\s*at\\s+/);\n        if (match) {\n          sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n          break;\n        }\n      }\n    }\n    for (let i = 0; i < frames1.length; i++) {\n      const frame1 = frames1[i];\n      const frame2 = frames2[i];\n      if (frame1 === frame2) {\n        IGNORE_FRAMES[frame1] = true;\n      } else {\n        break;\n      }\n    }\n  }\n  computeIgnoreFrames();\n}\nclass ProxyZoneSpec {\n  static get() {\n    return Zone.current.get('ProxyZoneSpec');\n  }\n  static isLoaded() {\n    return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n  }\n  static assertPresent() {\n    if (!ProxyZoneSpec.isLoaded()) {\n      throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n    }\n    return ProxyZoneSpec.get();\n  }\n  constructor(defaultSpecDelegate = null) {\n    this.defaultSpecDelegate = defaultSpecDelegate;\n    this.name = 'ProxyZone';\n    this._delegateSpec = null;\n    this.properties = {\n      'ProxyZoneSpec': this\n    };\n    this.propertyKeys = null;\n    this.lastTaskState = null;\n    this.isNeedToTriggerHasTask = false;\n    this.tasks = [];\n    this.setDelegate(defaultSpecDelegate);\n  }\n  setDelegate(delegateSpec) {\n    const isNewDelegate = this._delegateSpec !== delegateSpec;\n    this._delegateSpec = delegateSpec;\n    this.propertyKeys && this.propertyKeys.forEach(key => delete this.properties[key]);\n    this.propertyKeys = null;\n    if (delegateSpec && delegateSpec.properties) {\n      this.propertyKeys = Object.keys(delegateSpec.properties);\n      this.propertyKeys.forEach(k => this.properties[k] = delegateSpec.properties[k]);\n    }\n    // if a new delegateSpec was set, check if we need to trigger hasTask\n    if (isNewDelegate && this.lastTaskState && (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n      this.isNeedToTriggerHasTask = true;\n    }\n  }\n  getDelegate() {\n    return this._delegateSpec;\n  }\n  resetDelegate() {\n    this.getDelegate();\n    this.setDelegate(this.defaultSpecDelegate);\n  }\n  tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n    if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n      // last delegateSpec has microTask or macroTask\n      // should call onHasTask in current delegateSpec\n      this.isNeedToTriggerHasTask = false;\n      this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n    }\n  }\n  removeFromTasks(task) {\n    if (!this.tasks) {\n      return;\n    }\n    for (let i = 0; i < this.tasks.length; i++) {\n      if (this.tasks[i] === task) {\n        this.tasks.splice(i, 1);\n        return;\n      }\n    }\n  }\n  getAndClearPendingTasksInfo() {\n    if (this.tasks.length === 0) {\n      return '';\n    }\n    const taskInfo = this.tasks.map(task => {\n      const dataInfo = task.data && Object.keys(task.data).map(key => {\n        return key + ':' + task.data[key];\n      }).join(',');\n      return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n    });\n    const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n    // clear tasks\n    this.tasks = [];\n    return pendingTasksInfo;\n  }\n  onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n    if (this._delegateSpec && this._delegateSpec.onFork) {\n      return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n    } else {\n      return parentZoneDelegate.fork(targetZone, zoneSpec);\n    }\n  }\n  onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n    if (this._delegateSpec && this._delegateSpec.onIntercept) {\n      return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n    } else {\n      return parentZoneDelegate.intercept(targetZone, delegate, source);\n    }\n  }\n  onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvoke) {\n      return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n    } else {\n      return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n    }\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    if (this._delegateSpec && this._delegateSpec.onHandleError) {\n      return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n    } else {\n      return parentZoneDelegate.handleError(targetZone, error);\n    }\n  }\n  onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.tasks.push(task);\n    }\n    if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n      return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.scheduleTask(targetZone, task);\n    }\n  }\n  onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n      return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n    } else {\n      return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n    }\n  }\n  onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n      return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.cancelTask(targetZone, task);\n    }\n  }\n  onHasTask(delegate, current, target, hasTaskState) {\n    this.lastTaskState = hasTaskState;\n    if (this._delegateSpec && this._delegateSpec.onHasTask) {\n      this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n    } else {\n      delegate.hasTask(target, hasTaskState);\n    }\n  }\n}\nfunction patchProxyZoneSpec(Zone) {\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['ProxyZoneSpec'] = ProxyZoneSpec;\n}\nfunction patchSyncTest(Zone) {\n  class SyncTestZoneSpec {\n    constructor(namePrefix) {\n      this.runZone = Zone.current;\n      this.name = 'syncTestZone for ' + namePrefix;\n    }\n    onScheduleTask(delegate, current, target, task) {\n      switch (task.type) {\n        case 'microTask':\n        case 'macroTask':\n          throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n        case 'eventTask':\n          task = delegate.scheduleTask(target, task);\n          break;\n      }\n      return task;\n    }\n  }\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n}\nfunction patchPromiseTesting(Zone) {\n  /**\n   * Promise for async/fakeAsync zoneSpec test\n   * can support async operation which not supported by zone.js\n   * such as\n   * it ('test jsonp in AsyncZone', async() => {\n   *   new Promise(res => {\n   *     jsonp(url, (data) => {\n   *       // success callback\n   *       res(data);\n   *     });\n   *   }).then((jsonpResult) => {\n   *     // get jsonp result.\n   *\n   *     // user will expect AsyncZoneSpec wait for\n   *     // then, but because jsonp is not zone aware\n   *     // AsyncZone will finish before then is called.\n   *   });\n   * });\n   */\n  Zone.__load_patch('promisefortest', (global, Zone, api) => {\n    const symbolState = api.symbol('state');\n    const UNRESOLVED = null;\n    const symbolParentUnresolved = api.symbol('parentUnresolved');\n    // patch Promise.prototype.then to keep an internal\n    // number for tracking unresolved chained promise\n    // we will decrease this number when the parent promise\n    // being resolved/rejected and chained promise was\n    // scheduled as a microTask.\n    // so we can know such kind of chained promise still\n    // not resolved in AsyncTestZone\n    Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n      let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n      if (oriThen) {\n        return;\n      }\n      oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n      Promise.prototype.then = function () {\n        const chained = oriThen.apply(this, arguments);\n        if (this[symbolState] === UNRESOLVED) {\n          // parent promise is unresolved.\n          const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n          if (asyncTestZoneSpec) {\n            asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n            chained[symbolParentUnresolved] = true;\n          }\n        }\n        return chained;\n      };\n    };\n    Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n      // restore origin then\n      const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n      if (oriThen) {\n        Promise.prototype.then = oriThen;\n        Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n      }\n    };\n  });\n}\nfunction rollupTesting(Zone) {\n  patchLongStackTrace(Zone);\n  patchProxyZoneSpec(Zone);\n  patchSyncTest(Zone);\n  patchJasmine(Zone);\n  patchJest(Zone);\n  patchMocha(Zone);\n  patchAsyncTest(Zone);\n  patchFakeAsyncTest(Zone);\n  patchPromiseTesting(Zone);\n}\nrollupTesting(Zone);", "map": {"version": 3, "names": ["patchJasmine", "Zone", "__load_patch", "global", "api", "__extends", "d", "b", "p", "hasOwnProperty", "__", "constructor", "prototype", "Object", "create", "Error", "jest", "jasmine", "SyncTestZoneSpec", "ProxyZoneSpec", "ambientZone", "current", "symbol", "__symbol__", "disablePatchingJasmineClock", "enableAutoFakeAsyncWhenClockPatched", "ignoreUnhandledRejection", "globalErrors", "GlobalErrors", "instance", "originalInstall", "install", "isNode", "process", "on", "originalHandlers", "listeners", "eventListeners", "result", "apply", "arguments", "removeAllListeners", "for<PERSON>ach", "handler", "addEventListener", "jasmineEnv", "getEnv", "methodName", "originalJasmineFn", "description", "specDefinitions", "call", "wrapDescribeInZone", "timeout", "wrapTestInZone", "originalClockFn", "clock", "originalTick", "tick", "fakeAsyncZoneSpec", "get", "originalMockDate", "mockDate", "dateTime", "length", "Date", "setFakeBaseSystemTime", "getTime", "FakeAsyncTestZoneSpec", "originalCreateSpyObj", "createSpyObj", "args", "Array", "slice", "propertyNames", "spyObj", "defineProperty", "obj", "attributes", "configurable", "enumerable", "describeBody", "syncZone", "fork", "run", "runInTestZone", "testBody", "applyThis", "queueRunner", "done", "isClockInstalled", "testProxyZoneSpec", "testProxyZone", "fakeAsyncModule", "fakeAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "ZoneQueueRunner", "attrs", "onComplete", "fn", "scheduleMicroTask", "nativeSetTimeout", "nativeClearTimeout", "setTimeout", "clearTimeout", "UserContext", "userContext", "onException", "error", "message", "proxyZoneSpec", "pendingTasksInfo", "getAndClearPendingTasksInfo", "err", "execute", "zone", "isChildOfAmbientZone", "parent", "name", "currentTask", "patchJest", "context", "rootZone", "proxyZone", "wrapDescribeFactoryInZone", "originalJestFn", "tableArgs", "originalDescribeFn", "wrapTestFactoryInZone", "isTestFunc", "wrappedFunc", "isFakeAsync", "writable", "each", "describe", "only", "fdescribe", "skip", "xdescribe", "todo", "it", "fit", "xit", "test", "patchJestObject", "Timer", "isModern", "isPatchingFakeTimer", "isInTestFunc", "patchMethod", "delegate", "self", "getRealSystemTime", "flushMicrotasks", "flush", "flushOnlyPendingTimers", "tickToNext", "removeAllTimers", "getTimerCount", "patchMocha", "<PERSON><PERSON>", "testZone", "suiteZone", "mochaOriginal", "after", "after<PERSON>ach", "before", "beforeEach", "modifyArguments", "syncTest", "asyncTest", "i", "arg", "toString", "wrapSuiteInZone", "suite", "specify", "xspecify", "suiteTeardown", "teardown", "suiteSetup", "setup", "originalRunTest", "originalRun", "Runner", "runTest", "e", "global$2", "globalThis", "symbolPrefix", "__global", "window", "AsyncTestZoneSpec", "symbolParentUnresolved", "finishCallback", "fail<PERSON><PERSON>back", "namePrefix", "_pendingMicroTasks", "_pendingMacroTasks", "_alreadyErrored", "_isSync", "_existingFinishTimer", "entryFunction", "runZone", "unresolvedChainedPromiseCount", "supportWaitUnresolvedChainedPromise", "properties", "isUnresolvedChainedPromisePending", "_finishCallbackIfDone", "patchPromiseForTest", "Promise", "unPatchPromiseForTest", "onScheduleTask", "target", "task", "type", "data", "scheduleTask", "onInvokeTask", "applyArgs", "invokeTask", "onCancelTask", "cancelTask", "onInvoke", "parentZoneDelegate", "currentZone", "targetZone", "source", "invoke", "onHandleError", "handleError", "onHasTask", "hasTaskState", "hasTask", "change", "microTask", "macroTask", "patchAsyncTest", "fail", "undefined", "assertPresent", "getZoneWith", "previousDelegate", "getDelegate", "testZoneSpec", "setDelegate", "runGuarded", "global$1", "OriginalDate", "FakeDate", "setTime", "now", "fakeAsyncTestZoneSpec", "getFakeSystemTime", "UTC", "parse", "patchedTimers", "timeout<PERSON><PERSON><PERSON>", "Scheduler", "nextNodeJSId", "nextId", "_schedulerQueue", "_currentTickTime", "_currentFakeBaseSystemTime", "_currentTickRequeuePeriodicEntries", "getNextId", "id", "getCurrentTickTime", "fakeBaseSystemTime", "scheduleFunction", "cb", "delay", "options", "isPeriodic", "isRequestAnimationFrame", "isRequeuePeriodic", "currentId", "endTime", "newEntry", "func", "push", "currentEntry", "splice", "removeScheduledFunctionWithId", "removeAll", "step", "doTick", "tickOptions", "startTime", "targetTask", "millis", "finalTime", "lastCurrentTime", "assign", "processNewMacroTasksSynchronously", "schedulerQueue", "shift", "idx", "indexOf", "retval", "lastTask", "limit", "flushPeriodic", "flushNonPeriodic", "count", "filter", "assertInZone", "trackPendingRequestAnimationFrame", "macroTaskOptions", "_scheduler", "_microtasks", "_lastError", "_uncaughtPromiseErrors", "pendingPeriodicTimers", "pendingTimers", "patchDateLocked", "_fnAndFlush", "completers", "onSuccess", "onError", "_removeTimer", "timers", "index", "_dequeueTimer", "_requeuePeriodicTimer", "interval", "_dequeuePeriodicTimer", "_setTimeout", "isTimer", "removeTimerFn", "_clearTimeout", "_setInterval", "_clearInterval", "_resetLastErrorAndThrow", "realTime", "patchDate", "checkTimerPatch", "resetDate", "setInterval", "clearInterval", "lockDatePatch", "unlockDatePatch", "steps", "flushErrors", "microtask", "elapsed", "additionalArgs", "callbackIndex", "cbIdx", "macroTaskOption", "findMacroTaskOption", "callback<PERSON><PERSON><PERSON>", "handleId", "callback", "_fakeAsyncTestZoneSpec", "getProxyZoneSpec", "resetFakeAsyncZone", "resetDelegate", "fakeAsyncFn", "res", "lastProxyZoneSpec", "_getFakeAsyncZoneSpec", "ignoreNestedTimeout", "maxTurns", "discardPeriodicTasks", "zoneSpec", "patchFakeAsyncTest", "patchLongStackTrace", "NEWLINE", "IGNORE_FRAMES", "creationTrace", "ERROR_TAG", "SEP_TAG", "sepTemplate", "LongStackTrace", "getStacktrace", "timestamp", "getStacktraceWithUncaughtError", "getStacktraceWithCaughtError", "caughtError", "stack", "getFrames", "split", "addErrorStack", "lines", "trace", "frame", "renderLongStackTrace", "frames", "longTrace", "trim", "traceFrames", "lastTime", "separator", "replace", "join", "stackTracesEnabled", "stackTraceLimit", "longStackTraceLimit", "getLongStackTrace", "concat", "parentTask", "longStack", "captureStackTraces", "stackTraces", "computeIgnoreFrames", "frames1", "frames2", "frame1", "match", "frame2", "isLoaded", "defaultSpecDelegate", "_delegateSpec", "propertyKeys", "lastTaskState", "isNeedToTriggerHasTask", "tasks", "delegateSpec", "isNewDelegate", "key", "keys", "k", "tryTriggerHasTask", "removeFromTasks", "taskInfo", "map", "dataInfo", "onFork", "onIntercept", "intercept", "patchProxyZoneSpec", "patchSyncTest", "patchPromiseTesting", "symbolState", "UNRESOLVED", "ori<PERSON><PERSON>", "then", "chained", "asyncTestZoneSpec", "unpatchPromiseForTest", "rollupTesting"], "sources": ["C:/Users/<USER>/Downloads/Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/zone.js/fesm2015/zone-testing.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\nfunction patchJasmine(Zone) {\n    Zone.__load_patch('jasmine', (global, Zone, api) => {\n        const __extends = function (d, b) {\n            for (const p in b)\n                if (b.hasOwnProperty(p))\n                    d[p] = b[p];\n            function __() {\n                this.constructor = d;\n            }\n            d.prototype =\n                b === null ? Object.create(b) : ((__.prototype = b.prototype), new __());\n        };\n        // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n        // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n        if (!Zone)\n            throw new Error('Missing: zone.js');\n        if (typeof jest !== 'undefined') {\n            // return if jasmine is a light implementation inside jest\n            // in this case, we are running inside jest not jasmine\n            return;\n        }\n        if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n            return;\n        }\n        jasmine['__zone_patch__'] = true;\n        const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        if (!SyncTestZoneSpec)\n            throw new Error('Missing: SyncTestZoneSpec');\n        if (!ProxyZoneSpec)\n            throw new Error('Missing: ProxyZoneSpec');\n        const ambientZone = Zone.current;\n        const symbol = Zone.__symbol__;\n        // whether patch jasmine clock when in fakeAsync\n        const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n        // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n        // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we\n        // also automatically disable the auto jump into fakeAsync feature\n        const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock &&\n            (global[symbol('fakeAsyncPatchLock')] === true ||\n                global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true);\n        const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n        if (!ignoreUnhandledRejection) {\n            const globalErrors = jasmine.GlobalErrors;\n            if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n                jasmine[symbol('GlobalErrors')] = globalErrors;\n                jasmine.GlobalErrors = function () {\n                    const instance = new globalErrors();\n                    const originalInstall = instance.install;\n                    if (originalInstall && !instance[symbol('install')]) {\n                        instance[symbol('install')] = originalInstall;\n                        instance.install = function () {\n                            const isNode = typeof process !== 'undefined' && !!process.on;\n                            // Note: Jasmine checks internally if `process` and `process.on` is defined.\n                            // Otherwise, it installs the browser rejection handler through the\n                            // `global.addEventListener`. This code may be run in the browser environment where\n                            // `process` is not defined, and this will lead to a runtime exception since Webpack 5\n                            // removed automatic Node.js polyfills. Note, that events are named differently, it's\n                            // `unhandledRejection` in Node.js and `unhandledrejection` in the browser.\n                            const originalHandlers = isNode\n                                ? process.listeners('unhandledRejection')\n                                : global.eventListeners('unhandledrejection');\n                            const result = originalInstall.apply(this, arguments);\n                            isNode\n                                ? process.removeAllListeners('unhandledRejection')\n                                : global.removeAllListeners('unhandledrejection');\n                            if (originalHandlers) {\n                                originalHandlers.forEach((handler) => {\n                                    if (isNode) {\n                                        process.on('unhandledRejection', handler);\n                                    }\n                                    else {\n                                        global.addEventListener('unhandledrejection', handler);\n                                    }\n                                });\n                            }\n                            return result;\n                        };\n                    }\n                    return instance;\n                };\n            }\n        }\n        // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n        const jasmineEnv = jasmine.getEnv();\n        ['describe', 'xdescribe', 'fdescribe'].forEach((methodName) => {\n            let originalJasmineFn = jasmineEnv[methodName];\n            jasmineEnv[methodName] = function (description, specDefinitions) {\n                return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n            };\n        });\n        ['it', 'xit', 'fit'].forEach((methodName) => {\n            let originalJasmineFn = jasmineEnv[methodName];\n            jasmineEnv[symbol(methodName)] = originalJasmineFn;\n            jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n                arguments[1] = wrapTestInZone(specDefinitions);\n                return originalJasmineFn.apply(this, arguments);\n            };\n        });\n        ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach((methodName) => {\n            let originalJasmineFn = jasmineEnv[methodName];\n            jasmineEnv[symbol(methodName)] = originalJasmineFn;\n            jasmineEnv[methodName] = function (specDefinitions, timeout) {\n                arguments[0] = wrapTestInZone(specDefinitions);\n                return originalJasmineFn.apply(this, arguments);\n            };\n        });\n        if (!disablePatchingJasmineClock) {\n            // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n            // they can work properly in FakeAsyncTest\n            const originalClockFn = (jasmine[symbol('clock')] = jasmine['clock']);\n            jasmine['clock'] = function () {\n                const clock = originalClockFn.apply(this, arguments);\n                if (!clock[symbol('patched')]) {\n                    clock[symbol('patched')] = symbol('patched');\n                    const originalTick = (clock[symbol('tick')] = clock.tick);\n                    clock.tick = function () {\n                        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                        if (fakeAsyncZoneSpec) {\n                            return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n                        }\n                        return originalTick.apply(this, arguments);\n                    };\n                    const originalMockDate = (clock[symbol('mockDate')] = clock.mockDate);\n                    clock.mockDate = function () {\n                        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                        if (fakeAsyncZoneSpec) {\n                            const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n                            return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function'\n                                ? [dateTime.getTime()]\n                                : arguments);\n                        }\n                        return originalMockDate.apply(this, arguments);\n                    };\n                    // for auto go into fakeAsync feature, we need the flag to enable it\n                    if (enableAutoFakeAsyncWhenClockPatched) {\n                        ['install', 'uninstall'].forEach((methodName) => {\n                            const originalClockFn = (clock[symbol(methodName)] = clock[methodName]);\n                            clock[methodName] = function () {\n                                const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n                                if (FakeAsyncTestZoneSpec) {\n                                    jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                                    return;\n                                }\n                                return originalClockFn.apply(this, arguments);\n                            };\n                        });\n                    }\n                }\n                return clock;\n            };\n        }\n        // monkey patch createSpyObj to make properties enumerable to true\n        if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n            const originalCreateSpyObj = jasmine.createSpyObj;\n            jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n            jasmine.createSpyObj = function () {\n                const args = Array.prototype.slice.call(arguments);\n                const propertyNames = args.length >= 3 ? args[2] : null;\n                let spyObj;\n                if (propertyNames) {\n                    const defineProperty = Object.defineProperty;\n                    Object.defineProperty = function (obj, p, attributes) {\n                        return defineProperty.call(this, obj, p, {\n                            ...attributes,\n                            configurable: true,\n                            enumerable: true,\n                        });\n                    };\n                    try {\n                        spyObj = originalCreateSpyObj.apply(this, args);\n                    }\n                    finally {\n                        Object.defineProperty = defineProperty;\n                    }\n                }\n                else {\n                    spyObj = originalCreateSpyObj.apply(this, args);\n                }\n                return spyObj;\n            };\n        }\n        /**\n         * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n         * synchronous-only zone.\n         */\n        function wrapDescribeInZone(description, describeBody) {\n            return function () {\n                // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n                // error if any asynchronous operations are attempted inside of a `describe`.\n                const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n                return syncZone.run(describeBody, this, arguments);\n            };\n        }\n        function runInTestZone(testBody, applyThis, queueRunner, done) {\n            const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n            queueRunner.testProxyZoneSpec;\n            const testProxyZone = queueRunner.testProxyZone;\n            if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n                // auto run a fakeAsync\n                const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n                if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                    testBody = fakeAsyncModule.fakeAsync(testBody);\n                }\n            }\n            if (done) {\n                return testProxyZone.run(testBody, applyThis, [done]);\n            }\n            else {\n                return testProxyZone.run(testBody, applyThis);\n            }\n        }\n        /**\n         * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n         * execute in a ProxyZone zone.\n         * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n         */\n        function wrapTestInZone(testBody) {\n            // The `done` callback is only passed through if the function expects at least one argument.\n            // Note we have to make a function with correct number of arguments, otherwise jasmine will\n            // think that all functions are sync or async.\n            return (testBody &&\n                (testBody.length\n                    ? function (done) {\n                        return runInTestZone(testBody, this, this.queueRunner, done);\n                    }\n                    : function () {\n                        return runInTestZone(testBody, this, this.queueRunner);\n                    }));\n        }\n        const QueueRunner = jasmine.QueueRunner;\n        jasmine.QueueRunner = (function (_super) {\n            __extends(ZoneQueueRunner, _super);\n            function ZoneQueueRunner(attrs) {\n                if (attrs.onComplete) {\n                    attrs.onComplete = ((fn) => () => {\n                        // All functions are done, clear the test zone.\n                        this.testProxyZone = null;\n                        this.testProxyZoneSpec = null;\n                        ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n                    })(attrs.onComplete);\n                }\n                const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n                const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n                if (nativeSetTimeout) {\n                    // should run setTimeout inside jasmine outside of zone\n                    attrs.timeout = {\n                        setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n                        clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout,\n                    };\n                }\n                // create a userContext to hold the queueRunner itself\n                // so we can access the testProxy in it/xit/beforeEach ...\n                if (jasmine.UserContext) {\n                    if (!attrs.userContext) {\n                        attrs.userContext = new jasmine.UserContext();\n                    }\n                    attrs.userContext.queueRunner = this;\n                }\n                else {\n                    if (!attrs.userContext) {\n                        attrs.userContext = {};\n                    }\n                    attrs.userContext.queueRunner = this;\n                }\n                // patch attrs.onException\n                const onException = attrs.onException;\n                attrs.onException = function (error) {\n                    if (error &&\n                        error.message ===\n                            'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n                        // jasmine timeout, we can make the error message more\n                        // reasonable to tell what tasks are pending\n                        const proxyZoneSpec = this && this.testProxyZoneSpec;\n                        if (proxyZoneSpec) {\n                            const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n                            try {\n                                // try catch here in case error.message is not writable\n                                error.message += pendingTasksInfo;\n                            }\n                            catch (err) { }\n                        }\n                    }\n                    if (onException) {\n                        onException.call(this, error);\n                    }\n                };\n                _super.call(this, attrs);\n            }\n            ZoneQueueRunner.prototype.execute = function () {\n                let zone = Zone.current;\n                let isChildOfAmbientZone = false;\n                while (zone) {\n                    if (zone === ambientZone) {\n                        isChildOfAmbientZone = true;\n                        break;\n                    }\n                    zone = zone.parent;\n                }\n                if (!isChildOfAmbientZone)\n                    throw new Error('Unexpected Zone: ' + Zone.current.name);\n                // This is the zone which will be used for running individual tests.\n                // It will be a proxy zone, so that the tests function can retroactively install\n                // different zones.\n                // Example:\n                //   - In beforeEach() do childZone = Zone.current.fork(...);\n                //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n                //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n                //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n                //     fakeAsync behavior to the childZone.\n                this.testProxyZoneSpec = new ProxyZoneSpec();\n                this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n                if (!Zone.currentTask) {\n                    // if we are not running in a task then if someone would register a\n                    // element.addEventListener and then calling element.click() the\n                    // addEventListener callback would think that it is the top most task and would\n                    // drain the microtask queue on element.click() which would be incorrect.\n                    // For this reason we always force a task when running jasmine tests.\n                    Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n                }\n                else {\n                    _super.prototype.execute.call(this);\n                }\n            };\n            return ZoneQueueRunner;\n        })(QueueRunner);\n    });\n}\n\nfunction patchJest(Zone) {\n    Zone.__load_patch('jest', (context, Zone, api) => {\n        if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n            return;\n        }\n        // From jest 29 and jest-preset-angular v13, the module transform logic\n        // changed, and now jest-preset-angular use the use the tsconfig target\n        // other than the hardcoded one, https://github.com/thymikee/jest-preset-angular/issues/2010\n        // But jest-angular-preset doesn't introduce the @babel/plugin-transform-async-to-generator\n        // which is needed by angular since `async/await` still need to be transformed\n        // to promise for ES2017+ target.\n        // So for now, we disable to output the uncaught error console log for a temp solution,\n        // until jest-preset-angular find a proper solution.\n        Zone[api.symbol('ignoreConsoleErrorUncaughtError')] = true;\n        jest['__zone_patch__'] = true;\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n        if (!ProxyZoneSpec) {\n            throw new Error('Missing ProxyZoneSpec');\n        }\n        const rootZone = Zone.current;\n        const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n        const proxyZoneSpec = new ProxyZoneSpec();\n        const proxyZone = rootZone.fork(proxyZoneSpec);\n        function wrapDescribeFactoryInZone(originalJestFn) {\n            return function (...tableArgs) {\n                const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n                return function (...args) {\n                    args[1] = wrapDescribeInZone(args[1]);\n                    return originalDescribeFn.apply(this, args);\n                };\n            };\n        }\n        function wrapTestFactoryInZone(originalJestFn) {\n            return function (...tableArgs) {\n                return function (...args) {\n                    args[1] = wrapTestInZone(args[1]);\n                    return originalJestFn.apply(this, tableArgs).apply(this, args);\n                };\n            };\n        }\n        /**\n         * Gets a function wrapping the body of a jest `describe` block to execute in a\n         * synchronous-only zone.\n         */\n        function wrapDescribeInZone(describeBody) {\n            return function (...args) {\n                return syncZone.run(describeBody, this, args);\n            };\n        }\n        /**\n         * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n         * execute in a ProxyZone zone.\n         * This will run in the `proxyZone`.\n         */\n        function wrapTestInZone(testBody, isTestFunc = false) {\n            if (typeof testBody !== 'function') {\n                return testBody;\n            }\n            const wrappedFunc = function () {\n                if (Zone[api.symbol('useFakeTimersCalled')] === true &&\n                    testBody &&\n                    !testBody.isFakeAsync) {\n                    // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n                    const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n                    if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                        testBody = fakeAsyncModule.fakeAsync(testBody);\n                    }\n                }\n                proxyZoneSpec.isTestFunc = isTestFunc;\n                return proxyZone.run(testBody, null, arguments);\n            };\n            // Update the length of wrappedFunc to be the same as the length of the testBody\n            // So jest core can handle whether the test function has `done()` or not correctly\n            Object.defineProperty(wrappedFunc, 'length', {\n                configurable: true,\n                writable: true,\n                enumerable: false,\n            });\n            wrappedFunc.length = testBody.length;\n            return wrappedFunc;\n        }\n        ['describe', 'xdescribe', 'fdescribe'].forEach((methodName) => {\n            let originalJestFn = context[methodName];\n            if (context[Zone.__symbol__(methodName)]) {\n                return;\n            }\n            context[Zone.__symbol__(methodName)] = originalJestFn;\n            context[methodName] = function (...args) {\n                args[1] = wrapDescribeInZone(args[1]);\n                return originalJestFn.apply(this, args);\n            };\n            context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n        });\n        context.describe.only = context.fdescribe;\n        context.describe.skip = context.xdescribe;\n        ['it', 'xit', 'fit', 'test', 'xtest'].forEach((methodName) => {\n            let originalJestFn = context[methodName];\n            if (context[Zone.__symbol__(methodName)]) {\n                return;\n            }\n            context[Zone.__symbol__(methodName)] = originalJestFn;\n            context[methodName] = function (...args) {\n                args[1] = wrapTestInZone(args[1], true);\n                return originalJestFn.apply(this, args);\n            };\n            context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n            context[methodName].todo = originalJestFn.todo;\n        });\n        context.it.only = context.fit;\n        context.it.skip = context.xit;\n        context.test.only = context.fit;\n        context.test.skip = context.xit;\n        ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach((methodName) => {\n            let originalJestFn = context[methodName];\n            if (context[Zone.__symbol__(methodName)]) {\n                return;\n            }\n            context[Zone.__symbol__(methodName)] = originalJestFn;\n            context[methodName] = function (...args) {\n                args[0] = wrapTestInZone(args[0]);\n                return originalJestFn.apply(this, args);\n            };\n        });\n        Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n            // check whether currently the test is inside fakeAsync()\n            function isPatchingFakeTimer() {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                return !!fakeAsyncZoneSpec;\n            }\n            // check whether the current function is inside `test/it` or other methods\n            // such as `describe/beforeEach`\n            function isInTestFunc() {\n                const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n                return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n            }\n            if (Timer[api.symbol('fakeTimers')]) {\n                return;\n            }\n            Timer[api.symbol('fakeTimers')] = true;\n            // patch jest fakeTimer internal method to make sure no console.warn print out\n            api.patchMethod(Timer, '_checkFakeTimers', (delegate) => {\n                return function (self, args) {\n                    if (isPatchingFakeTimer()) {\n                        return true;\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n            api.patchMethod(Timer, 'useFakeTimers', (delegate) => {\n                return function (self, args) {\n                    Zone[api.symbol('useFakeTimersCalled')] = true;\n                    if (isModern || isInTestFunc()) {\n                        return delegate.apply(self, args);\n                    }\n                    return self;\n                };\n            });\n            // patch useRealTimers(), unset useFakeTimers flag\n            api.patchMethod(Timer, 'useRealTimers', (delegate) => {\n                return function (self, args) {\n                    Zone[api.symbol('useFakeTimersCalled')] = false;\n                    if (isModern || isInTestFunc()) {\n                        return delegate.apply(self, args);\n                    }\n                    return self;\n                };\n            });\n            // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n            api.patchMethod(Timer, 'setSystemTime', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                        fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n            api.patchMethod(Timer, 'getRealSystemTime', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                        return fakeAsyncZoneSpec.getRealSystemTime();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch runAllTicks(), run all microTasks inside fakeAsync\n            api.patchMethod(Timer, 'runAllTicks', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.flushMicrotasks();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch runAllTimers(), run all macroTasks inside fakeAsync\n            api.patchMethod(Timer, 'runAllTimers', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.flush(100, true);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n            api.patchMethod(Timer, 'advanceTimersByTime', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.tick(args[0]);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n            api.patchMethod(Timer, 'runOnlyPendingTimers', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.flushOnlyPendingTimers();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n            api.patchMethod(Timer, 'advanceTimersToNextTimer', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.tickToNext(args[0]);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n            api.patchMethod(Timer, 'clearAllTimers', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.removeAllTimers();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n            api.patchMethod(Timer, 'getTimerCount', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        return fakeAsyncZoneSpec.getTimerCount();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n        };\n    });\n}\n\nfunction patchMocha(Zone) {\n    Zone.__load_patch('mocha', (global, Zone) => {\n        const Mocha = global.Mocha;\n        if (typeof Mocha === 'undefined') {\n            // return if Mocha is not available, because now zone-testing\n            // will load mocha patch with jasmine/jest patch\n            return;\n        }\n        if (typeof Zone === 'undefined') {\n            throw new Error('Missing Zone.js');\n        }\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n        if (!ProxyZoneSpec) {\n            throw new Error('Missing ProxyZoneSpec');\n        }\n        if (Mocha['__zone_patch__']) {\n            throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n        }\n        Mocha['__zone_patch__'] = true;\n        const rootZone = Zone.current;\n        const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n        let testZone = null;\n        const suiteZone = rootZone.fork(new ProxyZoneSpec());\n        const mochaOriginal = {\n            after: global.after,\n            afterEach: global.afterEach,\n            before: global.before,\n            beforeEach: global.beforeEach,\n            describe: global.describe,\n            it: global.it,\n        };\n        function modifyArguments(args, syncTest, asyncTest) {\n            for (let i = 0; i < args.length; i++) {\n                let arg = args[i];\n                if (typeof arg === 'function') {\n                    // The `done` callback is only passed through if the function expects at\n                    // least one argument.\n                    // Note we have to make a function with correct number of arguments,\n                    // otherwise mocha will\n                    // think that all functions are sync or async.\n                    args[i] = arg.length === 0 ? syncTest(arg) : asyncTest(arg);\n                    // Mocha uses toString to view the test body in the result list, make sure we return the\n                    // correct function body\n                    args[i].toString = function () {\n                        return arg.toString();\n                    };\n                }\n            }\n            return args;\n        }\n        function wrapDescribeInZone(args) {\n            const syncTest = function (fn) {\n                return function () {\n                    return syncZone.run(fn, this, arguments);\n                };\n            };\n            return modifyArguments(args, syncTest);\n        }\n        function wrapTestInZone(args) {\n            const asyncTest = function (fn) {\n                return function (done) {\n                    return testZone.run(fn, this, [done]);\n                };\n            };\n            const syncTest = function (fn) {\n                return function () {\n                    return testZone.run(fn, this);\n                };\n            };\n            return modifyArguments(args, syncTest, asyncTest);\n        }\n        function wrapSuiteInZone(args) {\n            const asyncTest = function (fn) {\n                return function (done) {\n                    return suiteZone.run(fn, this, [done]);\n                };\n            };\n            const syncTest = function (fn) {\n                return function () {\n                    return suiteZone.run(fn, this);\n                };\n            };\n            return modifyArguments(args, syncTest, asyncTest);\n        }\n        global.describe = global.suite = function () {\n            return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n        };\n        global.xdescribe =\n            global.suite.skip =\n                global.describe.skip =\n                    function () {\n                        return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n                    };\n        global.describe.only = global.suite.only = function () {\n            return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n        };\n        global.it =\n            global.specify =\n                global.test =\n                    function () {\n                        return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n                    };\n        global.xit =\n            global.xspecify =\n                global.it.skip =\n                    function () {\n                        return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n                    };\n        global.it.only = global.test.only = function () {\n            return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n        };\n        global.after = global.suiteTeardown = function () {\n            return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n        };\n        global.afterEach = global.teardown = function () {\n            return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n        };\n        global.before = global.suiteSetup = function () {\n            return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n        };\n        global.beforeEach = global.setup = function () {\n            return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n        };\n        ((originalRunTest, originalRun) => {\n            Mocha.Runner.prototype.runTest = function (fn) {\n                Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n                    originalRunTest.call(this, fn);\n                });\n            };\n            Mocha.Runner.prototype.run = function (fn) {\n                this.on('test', (e) => {\n                    testZone = rootZone.fork(new ProxyZoneSpec());\n                });\n                this.on('fail', (test, err) => {\n                    const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n                    if (proxyZoneSpec && err) {\n                        try {\n                            // try catch here in case err.message is not writable\n                            err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n                        }\n                        catch (error) { }\n                    }\n                });\n                return originalRun.call(this, fn);\n            };\n        })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n    });\n}\n\nconst global$2 = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global$2['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\n\nconst __global = (typeof window !== 'undefined' && window) || (typeof self !== 'undefined' && self) || global;\nclass AsyncTestZoneSpec {\n    // Needs to be a getter and not a plain property in order run this just-in-time. Otherwise\n    // `__symbol__` would be evaluated during top-level execution prior to the Zone prefix being\n    // changed for tests.\n    static get symbolParentUnresolved() {\n        return __symbol__('parentUnresolved');\n    }\n    constructor(finishCallback, failCallback, namePrefix) {\n        this.finishCallback = finishCallback;\n        this.failCallback = failCallback;\n        this._pendingMicroTasks = false;\n        this._pendingMacroTasks = false;\n        this._alreadyErrored = false;\n        this._isSync = false;\n        this._existingFinishTimer = null;\n        this.entryFunction = null;\n        this.runZone = Zone.current;\n        this.unresolvedChainedPromiseCount = 0;\n        this.supportWaitUnresolvedChainedPromise = false;\n        this.name = 'asyncTestZone for ' + namePrefix;\n        this.properties = { 'AsyncTestZoneSpec': this };\n        this.supportWaitUnresolvedChainedPromise =\n            __global[__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n    }\n    isUnresolvedChainedPromisePending() {\n        return this.unresolvedChainedPromiseCount > 0;\n    }\n    _finishCallbackIfDone() {\n        // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n        // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n        // microtasks in the proxy zone that now complete as part of this async zone run.\n        // Consider the following scenario:\n        //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n        //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n        //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n        //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n        //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n        //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n        // If the finish timeout from below is already scheduled, terminate the existing scheduled\n        // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n        // want to schedule a new finish callback in case the task state changes again.\n        if (this._existingFinishTimer !== null) {\n            clearTimeout(this._existingFinishTimer);\n            this._existingFinishTimer = null;\n        }\n        if (!(this._pendingMicroTasks ||\n            this._pendingMacroTasks ||\n            (this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending()))) {\n            // We wait until the next tick because we would like to catch unhandled promises which could\n            // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n            this.runZone.run(() => {\n                this._existingFinishTimer = setTimeout(() => {\n                    if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n                        this.finishCallback();\n                    }\n                }, 0);\n            });\n        }\n    }\n    patchPromiseForTest() {\n        if (!this.supportWaitUnresolvedChainedPromise) {\n            return;\n        }\n        const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n        if (patchPromiseForTest) {\n            patchPromiseForTest();\n        }\n    }\n    unPatchPromiseForTest() {\n        if (!this.supportWaitUnresolvedChainedPromise) {\n            return;\n        }\n        const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n        if (unPatchPromiseForTest) {\n            unPatchPromiseForTest();\n        }\n    }\n    onScheduleTask(delegate, current, target, task) {\n        if (task.type !== 'eventTask') {\n            this._isSync = false;\n        }\n        if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n            // check whether the promise is a chained promise\n            if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n                // chained promise is being scheduled\n                this.unresolvedChainedPromiseCount--;\n            }\n        }\n        return delegate.scheduleTask(target, task);\n    }\n    onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n        if (task.type !== 'eventTask') {\n            this._isSync = false;\n        }\n        return delegate.invokeTask(target, task, applyThis, applyArgs);\n    }\n    onCancelTask(delegate, current, target, task) {\n        if (task.type !== 'eventTask') {\n            this._isSync = false;\n        }\n        return delegate.cancelTask(target, task);\n    }\n    // Note - we need to use onInvoke at the moment to call finish when a test is\n    // fully synchronous. TODO(juliemr): remove this when the logic for\n    // onHasTask changes and it calls whenever the task queues are dirty.\n    // updated by(JiaLiPassion), only call finish callback when no task\n    // was scheduled/invoked/canceled.\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n        if (!this.entryFunction) {\n            this.entryFunction = delegate;\n        }\n        try {\n            this._isSync = true;\n            return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n        }\n        finally {\n            // We need to check the delegate is the same as entryFunction or not.\n            // Consider the following case.\n            //\n            // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n            //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n            //   });\n            // });\n            //\n            // We only want to check whether there are async tasks scheduled\n            // for the entry function.\n            if (this._isSync && this.entryFunction === delegate) {\n                this._finishCallbackIfDone();\n            }\n        }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        // Let the parent try to handle the error.\n        const result = parentZoneDelegate.handleError(targetZone, error);\n        if (result) {\n            this.failCallback(error);\n            this._alreadyErrored = true;\n        }\n        return false;\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n        delegate.hasTask(target, hasTaskState);\n        // We should only trigger finishCallback when the target zone is the AsyncTestZone\n        // Consider the following cases.\n        //\n        // const childZone = asyncTestZone.fork({\n        //   name: 'child',\n        //   onHasTask: ...\n        // });\n        //\n        // So we have nested zones declared the onHasTask hook, in this case,\n        // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n        // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n        // when the current zone is the same as the target zone.\n        if (current !== target) {\n            return;\n        }\n        if (hasTaskState.change == 'microTask') {\n            this._pendingMicroTasks = hasTaskState.microTask;\n            this._finishCallbackIfDone();\n        }\n        else if (hasTaskState.change == 'macroTask') {\n            this._pendingMacroTasks = hasTaskState.macroTask;\n            this._finishCallbackIfDone();\n        }\n    }\n}\nfunction patchAsyncTest(Zone) {\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n    Zone.__load_patch('asynctest', (global, Zone, api) => {\n        /**\n         * Wraps a test function in an asynchronous test zone. The test will automatically\n         * complete when all asynchronous calls within this zone are done.\n         */\n        Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n            // If we're running using the Jasmine test framework, adapt to call the 'done'\n            // function when asynchronous activity is finished.\n            if (global.jasmine) {\n                // Not using an arrow function to preserve context passed from call site\n                return function (done) {\n                    if (!done) {\n                        // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n                        // fake it here and assume sync.\n                        done = function () { };\n                        done.fail = function (e) {\n                            throw e;\n                        };\n                    }\n                    runInTestZone(fn, this, done, (err) => {\n                        if (typeof err === 'string') {\n                            return done.fail(new Error(err));\n                        }\n                        else {\n                            done.fail(err);\n                        }\n                    });\n                };\n            }\n            // Otherwise, return a promise which will resolve when asynchronous activity\n            // is finished. This will be correctly consumed by the Mocha framework with\n            // it('...', async(myFn)); or can be used in a custom framework.\n            // Not using an arrow function to preserve context passed from call site\n            return function () {\n                return new Promise((finishCallback, failCallback) => {\n                    runInTestZone(fn, this, finishCallback, failCallback);\n                });\n            };\n        };\n        function runInTestZone(fn, context, finishCallback, failCallback) {\n            const currentZone = Zone.current;\n            const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n            if (AsyncTestZoneSpec === undefined) {\n                throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n                    'Please make sure that your environment includes zone.js/plugins/async-test');\n            }\n            const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n            if (!ProxyZoneSpec) {\n                throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                    'Please make sure that your environment includes zone.js/plugins/proxy');\n            }\n            const proxyZoneSpec = ProxyZoneSpec.get();\n            ProxyZoneSpec.assertPresent();\n            // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n            // If we do it in ProxyZone then we will get to infinite recursion.\n            const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n            const previousDelegate = proxyZoneSpec.getDelegate();\n            proxyZone.parent.run(() => {\n                const testZoneSpec = new AsyncTestZoneSpec(() => {\n                    // Need to restore the original zone.\n                    if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                        // Only reset the zone spec if it's\n                        // still this one. Otherwise, assume\n                        // it's OK.\n                        proxyZoneSpec.setDelegate(previousDelegate);\n                    }\n                    testZoneSpec.unPatchPromiseForTest();\n                    currentZone.run(() => {\n                        finishCallback();\n                    });\n                }, (error) => {\n                    // Need to restore the original zone.\n                    if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                        // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n                        proxyZoneSpec.setDelegate(previousDelegate);\n                    }\n                    testZoneSpec.unPatchPromiseForTest();\n                    currentZone.run(() => {\n                        failCallback(error);\n                    });\n                }, 'test');\n                proxyZoneSpec.setDelegate(testZoneSpec);\n                testZoneSpec.patchPromiseForTest();\n            });\n            return Zone.current.runGuarded(fn, context);\n        }\n    });\n}\n\nconst global$1 = (typeof window === 'object' && window) || (typeof self === 'object' && self) || globalThis.global;\nconst OriginalDate = global$1.Date;\n// Since when we compile this file to `es2015`, and if we define\n// this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n// there will be an error which is `Cannot assign to read only property 'prototype'`\n// so we need to use function implementation here.\nfunction FakeDate() {\n    if (arguments.length === 0) {\n        const d = new OriginalDate();\n        d.setTime(FakeDate.now());\n        return d;\n    }\n    else {\n        const args = Array.prototype.slice.call(arguments);\n        return new OriginalDate(...args);\n    }\n}\nFakeDate.now = function () {\n    const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n    if (fakeAsyncTestZoneSpec) {\n        return fakeAsyncTestZoneSpec.getFakeSystemTime();\n    }\n    return OriginalDate.now.apply(this, arguments);\n};\nFakeDate.UTC = OriginalDate.UTC;\nFakeDate.parse = OriginalDate.parse;\n// keep a reference for zone patched timer function\nlet patchedTimers;\nconst timeoutCallback = function () { };\nclass Scheduler {\n    // Next scheduler id.\n    static { this.nextNodeJSId = 1; }\n    static { this.nextId = -1; }\n    constructor() {\n        // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n        this._schedulerQueue = [];\n        // Current simulated time in millis.\n        this._currentTickTime = 0;\n        // Current fake system base time in millis.\n        this._currentFakeBaseSystemTime = OriginalDate.now();\n        // track requeuePeriodicTimer\n        this._currentTickRequeuePeriodicEntries = [];\n    }\n    static getNextId() {\n        const id = patchedTimers.nativeSetTimeout.call(global$1, timeoutCallback, 0);\n        patchedTimers.nativeClearTimeout.call(global$1, id);\n        if (typeof id === 'number') {\n            return id;\n        }\n        // in NodeJS, we just use a number for fakeAsync, since it will not\n        // conflict with native TimeoutId\n        return Scheduler.nextNodeJSId++;\n    }\n    getCurrentTickTime() {\n        return this._currentTickTime;\n    }\n    getFakeSystemTime() {\n        return this._currentFakeBaseSystemTime + this._currentTickTime;\n    }\n    setFakeBaseSystemTime(fakeBaseSystemTime) {\n        this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n    }\n    getRealSystemTime() {\n        return OriginalDate.now();\n    }\n    scheduleFunction(cb, delay, options) {\n        options = {\n            ...{\n                args: [],\n                isPeriodic: false,\n                isRequestAnimationFrame: false,\n                id: -1,\n                isRequeuePeriodic: false,\n            },\n            ...options,\n        };\n        let currentId = options.id < 0 ? Scheduler.nextId : options.id;\n        Scheduler.nextId = Scheduler.getNextId();\n        let endTime = this._currentTickTime + delay;\n        // Insert so that scheduler queue remains sorted by end time.\n        let newEntry = {\n            endTime: endTime,\n            id: currentId,\n            func: cb,\n            args: options.args,\n            delay: delay,\n            isPeriodic: options.isPeriodic,\n            isRequestAnimationFrame: options.isRequestAnimationFrame,\n        };\n        if (options.isRequeuePeriodic) {\n            this._currentTickRequeuePeriodicEntries.push(newEntry);\n        }\n        let i = 0;\n        for (; i < this._schedulerQueue.length; i++) {\n            let currentEntry = this._schedulerQueue[i];\n            if (newEntry.endTime < currentEntry.endTime) {\n                break;\n            }\n        }\n        this._schedulerQueue.splice(i, 0, newEntry);\n        return currentId;\n    }\n    removeScheduledFunctionWithId(id) {\n        for (let i = 0; i < this._schedulerQueue.length; i++) {\n            if (this._schedulerQueue[i].id == id) {\n                this._schedulerQueue.splice(i, 1);\n                break;\n            }\n        }\n    }\n    removeAll() {\n        this._schedulerQueue = [];\n    }\n    getTimerCount() {\n        return this._schedulerQueue.length;\n    }\n    tickToNext(step = 1, doTick, tickOptions) {\n        if (this._schedulerQueue.length < step) {\n            return;\n        }\n        // Find the last task currently queued in the scheduler queue and tick\n        // till that time.\n        const startTime = this._currentTickTime;\n        const targetTask = this._schedulerQueue[step - 1];\n        this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n    }\n    tick(millis = 0, doTick, tickOptions) {\n        let finalTime = this._currentTickTime + millis;\n        let lastCurrentTime = 0;\n        tickOptions = Object.assign({ processNewMacroTasksSynchronously: true }, tickOptions);\n        // we need to copy the schedulerQueue so nested timeout\n        // will not be wrongly called in the current tick\n        // https://github.com/angular/angular/issues/33799\n        const schedulerQueue = tickOptions.processNewMacroTasksSynchronously\n            ? this._schedulerQueue\n            : this._schedulerQueue.slice();\n        if (schedulerQueue.length === 0 && doTick) {\n            doTick(millis);\n            return;\n        }\n        while (schedulerQueue.length > 0) {\n            // clear requeueEntries before each loop\n            this._currentTickRequeuePeriodicEntries = [];\n            let current = schedulerQueue[0];\n            if (finalTime < current.endTime) {\n                // Done processing the queue since it's sorted by endTime.\n                break;\n            }\n            else {\n                // Time to run scheduled function. Remove it from the head of queue.\n                let current = schedulerQueue.shift();\n                if (!tickOptions.processNewMacroTasksSynchronously) {\n                    const idx = this._schedulerQueue.indexOf(current);\n                    if (idx >= 0) {\n                        this._schedulerQueue.splice(idx, 1);\n                    }\n                }\n                lastCurrentTime = this._currentTickTime;\n                this._currentTickTime = current.endTime;\n                if (doTick) {\n                    doTick(this._currentTickTime - lastCurrentTime);\n                }\n                let retval = current.func.apply(global$1, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n                if (!retval) {\n                    // Uncaught exception in the current scheduled function. Stop processing the queue.\n                    break;\n                }\n                // check is there any requeue periodic entry is added in\n                // current loop, if there is, we need to add to current loop\n                if (!tickOptions.processNewMacroTasksSynchronously) {\n                    this._currentTickRequeuePeriodicEntries.forEach((newEntry) => {\n                        let i = 0;\n                        for (; i < schedulerQueue.length; i++) {\n                            const currentEntry = schedulerQueue[i];\n                            if (newEntry.endTime < currentEntry.endTime) {\n                                break;\n                            }\n                        }\n                        schedulerQueue.splice(i, 0, newEntry);\n                    });\n                }\n            }\n        }\n        lastCurrentTime = this._currentTickTime;\n        this._currentTickTime = finalTime;\n        if (doTick) {\n            doTick(this._currentTickTime - lastCurrentTime);\n        }\n    }\n    flushOnlyPendingTimers(doTick) {\n        if (this._schedulerQueue.length === 0) {\n            return 0;\n        }\n        // Find the last task currently queued in the scheduler queue and tick\n        // till that time.\n        const startTime = this._currentTickTime;\n        const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n        this.tick(lastTask.endTime - startTime, doTick, { processNewMacroTasksSynchronously: false });\n        return this._currentTickTime - startTime;\n    }\n    flush(limit = 20, flushPeriodic = false, doTick) {\n        if (flushPeriodic) {\n            return this.flushPeriodic(doTick);\n        }\n        else {\n            return this.flushNonPeriodic(limit, doTick);\n        }\n    }\n    flushPeriodic(doTick) {\n        if (this._schedulerQueue.length === 0) {\n            return 0;\n        }\n        // Find the last task currently queued in the scheduler queue and tick\n        // till that time.\n        const startTime = this._currentTickTime;\n        const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n        this.tick(lastTask.endTime - startTime, doTick);\n        return this._currentTickTime - startTime;\n    }\n    flushNonPeriodic(limit, doTick) {\n        const startTime = this._currentTickTime;\n        let lastCurrentTime = 0;\n        let count = 0;\n        while (this._schedulerQueue.length > 0) {\n            count++;\n            if (count > limit) {\n                throw new Error('flush failed after reaching the limit of ' +\n                    limit +\n                    ' tasks. Does your code use a polling timeout?');\n            }\n            // flush only non-periodic timers.\n            // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n            if (this._schedulerQueue.filter((task) => !task.isPeriodic && !task.isRequestAnimationFrame)\n                .length === 0) {\n                break;\n            }\n            const current = this._schedulerQueue.shift();\n            lastCurrentTime = this._currentTickTime;\n            this._currentTickTime = current.endTime;\n            if (doTick) {\n                // Update any secondary schedulers like Jasmine mock Date.\n                doTick(this._currentTickTime - lastCurrentTime);\n            }\n            const retval = current.func.apply(global$1, current.args);\n            if (!retval) {\n                // Uncaught exception in the current scheduled function. Stop processing the queue.\n                break;\n            }\n        }\n        return this._currentTickTime - startTime;\n    }\n}\nclass FakeAsyncTestZoneSpec {\n    static assertInZone() {\n        if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n            throw new Error('The code should be running in the fakeAsync zone to call this function');\n        }\n    }\n    constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n        this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n        this.macroTaskOptions = macroTaskOptions;\n        this._scheduler = new Scheduler();\n        this._microtasks = [];\n        this._lastError = null;\n        this._uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n        this.pendingPeriodicTimers = [];\n        this.pendingTimers = [];\n        this.patchDateLocked = false;\n        this.properties = { 'FakeAsyncTestZoneSpec': this };\n        this.name = 'fakeAsyncTestZone for ' + namePrefix;\n        // in case user can't access the construction of FakeAsyncTestSpec\n        // user can also define macroTaskOptions by define a global variable.\n        if (!this.macroTaskOptions) {\n            this.macroTaskOptions = global$1[Zone.__symbol__('FakeAsyncTestMacroTask')];\n        }\n    }\n    _fnAndFlush(fn, completers) {\n        return (...args) => {\n            fn.apply(global$1, args);\n            if (this._lastError === null) {\n                // Success\n                if (completers.onSuccess != null) {\n                    completers.onSuccess.apply(global$1);\n                }\n                // Flush microtasks only on success.\n                this.flushMicrotasks();\n            }\n            else {\n                // Failure\n                if (completers.onError != null) {\n                    completers.onError.apply(global$1);\n                }\n            }\n            // Return true if there were no errors, false otherwise.\n            return this._lastError === null;\n        };\n    }\n    static _removeTimer(timers, id) {\n        let index = timers.indexOf(id);\n        if (index > -1) {\n            timers.splice(index, 1);\n        }\n    }\n    _dequeueTimer(id) {\n        return () => {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n        };\n    }\n    _requeuePeriodicTimer(fn, interval, args, id) {\n        return () => {\n            // Requeue the timer callback if it's not been canceled.\n            if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n                this._scheduler.scheduleFunction(fn, interval, {\n                    args,\n                    isPeriodic: true,\n                    id,\n                    isRequeuePeriodic: true,\n                });\n            }\n        };\n    }\n    _dequeuePeriodicTimer(id) {\n        return () => {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n        };\n    }\n    _setTimeout(fn, delay, args, isTimer = true) {\n        let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n        // Queue the callback and dequeue the timer on success and error.\n        let cb = this._fnAndFlush(fn, { onSuccess: removeTimerFn, onError: removeTimerFn });\n        let id = this._scheduler.scheduleFunction(cb, delay, { args, isRequestAnimationFrame: !isTimer });\n        if (isTimer) {\n            this.pendingTimers.push(id);\n        }\n        return id;\n    }\n    _clearTimeout(id) {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n        this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _setInterval(fn, interval, args) {\n        let id = Scheduler.nextId;\n        let completers = { onSuccess: null, onError: this._dequeuePeriodicTimer(id) };\n        let cb = this._fnAndFlush(fn, completers);\n        // Use the callback created above to requeue on success.\n        completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n        // Queue the callback and dequeue the periodic timer only on error.\n        this._scheduler.scheduleFunction(cb, interval, { args, isPeriodic: true });\n        this.pendingPeriodicTimers.push(id);\n        return id;\n    }\n    _clearInterval(id) {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n        this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _resetLastErrorAndThrow() {\n        let error = this._lastError || this._uncaughtPromiseErrors[0];\n        this._uncaughtPromiseErrors.length = 0;\n        this._lastError = null;\n        throw error;\n    }\n    getCurrentTickTime() {\n        return this._scheduler.getCurrentTickTime();\n    }\n    getFakeSystemTime() {\n        return this._scheduler.getFakeSystemTime();\n    }\n    setFakeBaseSystemTime(realTime) {\n        this._scheduler.setFakeBaseSystemTime(realTime);\n    }\n    getRealSystemTime() {\n        return this._scheduler.getRealSystemTime();\n    }\n    static patchDate() {\n        if (!!global$1[Zone.__symbol__('disableDatePatching')]) {\n            // we don't want to patch global Date\n            // because in some case, global Date\n            // is already being patched, we need to provide\n            // an option to let user still use their\n            // own version of Date.\n            return;\n        }\n        if (global$1['Date'] === FakeDate) {\n            // already patched\n            return;\n        }\n        global$1['Date'] = FakeDate;\n        FakeDate.prototype = OriginalDate.prototype;\n        // try check and reset timers\n        // because jasmine.clock().install() may\n        // have replaced the global timer\n        FakeAsyncTestZoneSpec.checkTimerPatch();\n    }\n    static resetDate() {\n        if (global$1['Date'] === FakeDate) {\n            global$1['Date'] = OriginalDate;\n        }\n    }\n    static checkTimerPatch() {\n        if (!patchedTimers) {\n            throw new Error('Expected timers to have been patched.');\n        }\n        if (global$1.setTimeout !== patchedTimers.setTimeout) {\n            global$1.setTimeout = patchedTimers.setTimeout;\n            global$1.clearTimeout = patchedTimers.clearTimeout;\n        }\n        if (global$1.setInterval !== patchedTimers.setInterval) {\n            global$1.setInterval = patchedTimers.setInterval;\n            global$1.clearInterval = patchedTimers.clearInterval;\n        }\n    }\n    lockDatePatch() {\n        this.patchDateLocked = true;\n        FakeAsyncTestZoneSpec.patchDate();\n    }\n    unlockDatePatch() {\n        this.patchDateLocked = false;\n        FakeAsyncTestZoneSpec.resetDate();\n    }\n    tickToNext(steps = 1, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n        if (steps <= 0) {\n            return;\n        }\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        this._scheduler.tickToNext(steps, doTick, tickOptions);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n    }\n    tick(millis = 0, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        this._scheduler.tick(millis, doTick, tickOptions);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n    }\n    flushMicrotasks() {\n        FakeAsyncTestZoneSpec.assertInZone();\n        const flushErrors = () => {\n            if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n                // If there is an error stop processing the microtask queue and rethrow the error.\n                this._resetLastErrorAndThrow();\n            }\n        };\n        while (this._microtasks.length > 0) {\n            let microtask = this._microtasks.shift();\n            microtask.func.apply(microtask.target, microtask.args);\n        }\n        flushErrors();\n    }\n    flush(limit, flushPeriodic, doTick) {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n        return elapsed;\n    }\n    flushOnlyPendingTimers(doTick) {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n        return elapsed;\n    }\n    removeAllTimers() {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this._scheduler.removeAll();\n        this.pendingPeriodicTimers = [];\n        this.pendingTimers = [];\n    }\n    getTimerCount() {\n        return this._scheduler.getTimerCount() + this._microtasks.length;\n    }\n    onScheduleTask(delegate, current, target, task) {\n        switch (task.type) {\n            case 'microTask':\n                let args = task.data && task.data.args;\n                // should pass additional arguments to callback if have any\n                // currently we know process.nextTick will have such additional\n                // arguments\n                let additionalArgs;\n                if (args) {\n                    let callbackIndex = task.data.cbIdx;\n                    if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n                        additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n                    }\n                }\n                this._microtasks.push({\n                    func: task.invoke,\n                    args: additionalArgs,\n                    target: task.data && task.data.target,\n                });\n                break;\n            case 'macroTask':\n                switch (task.source) {\n                    case 'setTimeout':\n                        task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                        break;\n                    case 'setImmediate':\n                        task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n                        break;\n                    case 'setInterval':\n                        task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                        break;\n                    case 'XMLHttpRequest.send':\n                        throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' +\n                            task.data['url']);\n                    case 'requestAnimationFrame':\n                    case 'webkitRequestAnimationFrame':\n                    case 'mozRequestAnimationFrame':\n                        // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n                        // (60 frames per second)\n                        task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n                        break;\n                    default:\n                        // user can define which macroTask they want to support by passing\n                        // macroTaskOptions\n                        const macroTaskOption = this.findMacroTaskOption(task);\n                        if (macroTaskOption) {\n                            const args = task.data && task.data['args'];\n                            const delay = args && args.length > 1 ? args[1] : 0;\n                            let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n                            if (!!macroTaskOption.isPeriodic) {\n                                // periodic macroTask, use setInterval to simulate\n                                task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                                task.data.isPeriodic = true;\n                            }\n                            else {\n                                // not periodic, use setTimeout to simulate\n                                task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n                            }\n                            break;\n                        }\n                        throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n                }\n                break;\n            case 'eventTask':\n                task = delegate.scheduleTask(target, task);\n                break;\n        }\n        return task;\n    }\n    onCancelTask(delegate, current, target, task) {\n        switch (task.source) {\n            case 'setTimeout':\n            case 'requestAnimationFrame':\n            case 'webkitRequestAnimationFrame':\n            case 'mozRequestAnimationFrame':\n                return this._clearTimeout(task.data['handleId']);\n            case 'setInterval':\n                return this._clearInterval(task.data['handleId']);\n            default:\n                // user can define which macroTask they want to support by passing\n                // macroTaskOptions\n                const macroTaskOption = this.findMacroTaskOption(task);\n                if (macroTaskOption) {\n                    const handleId = task.data['handleId'];\n                    return macroTaskOption.isPeriodic\n                        ? this._clearInterval(handleId)\n                        : this._clearTimeout(handleId);\n                }\n                return delegate.cancelTask(target, task);\n        }\n    }\n    onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n        try {\n            FakeAsyncTestZoneSpec.patchDate();\n            return delegate.invoke(target, callback, applyThis, applyArgs, source);\n        }\n        finally {\n            if (!this.patchDateLocked) {\n                FakeAsyncTestZoneSpec.resetDate();\n            }\n        }\n    }\n    findMacroTaskOption(task) {\n        if (!this.macroTaskOptions) {\n            return null;\n        }\n        for (let i = 0; i < this.macroTaskOptions.length; i++) {\n            const macroTaskOption = this.macroTaskOptions[i];\n            if (macroTaskOption.source === task.source) {\n                return macroTaskOption;\n            }\n        }\n        return null;\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        this._lastError = error;\n        return false; // Don't propagate error to parent zone.\n    }\n}\nlet _fakeAsyncTestZoneSpec = null;\nfunction getProxyZoneSpec() {\n    return Zone && Zone['ProxyZoneSpec'];\n}\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nfunction resetFakeAsyncZone() {\n    if (_fakeAsyncTestZoneSpec) {\n        _fakeAsyncTestZoneSpec.unlockDatePatch();\n    }\n    _fakeAsyncTestZoneSpec = null;\n    // in node.js testing we may not have ProxyZoneSpec in which case there is nothing to reset.\n    getProxyZoneSpec() && getProxyZoneSpec().assertPresent().resetDelegate();\n}\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * When flush is `false`, if there are any pending timers at the end of the function,\n * an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @param options\n *     flush: when true, will drain the macrotask queue after the test function completes.\n * @returns The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nfunction fakeAsync(fn, options = {}) {\n    const { flush = false } = options;\n    // Not using an arrow function to preserve context passed from call site\n    const fakeAsyncFn = function (...args) {\n        const ProxyZoneSpec = getProxyZoneSpec();\n        if (!ProxyZoneSpec) {\n            throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js/plugins/proxy');\n        }\n        const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n        if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n            throw new Error('fakeAsync() calls can not be nested');\n        }\n        try {\n            // in case jasmine.clock init a fakeAsyncTestZoneSpec\n            if (!_fakeAsyncTestZoneSpec) {\n                const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n                if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n                    throw new Error('fakeAsync() calls can not be nested');\n                }\n                _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n            }\n            let res;\n            const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n            proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n            _fakeAsyncTestZoneSpec.lockDatePatch();\n            try {\n                res = fn.apply(this, args);\n                if (flush) {\n                    _fakeAsyncTestZoneSpec.flush(20, true);\n                }\n                else {\n                    flushMicrotasks();\n                }\n            }\n            finally {\n                proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n            }\n            if (!flush) {\n                if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n                        `periodic timer(s) still in the queue.`);\n                }\n                if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n                }\n            }\n            return res;\n        }\n        finally {\n            resetFakeAsyncZone();\n        }\n    };\n    fakeAsyncFn.isFakeAsync = true;\n    return fakeAsyncFn;\n}\nfunction _getFakeAsyncZoneSpec() {\n    if (_fakeAsyncTestZoneSpec == null) {\n        _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (_fakeAsyncTestZoneSpec == null) {\n            throw new Error('The code should be running in the fakeAsync zone to call this function');\n        }\n    }\n    return _fakeAsyncTestZoneSpec;\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer\n * callback has been executed.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nfunction tick(millis = 0, ignoreNestedTimeout = false) {\n    _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns The simulated time elapsed, in millis.\n *\n * @experimental\n */\nfunction flush(maxTurns) {\n    return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nfunction discardPeriodicTasks() {\n    const zoneSpec = _getFakeAsyncZoneSpec();\n    zoneSpec.pendingPeriodicTimers;\n    zoneSpec.pendingPeriodicTimers.length = 0;\n}\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nfunction flushMicrotasks() {\n    _getFakeAsyncZoneSpec().flushMicrotasks();\n}\nfunction patchFakeAsyncTest(Zone) {\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n    Zone.__load_patch('fakeasync', (global, Zone, api) => {\n        Zone[api.symbol('fakeAsyncTest')] = {\n            resetFakeAsyncZone,\n            flushMicrotasks,\n            discardPeriodicTasks,\n            tick,\n            flush,\n            fakeAsync,\n        };\n    }, true);\n    patchedTimers = {\n        setTimeout: global$1.setTimeout,\n        setInterval: global$1.setInterval,\n        clearTimeout: global$1.clearTimeout,\n        clearInterval: global$1.clearInterval,\n        nativeSetTimeout: global$1[Zone.__symbol__('setTimeout')],\n        nativeClearTimeout: global$1[Zone.__symbol__('clearTimeout')],\n    };\n    Scheduler.nextId = Scheduler.getNextId();\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction patchLongStackTrace(Zone) {\n    const NEWLINE = '\\n';\n    const IGNORE_FRAMES = {};\n    const creationTrace = '__creationTrace__';\n    const ERROR_TAG = 'STACKTRACE TRACKING';\n    const SEP_TAG = '__SEP_TAG__';\n    let sepTemplate = SEP_TAG + '@[native]';\n    class LongStackTrace {\n        constructor() {\n            this.error = getStacktrace();\n            this.timestamp = new Date();\n        }\n    }\n    function getStacktraceWithUncaughtError() {\n        return new Error(ERROR_TAG);\n    }\n    function getStacktraceWithCaughtError() {\n        try {\n            throw getStacktraceWithUncaughtError();\n        }\n        catch (err) {\n            return err;\n        }\n    }\n    // Some implementations of exception handling don't create a stack trace if the exception\n    // isn't thrown, however it's faster not to actually throw the exception.\n    const error = getStacktraceWithUncaughtError();\n    const caughtError = getStacktraceWithCaughtError();\n    const getStacktrace = error.stack\n        ? getStacktraceWithUncaughtError\n        : caughtError.stack\n            ? getStacktraceWithCaughtError\n            : getStacktraceWithUncaughtError;\n    function getFrames(error) {\n        return error.stack ? error.stack.split(NEWLINE) : [];\n    }\n    function addErrorStack(lines, error) {\n        let trace = getFrames(error);\n        for (let i = 0; i < trace.length; i++) {\n            const frame = trace[i];\n            // Filter out the Frames which are part of stack capturing.\n            if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n                lines.push(trace[i]);\n            }\n        }\n    }\n    function renderLongStackTrace(frames, stack) {\n        const longTrace = [stack ? stack.trim() : ''];\n        if (frames) {\n            let timestamp = new Date().getTime();\n            for (let i = 0; i < frames.length; i++) {\n                const traceFrames = frames[i];\n                const lastTime = traceFrames.timestamp;\n                let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n                separator = separator.replace(/[^\\w\\d]/g, '_');\n                longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n                addErrorStack(longTrace, traceFrames.error);\n                timestamp = lastTime.getTime();\n            }\n        }\n        return longTrace.join(NEWLINE);\n    }\n    // if Error.stackTraceLimit is 0, means stack trace\n    // is disabled, so we don't need to generate long stack trace\n    // this will improve performance in some test(some test will\n    // set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\n    function stackTracesEnabled() {\n        // Cast through any since this property only exists on Error in the nodejs\n        // typings.\n        return Error.stackTraceLimit > 0;\n    }\n    Zone['longStackTraceZoneSpec'] = {\n        name: 'long-stack-trace',\n        longStackTraceLimit: 10, // Max number of task to keep the stack trace for.\n        // add a getLongStackTrace method in spec to\n        // handle handled reject promise error.\n        getLongStackTrace: function (error) {\n            if (!error) {\n                return undefined;\n            }\n            const trace = error[Zone.__symbol__('currentTaskTrace')];\n            if (!trace) {\n                return error.stack;\n            }\n            return renderLongStackTrace(trace, error.stack);\n        },\n        onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n            if (stackTracesEnabled()) {\n                const currentTask = Zone.currentTask;\n                let trace = (currentTask && currentTask.data && currentTask.data[creationTrace]) || [];\n                trace = [new LongStackTrace()].concat(trace);\n                if (trace.length > this.longStackTraceLimit) {\n                    trace.length = this.longStackTraceLimit;\n                }\n                if (!task.data)\n                    task.data = {};\n                if (task.type === 'eventTask') {\n                    // Fix issue https://github.com/angular/zone.js/issues/1195,\n                    // For event task of browser, by default, all task will share a\n                    // singleton instance of data object, we should create a new one here\n                    // The cast to `any` is required to workaround a closure bug which wrongly applies\n                    // URL sanitization rules to .data access.\n                    task.data = { ...task.data };\n                }\n                task.data[creationTrace] = trace;\n            }\n            return parentZoneDelegate.scheduleTask(targetZone, task);\n        },\n        onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n            if (stackTracesEnabled()) {\n                const parentTask = Zone.currentTask || error.task;\n                if (error instanceof Error && parentTask) {\n                    const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n                    try {\n                        error.stack = error.longStack = longStack;\n                    }\n                    catch (err) { }\n                }\n            }\n            return parentZoneDelegate.handleError(targetZone, error);\n        },\n    };\n    function captureStackTraces(stackTraces, count) {\n        if (count > 0) {\n            stackTraces.push(getFrames(new LongStackTrace().error));\n            captureStackTraces(stackTraces, count - 1);\n        }\n    }\n    function computeIgnoreFrames() {\n        if (!stackTracesEnabled()) {\n            return;\n        }\n        const frames = [];\n        captureStackTraces(frames, 2);\n        const frames1 = frames[0];\n        const frames2 = frames[1];\n        for (let i = 0; i < frames1.length; i++) {\n            const frame1 = frames1[i];\n            if (frame1.indexOf(ERROR_TAG) == -1) {\n                let match = frame1.match(/^\\s*at\\s+/);\n                if (match) {\n                    sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n                    break;\n                }\n            }\n        }\n        for (let i = 0; i < frames1.length; i++) {\n            const frame1 = frames1[i];\n            const frame2 = frames2[i];\n            if (frame1 === frame2) {\n                IGNORE_FRAMES[frame1] = true;\n            }\n            else {\n                break;\n            }\n        }\n    }\n    computeIgnoreFrames();\n}\n\nclass ProxyZoneSpec {\n    static get() {\n        return Zone.current.get('ProxyZoneSpec');\n    }\n    static isLoaded() {\n        return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n    }\n    static assertPresent() {\n        if (!ProxyZoneSpec.isLoaded()) {\n            throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n        }\n        return ProxyZoneSpec.get();\n    }\n    constructor(defaultSpecDelegate = null) {\n        this.defaultSpecDelegate = defaultSpecDelegate;\n        this.name = 'ProxyZone';\n        this._delegateSpec = null;\n        this.properties = { 'ProxyZoneSpec': this };\n        this.propertyKeys = null;\n        this.lastTaskState = null;\n        this.isNeedToTriggerHasTask = false;\n        this.tasks = [];\n        this.setDelegate(defaultSpecDelegate);\n    }\n    setDelegate(delegateSpec) {\n        const isNewDelegate = this._delegateSpec !== delegateSpec;\n        this._delegateSpec = delegateSpec;\n        this.propertyKeys && this.propertyKeys.forEach((key) => delete this.properties[key]);\n        this.propertyKeys = null;\n        if (delegateSpec && delegateSpec.properties) {\n            this.propertyKeys = Object.keys(delegateSpec.properties);\n            this.propertyKeys.forEach((k) => (this.properties[k] = delegateSpec.properties[k]));\n        }\n        // if a new delegateSpec was set, check if we need to trigger hasTask\n        if (isNewDelegate &&\n            this.lastTaskState &&\n            (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n            this.isNeedToTriggerHasTask = true;\n        }\n    }\n    getDelegate() {\n        return this._delegateSpec;\n    }\n    resetDelegate() {\n        this.getDelegate();\n        this.setDelegate(this.defaultSpecDelegate);\n    }\n    tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n        if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n            // last delegateSpec has microTask or macroTask\n            // should call onHasTask in current delegateSpec\n            this.isNeedToTriggerHasTask = false;\n            this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n        }\n    }\n    removeFromTasks(task) {\n        if (!this.tasks) {\n            return;\n        }\n        for (let i = 0; i < this.tasks.length; i++) {\n            if (this.tasks[i] === task) {\n                this.tasks.splice(i, 1);\n                return;\n            }\n        }\n    }\n    getAndClearPendingTasksInfo() {\n        if (this.tasks.length === 0) {\n            return '';\n        }\n        const taskInfo = this.tasks.map((task) => {\n            const dataInfo = task.data &&\n                Object.keys(task.data)\n                    .map((key) => {\n                    return key + ':' + task.data[key];\n                })\n                    .join(',');\n            return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n        });\n        const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n        // clear tasks\n        this.tasks = [];\n        return pendingTasksInfo;\n    }\n    onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n        if (this._delegateSpec && this._delegateSpec.onFork) {\n            return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n        }\n        else {\n            return parentZoneDelegate.fork(targetZone, zoneSpec);\n        }\n    }\n    onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n        if (this._delegateSpec && this._delegateSpec.onIntercept) {\n            return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n        }\n        else {\n            return parentZoneDelegate.intercept(targetZone, delegate, source);\n        }\n    }\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvoke) {\n            return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n        }\n        else {\n            return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n        }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        if (this._delegateSpec && this._delegateSpec.onHandleError) {\n            return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n        }\n        else {\n            return parentZoneDelegate.handleError(targetZone, error);\n        }\n    }\n    onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.tasks.push(task);\n        }\n        if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n            return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.scheduleTask(targetZone, task);\n        }\n    }\n    onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n            return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n        }\n        else {\n            return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n        }\n    }\n    onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n            return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.cancelTask(targetZone, task);\n        }\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n        this.lastTaskState = hasTaskState;\n        if (this._delegateSpec && this._delegateSpec.onHasTask) {\n            this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n        }\n        else {\n            delegate.hasTask(target, hasTaskState);\n        }\n    }\n}\nfunction patchProxyZoneSpec(Zone) {\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['ProxyZoneSpec'] = ProxyZoneSpec;\n}\n\nfunction patchSyncTest(Zone) {\n    class SyncTestZoneSpec {\n        constructor(namePrefix) {\n            this.runZone = Zone.current;\n            this.name = 'syncTestZone for ' + namePrefix;\n        }\n        onScheduleTask(delegate, current, target, task) {\n            switch (task.type) {\n                case 'microTask':\n                case 'macroTask':\n                    throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n                case 'eventTask':\n                    task = delegate.scheduleTask(target, task);\n                    break;\n            }\n            return task;\n        }\n    }\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n}\n\nfunction patchPromiseTesting(Zone) {\n    /**\n     * Promise for async/fakeAsync zoneSpec test\n     * can support async operation which not supported by zone.js\n     * such as\n     * it ('test jsonp in AsyncZone', async() => {\n     *   new Promise(res => {\n     *     jsonp(url, (data) => {\n     *       // success callback\n     *       res(data);\n     *     });\n     *   }).then((jsonpResult) => {\n     *     // get jsonp result.\n     *\n     *     // user will expect AsyncZoneSpec wait for\n     *     // then, but because jsonp is not zone aware\n     *     // AsyncZone will finish before then is called.\n     *   });\n     * });\n     */\n    Zone.__load_patch('promisefortest', (global, Zone, api) => {\n        const symbolState = api.symbol('state');\n        const UNRESOLVED = null;\n        const symbolParentUnresolved = api.symbol('parentUnresolved');\n        // patch Promise.prototype.then to keep an internal\n        // number for tracking unresolved chained promise\n        // we will decrease this number when the parent promise\n        // being resolved/rejected and chained promise was\n        // scheduled as a microTask.\n        // so we can know such kind of chained promise still\n        // not resolved in AsyncTestZone\n        Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n            let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n            if (oriThen) {\n                return;\n            }\n            oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n            Promise.prototype.then = function () {\n                const chained = oriThen.apply(this, arguments);\n                if (this[symbolState] === UNRESOLVED) {\n                    // parent promise is unresolved.\n                    const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n                    if (asyncTestZoneSpec) {\n                        asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n                        chained[symbolParentUnresolved] = true;\n                    }\n                }\n                return chained;\n            };\n        };\n        Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n            // restore origin then\n            const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n            if (oriThen) {\n                Promise.prototype.then = oriThen;\n                Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n            }\n        };\n    });\n}\n\nfunction rollupTesting(Zone) {\n    patchLongStackTrace(Zone);\n    patchProxyZoneSpec(Zone);\n    patchSyncTest(Zone);\n    patchJasmine(Zone);\n    patchJest(Zone);\n    patchMocha(Zone);\n    patchAsyncTest(Zone);\n    patchFakeAsyncTest(Zone);\n    patchPromiseTesting(Zone);\n}\n\nrollupTesting(Zone);\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,IAAI,EAAE;EACxBA,IAAI,CAACC,YAAY,CAAC,SAAS,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IAChD,MAAMC,SAAS,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;MAC9B,KAAK,MAAMC,CAAC,IAAID,CAAC,EACb,IAAIA,CAAC,CAACE,cAAc,CAACD,CAAC,CAAC,EACnBF,CAAC,CAACE,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;MACnB,SAASE,EAAEA,CAAA,EAAG;QACV,IAAI,CAACC,WAAW,GAAGL,CAAC;MACxB;MACAA,CAAC,CAACM,SAAS,GACPL,CAAC,KAAK,IAAI,GAAGM,MAAM,CAACC,MAAM,CAACP,CAAC,CAAC,IAAKG,EAAE,CAACE,SAAS,GAAGL,CAAC,CAACK,SAAS,EAAG,IAAIF,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;IACD;IACA;IACA,IAAI,CAACT,IAAI,EACL,MAAM,IAAIc,KAAK,CAAC,kBAAkB,CAAC;IACvC,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC7B;MACA;MACA;IACJ;IACA,IAAI,OAAOC,OAAO,IAAI,WAAW,IAAIA,OAAO,CAAC,gBAAgB,CAAC,EAAE;MAC5D;IACJ;IACAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAChC,MAAMC,gBAAgB,GAAGjB,IAAI,CAAC,kBAAkB,CAAC;IACjD,MAAMkB,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACiB,gBAAgB,EACjB,MAAM,IAAIH,KAAK,CAAC,2BAA2B,CAAC;IAChD,IAAI,CAACI,aAAa,EACd,MAAM,IAAIJ,KAAK,CAAC,wBAAwB,CAAC;IAC7C,MAAMK,WAAW,GAAGnB,IAAI,CAACoB,OAAO;IAChC,MAAMC,MAAM,GAAGrB,IAAI,CAACsB,UAAU;IAC9B;IACA,MAAMC,2BAA2B,GAAGrB,MAAM,CAACmB,MAAM,CAAC,+BAA+B,CAAC,CAAC,KAAK,IAAI;IAC5F;IACA;IACA;IACA,MAAMG,mCAAmC,GAAG,CAACD,2BAA2B,KACnErB,MAAM,CAACmB,MAAM,CAAC,oBAAoB,CAAC,CAAC,KAAK,IAAI,IAC1CnB,MAAM,CAACmB,MAAM,CAAC,wCAAwC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC1E,MAAMI,wBAAwB,GAAGvB,MAAM,CAACmB,MAAM,CAAC,0BAA0B,CAAC,CAAC,KAAK,IAAI;IACpF,IAAI,CAACI,wBAAwB,EAAE;MAC3B,MAAMC,YAAY,GAAGV,OAAO,CAACW,YAAY;MACzC,IAAID,YAAY,IAAI,CAACV,OAAO,CAACK,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE;QAClDL,OAAO,CAACK,MAAM,CAAC,cAAc,CAAC,CAAC,GAAGK,YAAY;QAC9CV,OAAO,CAACW,YAAY,GAAG,YAAY;UAC/B,MAAMC,QAAQ,GAAG,IAAIF,YAAY,CAAC,CAAC;UACnC,MAAMG,eAAe,GAAGD,QAAQ,CAACE,OAAO;UACxC,IAAID,eAAe,IAAI,CAACD,QAAQ,CAACP,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;YACjDO,QAAQ,CAACP,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGQ,eAAe;YAC7CD,QAAQ,CAACE,OAAO,GAAG,YAAY;cAC3B,MAAMC,MAAM,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAO,CAACC,EAAE;cAC7D;cACA;cACA;cACA;cACA;cACA;cACA,MAAMC,gBAAgB,GAAGH,MAAM,GACzBC,OAAO,CAACG,SAAS,CAAC,oBAAoB,CAAC,GACvCjC,MAAM,CAACkC,cAAc,CAAC,oBAAoB,CAAC;cACjD,MAAMC,MAAM,GAAGR,eAAe,CAACS,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;cACrDR,MAAM,GACAC,OAAO,CAACQ,kBAAkB,CAAC,oBAAoB,CAAC,GAChDtC,MAAM,CAACsC,kBAAkB,CAAC,oBAAoB,CAAC;cACrD,IAAIN,gBAAgB,EAAE;gBAClBA,gBAAgB,CAACO,OAAO,CAAEC,OAAO,IAAK;kBAClC,IAAIX,MAAM,EAAE;oBACRC,OAAO,CAACC,EAAE,CAAC,oBAAoB,EAAES,OAAO,CAAC;kBAC7C,CAAC,MACI;oBACDxC,MAAM,CAACyC,gBAAgB,CAAC,oBAAoB,EAAED,OAAO,CAAC;kBAC1D;gBACJ,CAAC,CAAC;cACN;cACA,OAAOL,MAAM;YACjB,CAAC;UACL;UACA,OAAOT,QAAQ;QACnB,CAAC;MACL;IACJ;IACA;IACA,MAAMgB,UAAU,GAAG5B,OAAO,CAAC6B,MAAM,CAAC,CAAC;IACnC,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAACJ,OAAO,CAAEK,UAAU,IAAK;MAC3D,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;MAC9CF,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAE;QAC7D,OAAOF,iBAAiB,CAACG,IAAI,CAAC,IAAI,EAAEF,WAAW,EAAEG,kBAAkB,CAACH,WAAW,EAAEC,eAAe,CAAC,CAAC;MACtG,CAAC;IACL,CAAC,CAAC;IACF,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAACR,OAAO,CAAEK,UAAU,IAAK;MACzC,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;MAC9CF,UAAU,CAACvB,MAAM,CAACyB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;MAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAEG,OAAO,EAAE;QACtEb,SAAS,CAAC,CAAC,CAAC,GAAGc,cAAc,CAACJ,eAAe,CAAC;QAC9C,OAAOF,iBAAiB,CAACT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACnD,CAAC;IACL,CAAC,CAAC;IACF,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACE,OAAO,CAAEK,UAAU,IAAK;MACzE,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;MAC9CF,UAAU,CAACvB,MAAM,CAACyB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;MAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUG,eAAe,EAAEG,OAAO,EAAE;QACzDb,SAAS,CAAC,CAAC,CAAC,GAAGc,cAAc,CAACJ,eAAe,CAAC;QAC9C,OAAOF,iBAAiB,CAACT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACnD,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAAChB,2BAA2B,EAAE;MAC9B;MACA;MACA,MAAM+B,eAAe,GAAItC,OAAO,CAACK,MAAM,CAAC,OAAO,CAAC,CAAC,GAAGL,OAAO,CAAC,OAAO,CAAE;MACrEA,OAAO,CAAC,OAAO,CAAC,GAAG,YAAY;QAC3B,MAAMuC,KAAK,GAAGD,eAAe,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QACpD,IAAI,CAACgB,KAAK,CAAClC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;UAC3BkC,KAAK,CAAClC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGA,MAAM,CAAC,SAAS,CAAC;UAC5C,MAAMmC,YAAY,GAAID,KAAK,CAAClC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAGkC,KAAK,CAACE,IAAK;UACzDF,KAAK,CAACE,IAAI,GAAG,YAAY;YACrB,MAAMC,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;YACnE,IAAID,iBAAiB,EAAE;cACnB,OAAOA,iBAAiB,CAACD,IAAI,CAACnB,KAAK,CAACoB,iBAAiB,EAAEnB,SAAS,CAAC;YACrE;YACA,OAAOiB,YAAY,CAAClB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UAC9C,CAAC;UACD,MAAMqB,gBAAgB,GAAIL,KAAK,CAAClC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAGkC,KAAK,CAACM,QAAS;UACrEN,KAAK,CAACM,QAAQ,GAAG,YAAY;YACzB,MAAMH,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;YACnE,IAAID,iBAAiB,EAAE;cACnB,MAAMI,QAAQ,GAAGvB,SAAS,CAACwB,MAAM,GAAG,CAAC,GAAGxB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAIyB,IAAI,CAAC,CAAC;cACjE,OAAON,iBAAiB,CAACO,qBAAqB,CAAC3B,KAAK,CAACoB,iBAAiB,EAAEI,QAAQ,IAAI,OAAOA,QAAQ,CAACI,OAAO,KAAK,UAAU,GACpH,CAACJ,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,GACpB3B,SAAS,CAAC;YACpB;YACA,OAAOqB,gBAAgB,CAACtB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UAClD,CAAC;UACD;UACA,IAAIf,mCAAmC,EAAE;YACrC,CAAC,SAAS,EAAE,WAAW,CAAC,CAACiB,OAAO,CAAEK,UAAU,IAAK;cAC7C,MAAMQ,eAAe,GAAIC,KAAK,CAAClC,MAAM,CAACyB,UAAU,CAAC,CAAC,GAAGS,KAAK,CAACT,UAAU,CAAE;cACvES,KAAK,CAACT,UAAU,CAAC,GAAG,YAAY;gBAC5B,MAAMqB,qBAAqB,GAAGnE,IAAI,CAAC,uBAAuB,CAAC;gBAC3D,IAAImE,qBAAqB,EAAE;kBACvBnD,OAAO,CAACK,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,SAAS,KAAKyB,UAAU;kBAC5D;gBACJ;gBACA,OAAOQ,eAAe,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;cACjD,CAAC;YACL,CAAC,CAAC;UACN;QACJ;QACA,OAAOgB,KAAK;MAChB,CAAC;IACL;IACA;IACA,IAAI,CAACvC,OAAO,CAAChB,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE;MAC3C,MAAM8C,oBAAoB,GAAGpD,OAAO,CAACqD,YAAY;MACjDrD,OAAO,CAAChB,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC,CAAC,GAAG8C,oBAAoB;MAC/DpD,OAAO,CAACqD,YAAY,GAAG,YAAY;QAC/B,MAAMC,IAAI,GAAGC,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACX,SAAS,CAAC;QAClD,MAAMkC,aAAa,GAAGH,IAAI,CAACP,MAAM,IAAI,CAAC,GAAGO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;QACvD,IAAII,MAAM;QACV,IAAID,aAAa,EAAE;UACf,MAAME,cAAc,GAAG/D,MAAM,CAAC+D,cAAc;UAC5C/D,MAAM,CAAC+D,cAAc,GAAG,UAAUC,GAAG,EAAErE,CAAC,EAAEsE,UAAU,EAAE;YAClD,OAAOF,cAAc,CAACzB,IAAI,CAAC,IAAI,EAAE0B,GAAG,EAAErE,CAAC,EAAE;cACrC,GAAGsE,UAAU;cACbC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAE;YAChB,CAAC,CAAC;UACN,CAAC;UACD,IAAI;YACAL,MAAM,GAAGN,oBAAoB,CAAC9B,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;UACnD,CAAC,SACO;YACJ1D,MAAM,CAAC+D,cAAc,GAAGA,cAAc;UAC1C;QACJ,CAAC,MACI;UACDD,MAAM,GAAGN,oBAAoB,CAAC9B,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QACnD;QACA,OAAOI,MAAM;MACjB,CAAC;IACL;IACA;AACR;AACA;AACA;IACQ,SAASvB,kBAAkBA,CAACH,WAAW,EAAEgC,YAAY,EAAE;MACnD,OAAO,YAAY;QACf;QACA;QACA,MAAMC,QAAQ,GAAG9D,WAAW,CAAC+D,IAAI,CAAC,IAAIjE,gBAAgB,CAAC,oBAAoB+B,WAAW,EAAE,CAAC,CAAC;QAC1F,OAAOiC,QAAQ,CAACE,GAAG,CAACH,YAAY,EAAE,IAAI,EAAEzC,SAAS,CAAC;MACtD,CAAC;IACL;IACA,SAAS6C,aAAaA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,IAAI,EAAE;MAC3D,MAAMC,gBAAgB,GAAG,CAAC,CAACzE,OAAO,CAACK,MAAM,CAAC,gBAAgB,CAAC,CAAC;MAC5DkE,WAAW,CAACG,iBAAiB;MAC7B,MAAMC,aAAa,GAAGJ,WAAW,CAACI,aAAa;MAC/C,IAAIF,gBAAgB,IAAIjE,mCAAmC,EAAE;QACzD;QACA,MAAMoE,eAAe,GAAG5F,IAAI,CAACA,IAAI,CAACsB,UAAU,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAIsE,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;UACpER,QAAQ,GAAGO,eAAe,CAACC,SAAS,CAACR,QAAQ,CAAC;QAClD;MACJ;MACA,IAAIG,IAAI,EAAE;QACN,OAAOG,aAAa,CAACR,GAAG,CAACE,QAAQ,EAAEC,SAAS,EAAE,CAACE,IAAI,CAAC,CAAC;MACzD,CAAC,MACI;QACD,OAAOG,aAAa,CAACR,GAAG,CAACE,QAAQ,EAAEC,SAAS,CAAC;MACjD;IACJ;IACA;AACR;AACA;AACA;AACA;IACQ,SAASjC,cAAcA,CAACgC,QAAQ,EAAE;MAC9B;MACA;MACA;MACA,OAAQA,QAAQ,KACXA,QAAQ,CAACtB,MAAM,GACV,UAAUyB,IAAI,EAAE;QACd,OAAOJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACE,WAAW,EAAEC,IAAI,CAAC;MAChE,CAAC,GACC,YAAY;QACV,OAAOJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACE,WAAW,CAAC;MAC1D,CAAC,CAAC;IACd;IACA,MAAMO,WAAW,GAAG9E,OAAO,CAAC8E,WAAW;IACvC9E,OAAO,CAAC8E,WAAW,GAAI,UAAUC,MAAM,EAAE;MACrC3F,SAAS,CAAC4F,eAAe,EAAED,MAAM,CAAC;MAClC,SAASC,eAAeA,CAACC,KAAK,EAAE;QAC5B,IAAIA,KAAK,CAACC,UAAU,EAAE;UAClBD,KAAK,CAACC,UAAU,GAAG,CAAEC,EAAE,IAAK,MAAM;YAC9B;YACA,IAAI,CAACR,aAAa,GAAG,IAAI;YACzB,IAAI,CAACD,iBAAiB,GAAG,IAAI;YAC7BvE,WAAW,CAACiF,iBAAiB,CAAC,oBAAoB,EAAED,EAAE,CAAC;UAC3D,CAAC,EAAEF,KAAK,CAACC,UAAU,CAAC;QACxB;QACA,MAAMG,gBAAgB,GAAGnG,MAAM,CAACF,IAAI,CAACsB,UAAU,CAAC,YAAY,CAAC,CAAC;QAC9D,MAAMgF,kBAAkB,GAAGpG,MAAM,CAACF,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC,CAAC;QAClE,IAAI+E,gBAAgB,EAAE;UAClB;UACAJ,KAAK,CAAC7C,OAAO,GAAG;YACZmD,UAAU,EAAEF,gBAAgB,GAAGA,gBAAgB,GAAGnG,MAAM,CAACqG,UAAU;YACnEC,YAAY,EAAEF,kBAAkB,GAAGA,kBAAkB,GAAGpG,MAAM,CAACsG;UACnE,CAAC;QACL;QACA;QACA;QACA,IAAIxF,OAAO,CAACyF,WAAW,EAAE;UACrB,IAAI,CAACR,KAAK,CAACS,WAAW,EAAE;YACpBT,KAAK,CAACS,WAAW,GAAG,IAAI1F,OAAO,CAACyF,WAAW,CAAC,CAAC;UACjD;UACAR,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;QACxC,CAAC,MACI;UACD,IAAI,CAACU,KAAK,CAACS,WAAW,EAAE;YACpBT,KAAK,CAACS,WAAW,GAAG,CAAC,CAAC;UAC1B;UACAT,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;QACxC;QACA;QACA,MAAMoB,WAAW,GAAGV,KAAK,CAACU,WAAW;QACrCV,KAAK,CAACU,WAAW,GAAG,UAAUC,KAAK,EAAE;UACjC,IAAIA,KAAK,IACLA,KAAK,CAACC,OAAO,KACT,wGAAwG,EAAE;YAC9G;YACA;YACA,MAAMC,aAAa,GAAG,IAAI,IAAI,IAAI,CAACpB,iBAAiB;YACpD,IAAIoB,aAAa,EAAE;cACf,MAAMC,gBAAgB,GAAGD,aAAa,CAACE,2BAA2B,CAAC,CAAC;cACpE,IAAI;gBACA;gBACAJ,KAAK,CAACC,OAAO,IAAIE,gBAAgB;cACrC,CAAC,CACD,OAAOE,GAAG,EAAE,CAAE;YAClB;UACJ;UACA,IAAIN,WAAW,EAAE;YACbA,WAAW,CAACzD,IAAI,CAAC,IAAI,EAAE0D,KAAK,CAAC;UACjC;QACJ,CAAC;QACDb,MAAM,CAAC7C,IAAI,CAAC,IAAI,EAAE+C,KAAK,CAAC;MAC5B;MACAD,eAAe,CAACrF,SAAS,CAACuG,OAAO,GAAG,YAAY;QAC5C,IAAIC,IAAI,GAAGnH,IAAI,CAACoB,OAAO;QACvB,IAAIgG,oBAAoB,GAAG,KAAK;QAChC,OAAOD,IAAI,EAAE;UACT,IAAIA,IAAI,KAAKhG,WAAW,EAAE;YACtBiG,oBAAoB,GAAG,IAAI;YAC3B;UACJ;UACAD,IAAI,GAAGA,IAAI,CAACE,MAAM;QACtB;QACA,IAAI,CAACD,oBAAoB,EACrB,MAAM,IAAItG,KAAK,CAAC,mBAAmB,GAAGd,IAAI,CAACoB,OAAO,CAACkG,IAAI,CAAC;QAC5D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC5B,iBAAiB,GAAG,IAAIxE,aAAa,CAAC,CAAC;QAC5C,IAAI,CAACyE,aAAa,GAAGxE,WAAW,CAAC+D,IAAI,CAAC,IAAI,CAACQ,iBAAiB,CAAC;QAC7D,IAAI,CAAC1F,IAAI,CAACuH,WAAW,EAAE;UACnB;UACA;UACA;UACA;UACA;UACAvH,IAAI,CAACoB,OAAO,CAACgF,iBAAiB,CAAC,6BAA6B,EAAE,MAAMN,WAAW,CAACnF,SAAS,CAACuG,OAAO,CAAChE,IAAI,CAAC,IAAI,CAAC,CAAC;QACjH,CAAC,MACI;UACD6C,MAAM,CAACpF,SAAS,CAACuG,OAAO,CAAChE,IAAI,CAAC,IAAI,CAAC;QACvC;MACJ,CAAC;MACD,OAAO8C,eAAe;IAC1B,CAAC,CAAEF,WAAW,CAAC;EACnB,CAAC,CAAC;AACN;AAEA,SAAS0B,SAASA,CAACxH,IAAI,EAAE;EACrBA,IAAI,CAACC,YAAY,CAAC,MAAM,EAAE,CAACwH,OAAO,EAAEzH,IAAI,EAAEG,GAAG,KAAK;IAC9C,IAAI,OAAOY,IAAI,KAAK,WAAW,IAAIA,IAAI,CAAC,gBAAgB,CAAC,EAAE;MACvD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAf,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,iCAAiC,CAAC,CAAC,GAAG,IAAI;IAC1DN,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAC7B,MAAMG,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;IAC3C,MAAMiB,gBAAgB,GAAGjB,IAAI,CAAC,kBAAkB,CAAC;IACjD,IAAI,CAACkB,aAAa,EAAE;MAChB,MAAM,IAAIJ,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IACA,MAAM4G,QAAQ,GAAG1H,IAAI,CAACoB,OAAO;IAC7B,MAAM6D,QAAQ,GAAGyC,QAAQ,CAACxC,IAAI,CAAC,IAAIjE,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACrE,MAAM6F,aAAa,GAAG,IAAI5F,aAAa,CAAC,CAAC;IACzC,MAAMyG,SAAS,GAAGD,QAAQ,CAACxC,IAAI,CAAC4B,aAAa,CAAC;IAC9C,SAASc,yBAAyBA,CAACC,cAAc,EAAE;MAC/C,OAAO,UAAU,GAAGC,SAAS,EAAE;QAC3B,MAAMC,kBAAkB,GAAGF,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEwF,SAAS,CAAC;QAChE,OAAO,UAAU,GAAGxD,IAAI,EAAE;UACtBA,IAAI,CAAC,CAAC,CAAC,GAAGnB,kBAAkB,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;UACrC,OAAOyD,kBAAkB,CAACzF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QAC/C,CAAC;MACL,CAAC;IACL;IACA,SAAS0D,qBAAqBA,CAACH,cAAc,EAAE;MAC3C,OAAO,UAAU,GAAGC,SAAS,EAAE;QAC3B,OAAO,UAAU,GAAGxD,IAAI,EAAE;UACtBA,IAAI,CAAC,CAAC,CAAC,GAAGjB,cAAc,CAACiB,IAAI,CAAC,CAAC,CAAC,CAAC;UACjC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEwF,SAAS,CAAC,CAACxF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QAClE,CAAC;MACL,CAAC;IACL;IACA;AACR;AACA;AACA;IACQ,SAASnB,kBAAkBA,CAAC6B,YAAY,EAAE;MACtC,OAAO,UAAU,GAAGV,IAAI,EAAE;QACtB,OAAOW,QAAQ,CAACE,GAAG,CAACH,YAAY,EAAE,IAAI,EAAEV,IAAI,CAAC;MACjD,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;IACQ,SAASjB,cAAcA,CAACgC,QAAQ,EAAE4C,UAAU,GAAG,KAAK,EAAE;MAClD,IAAI,OAAO5C,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOA,QAAQ;MACnB;MACA,MAAM6C,WAAW,GAAG,SAAAA,CAAA,EAAY;QAC5B,IAAIlI,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,KAAK,IAAI,IAChDgE,QAAQ,IACR,CAACA,QAAQ,CAAC8C,WAAW,EAAE;UACvB;UACA,MAAMvC,eAAe,GAAG5F,IAAI,CAACA,IAAI,CAACsB,UAAU,CAAC,eAAe,CAAC,CAAC;UAC9D,IAAIsE,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;YACpER,QAAQ,GAAGO,eAAe,CAACC,SAAS,CAACR,QAAQ,CAAC;UAClD;QACJ;QACAyB,aAAa,CAACmB,UAAU,GAAGA,UAAU;QACrC,OAAON,SAAS,CAACxC,GAAG,CAACE,QAAQ,EAAE,IAAI,EAAE9C,SAAS,CAAC;MACnD,CAAC;MACD;MACA;MACA3B,MAAM,CAAC+D,cAAc,CAACuD,WAAW,EAAE,QAAQ,EAAE;QACzCpD,YAAY,EAAE,IAAI;QAClBsD,QAAQ,EAAE,IAAI;QACdrD,UAAU,EAAE;MAChB,CAAC,CAAC;MACFmD,WAAW,CAACnE,MAAM,GAAGsB,QAAQ,CAACtB,MAAM;MACpC,OAAOmE,WAAW;IACtB;IACA,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAACzF,OAAO,CAAEK,UAAU,IAAK;MAC3D,IAAI+E,cAAc,GAAGJ,OAAO,CAAC3E,UAAU,CAAC;MACxC,IAAI2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,EAAE;QACtC;MACJ;MACA2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,GAAG+E,cAAc;MACrDJ,OAAO,CAAC3E,UAAU,CAAC,GAAG,UAAU,GAAGwB,IAAI,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGnB,kBAAkB,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;MAC3C,CAAC;MACDmD,OAAO,CAAC3E,UAAU,CAAC,CAACuF,IAAI,GAAGT,yBAAyB,CAACC,cAAc,CAACQ,IAAI,CAAC;IAC7E,CAAC,CAAC;IACFZ,OAAO,CAACa,QAAQ,CAACC,IAAI,GAAGd,OAAO,CAACe,SAAS;IACzCf,OAAO,CAACa,QAAQ,CAACG,IAAI,GAAGhB,OAAO,CAACiB,SAAS;IACzC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACjG,OAAO,CAAEK,UAAU,IAAK;MAC1D,IAAI+E,cAAc,GAAGJ,OAAO,CAAC3E,UAAU,CAAC;MACxC,IAAI2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,EAAE;QACtC;MACJ;MACA2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,GAAG+E,cAAc;MACrDJ,OAAO,CAAC3E,UAAU,CAAC,GAAG,UAAU,GAAGwB,IAAI,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGjB,cAAc,CAACiB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACvC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;MAC3C,CAAC;MACDmD,OAAO,CAAC3E,UAAU,CAAC,CAACuF,IAAI,GAAGL,qBAAqB,CAACH,cAAc,CAACQ,IAAI,CAAC;MACrEZ,OAAO,CAAC3E,UAAU,CAAC,CAAC6F,IAAI,GAAGd,cAAc,CAACc,IAAI;IAClD,CAAC,CAAC;IACFlB,OAAO,CAACmB,EAAE,CAACL,IAAI,GAAGd,OAAO,CAACoB,GAAG;IAC7BpB,OAAO,CAACmB,EAAE,CAACH,IAAI,GAAGhB,OAAO,CAACqB,GAAG;IAC7BrB,OAAO,CAACsB,IAAI,CAACR,IAAI,GAAGd,OAAO,CAACoB,GAAG;IAC/BpB,OAAO,CAACsB,IAAI,CAACN,IAAI,GAAGhB,OAAO,CAACqB,GAAG;IAC/B,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACrG,OAAO,CAAEK,UAAU,IAAK;MACzE,IAAI+E,cAAc,GAAGJ,OAAO,CAAC3E,UAAU,CAAC;MACxC,IAAI2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,EAAE;QACtC;MACJ;MACA2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,GAAG+E,cAAc;MACrDJ,OAAO,CAAC3E,UAAU,CAAC,GAAG,UAAU,GAAGwB,IAAI,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGjB,cAAc,CAACiB,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;MAC3C,CAAC;IACL,CAAC,CAAC;IACFtE,IAAI,CAACgJ,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,QAAQ,GAAG,KAAK,EAAE;MACrE;MACA,SAASC,mBAAmBA,CAAA,EAAG;QAC3B,MAAMzF,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;QACnE,OAAO,CAAC,CAACD,iBAAiB;MAC9B;MACA;MACA;MACA,SAAS0F,YAAYA,CAAA,EAAG;QACpB,MAAMtC,aAAa,GAAG9G,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,eAAe,CAAC;QACvD,OAAOmD,aAAa,IAAIA,aAAa,CAACmB,UAAU;MACpD;MACA,IAAIgB,KAAK,CAAC9I,GAAG,CAACkB,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE;QACjC;MACJ;MACA4H,KAAK,CAAC9I,GAAG,CAACkB,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;MACtC;MACAlB,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,kBAAkB,EAAGK,QAAQ,IAAK;QACrD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,IAAI6E,mBAAmB,CAAC,CAAC,EAAE;YACvB,OAAO,IAAI;UACf,CAAC,MACI;YACD,OAAOG,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzBtE,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI;UAC9C,IAAI6H,QAAQ,IAAIE,YAAY,CAAC,CAAC,EAAE;YAC5B,OAAOE,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;UACA,OAAOiF,IAAI;QACf,CAAC;MACL,CAAC,CAAC;MACF;MACApJ,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzBtE,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK;UAC/C,IAAI6H,QAAQ,IAAIE,YAAY,CAAC,CAAC,EAAE;YAC5B,OAAOE,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;UACA,OAAOiF,IAAI;QACf,CAAC;MACL,CAAC,CAAC;MACF;MACApJ,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,IAAIyF,mBAAmB,CAAC,CAAC,EAAE;YAC5CzF,iBAAiB,CAACO,qBAAqB,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,MACI;YACD,OAAOgF,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,mBAAmB,EAAGK,QAAQ,IAAK;QACtD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,IAAIyF,mBAAmB,CAAC,CAAC,EAAE;YAC5C,OAAOzF,iBAAiB,CAAC8F,iBAAiB,CAAC,CAAC;UAChD,CAAC,MACI;YACD,OAAOF,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,aAAa,EAAGK,QAAQ,IAAK;QAChD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAAC+F,eAAe,CAAC,CAAC;UACvC,CAAC,MACI;YACD,OAAOH,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,cAAc,EAAGK,QAAQ,IAAK;QACjD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACgG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;UACtC,CAAC,MACI;YACD,OAAOJ,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,qBAAqB,EAAGK,QAAQ,IAAK;QACxD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACD,IAAI,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,MACI;YACD,OAAOgF,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,sBAAsB,EAAGK,QAAQ,IAAK;QACzD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACiG,sBAAsB,CAAC,CAAC;UAC9C,CAAC,MACI;YACD,OAAOL,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,0BAA0B,EAAGK,QAAQ,IAAK;QAC7D,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACkG,UAAU,CAACtF,IAAI,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,MACI;YACD,OAAOgF,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,gBAAgB,EAAGK,QAAQ,IAAK;QACnD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACmG,eAAe,CAAC,CAAC;UACvC,CAAC,MACI;YACD,OAAOP,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACkJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAEjF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnB,OAAOA,iBAAiB,CAACoG,aAAa,CAAC,CAAC;UAC5C,CAAC,MACI;YACD,OAAOR,QAAQ,CAAChH,KAAK,CAACiH,IAAI,EAAEjF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAASyF,UAAUA,CAAC/J,IAAI,EAAE;EACtBA,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE,CAACC,MAAM,EAAEF,IAAI,KAAK;IACzC,MAAMgK,KAAK,GAAG9J,MAAM,CAAC8J,KAAK;IAC1B,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAC9B;MACA;MACA;IACJ;IACA,IAAI,OAAOhK,IAAI,KAAK,WAAW,EAAE;MAC7B,MAAM,IAAIc,KAAK,CAAC,iBAAiB,CAAC;IACtC;IACA,MAAMI,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;IAC3C,MAAMiB,gBAAgB,GAAGjB,IAAI,CAAC,kBAAkB,CAAC;IACjD,IAAI,CAACkB,aAAa,EAAE;MAChB,MAAM,IAAIJ,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IACA,IAAIkJ,KAAK,CAAC,gBAAgB,CAAC,EAAE;MACzB,MAAM,IAAIlJ,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACAkJ,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAC9B,MAAMtC,QAAQ,GAAG1H,IAAI,CAACoB,OAAO;IAC7B,MAAM6D,QAAQ,GAAGyC,QAAQ,CAACxC,IAAI,CAAC,IAAIjE,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACtE,IAAIgJ,QAAQ,GAAG,IAAI;IACnB,MAAMC,SAAS,GAAGxC,QAAQ,CAACxC,IAAI,CAAC,IAAIhE,aAAa,CAAC,CAAC,CAAC;IACpD,MAAMiJ,aAAa,GAAG;MAClBC,KAAK,EAAElK,MAAM,CAACkK,KAAK;MACnBC,SAAS,EAAEnK,MAAM,CAACmK,SAAS;MAC3BC,MAAM,EAAEpK,MAAM,CAACoK,MAAM;MACrBC,UAAU,EAAErK,MAAM,CAACqK,UAAU;MAC7BjC,QAAQ,EAAEpI,MAAM,CAACoI,QAAQ;MACzBM,EAAE,EAAE1I,MAAM,CAAC0I;IACf,CAAC;IACD,SAAS4B,eAAeA,CAAClG,IAAI,EAAEmG,QAAQ,EAAEC,SAAS,EAAE;MAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrG,IAAI,CAACP,MAAM,EAAE4G,CAAC,EAAE,EAAE;QAClC,IAAIC,GAAG,GAAGtG,IAAI,CAACqG,CAAC,CAAC;QACjB,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACAtG,IAAI,CAACqG,CAAC,CAAC,GAAGC,GAAG,CAAC7G,MAAM,KAAK,CAAC,GAAG0G,QAAQ,CAACG,GAAG,CAAC,GAAGF,SAAS,CAACE,GAAG,CAAC;UAC3D;UACA;UACAtG,IAAI,CAACqG,CAAC,CAAC,CAACE,QAAQ,GAAG,YAAY;YAC3B,OAAOD,GAAG,CAACC,QAAQ,CAAC,CAAC;UACzB,CAAC;QACL;MACJ;MACA,OAAOvG,IAAI;IACf;IACA,SAASnB,kBAAkBA,CAACmB,IAAI,EAAE;MAC9B,MAAMmG,QAAQ,GAAG,SAAAA,CAAUtE,EAAE,EAAE;QAC3B,OAAO,YAAY;UACf,OAAOlB,QAAQ,CAACE,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE5D,SAAS,CAAC;QAC5C,CAAC;MACL,CAAC;MACD,OAAOiI,eAAe,CAAClG,IAAI,EAAEmG,QAAQ,CAAC;IAC1C;IACA,SAASpH,cAAcA,CAACiB,IAAI,EAAE;MAC1B,MAAMoG,SAAS,GAAG,SAAAA,CAAUvE,EAAE,EAAE;QAC5B,OAAO,UAAUX,IAAI,EAAE;UACnB,OAAOyE,QAAQ,CAAC9E,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;QACzC,CAAC;MACL,CAAC;MACD,MAAMiF,QAAQ,GAAG,SAAAA,CAAUtE,EAAE,EAAE;QAC3B,OAAO,YAAY;UACf,OAAO8D,QAAQ,CAAC9E,GAAG,CAACgB,EAAE,EAAE,IAAI,CAAC;QACjC,CAAC;MACL,CAAC;MACD,OAAOqE,eAAe,CAAClG,IAAI,EAAEmG,QAAQ,EAAEC,SAAS,CAAC;IACrD;IACA,SAASI,eAAeA,CAACxG,IAAI,EAAE;MAC3B,MAAMoG,SAAS,GAAG,SAAAA,CAAUvE,EAAE,EAAE;QAC5B,OAAO,UAAUX,IAAI,EAAE;UACnB,OAAO0E,SAAS,CAAC/E,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;QAC1C,CAAC;MACL,CAAC;MACD,MAAMiF,QAAQ,GAAG,SAAAA,CAAUtE,EAAE,EAAE;QAC3B,OAAO,YAAY;UACf,OAAO+D,SAAS,CAAC/E,GAAG,CAACgB,EAAE,EAAE,IAAI,CAAC;QAClC,CAAC;MACL,CAAC;MACD,OAAOqE,eAAe,CAAClG,IAAI,EAAEmG,QAAQ,EAAEC,SAAS,CAAC;IACrD;IACAxK,MAAM,CAACoI,QAAQ,GAAGpI,MAAM,CAAC6K,KAAK,GAAG,YAAY;MACzC,OAAOZ,aAAa,CAAC7B,QAAQ,CAAChG,KAAK,CAAC,IAAI,EAAEa,kBAAkB,CAACZ,SAAS,CAAC,CAAC;IAC5E,CAAC;IACDrC,MAAM,CAACwI,SAAS,GACZxI,MAAM,CAAC6K,KAAK,CAACtC,IAAI,GACbvI,MAAM,CAACoI,QAAQ,CAACG,IAAI,GAChB,YAAY;MACR,OAAO0B,aAAa,CAAC7B,QAAQ,CAACG,IAAI,CAACnG,KAAK,CAAC,IAAI,EAAEa,kBAAkB,CAACZ,SAAS,CAAC,CAAC;IACjF,CAAC;IACbrC,MAAM,CAACoI,QAAQ,CAACC,IAAI,GAAGrI,MAAM,CAAC6K,KAAK,CAACxC,IAAI,GAAG,YAAY;MACnD,OAAO4B,aAAa,CAAC7B,QAAQ,CAACC,IAAI,CAACjG,KAAK,CAAC,IAAI,EAAEa,kBAAkB,CAACZ,SAAS,CAAC,CAAC;IACjF,CAAC;IACDrC,MAAM,CAAC0I,EAAE,GACL1I,MAAM,CAAC8K,OAAO,GACV9K,MAAM,CAAC6I,IAAI,GACP,YAAY;MACR,OAAOoB,aAAa,CAACvB,EAAE,CAACtG,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IAClE,CAAC;IACbrC,MAAM,CAAC4I,GAAG,GACN5I,MAAM,CAAC+K,QAAQ,GACX/K,MAAM,CAAC0I,EAAE,CAACH,IAAI,GACV,YAAY;MACR,OAAO0B,aAAa,CAACvB,EAAE,CAACH,IAAI,CAACnG,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IACvE,CAAC;IACbrC,MAAM,CAAC0I,EAAE,CAACL,IAAI,GAAGrI,MAAM,CAAC6I,IAAI,CAACR,IAAI,GAAG,YAAY;MAC5C,OAAO4B,aAAa,CAACvB,EAAE,CAACL,IAAI,CAACjG,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IACvE,CAAC;IACDrC,MAAM,CAACkK,KAAK,GAAGlK,MAAM,CAACgL,aAAa,GAAG,YAAY;MAC9C,OAAOf,aAAa,CAACC,KAAK,CAAC9H,KAAK,CAAC,IAAI,EAAEwI,eAAe,CAACvI,SAAS,CAAC,CAAC;IACtE,CAAC;IACDrC,MAAM,CAACmK,SAAS,GAAGnK,MAAM,CAACiL,QAAQ,GAAG,YAAY;MAC7C,OAAOhB,aAAa,CAACE,SAAS,CAAC/H,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IACzE,CAAC;IACDrC,MAAM,CAACoK,MAAM,GAAGpK,MAAM,CAACkL,UAAU,GAAG,YAAY;MAC5C,OAAOjB,aAAa,CAACG,MAAM,CAAChI,KAAK,CAAC,IAAI,EAAEwI,eAAe,CAACvI,SAAS,CAAC,CAAC;IACvE,CAAC;IACDrC,MAAM,CAACqK,UAAU,GAAGrK,MAAM,CAACmL,KAAK,GAAG,YAAY;MAC3C,OAAOlB,aAAa,CAACI,UAAU,CAACjI,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IAC1E,CAAC;IACD,CAAC,CAAC+I,eAAe,EAAEC,WAAW,KAAK;MAC/BvB,KAAK,CAACwB,MAAM,CAAC7K,SAAS,CAAC8K,OAAO,GAAG,UAAUtF,EAAE,EAAE;QAC3CnG,IAAI,CAACoB,OAAO,CAACgF,iBAAiB,CAAC,iBAAiB,EAAE,MAAM;UACpDkF,eAAe,CAACpI,IAAI,CAAC,IAAI,EAAEiD,EAAE,CAAC;QAClC,CAAC,CAAC;MACN,CAAC;MACD6D,KAAK,CAACwB,MAAM,CAAC7K,SAAS,CAACwE,GAAG,GAAG,UAAUgB,EAAE,EAAE;QACvC,IAAI,CAAClE,EAAE,CAAC,MAAM,EAAGyJ,CAAC,IAAK;UACnBzB,QAAQ,GAAGvC,QAAQ,CAACxC,IAAI,CAAC,IAAIhE,aAAa,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC;QACF,IAAI,CAACe,EAAE,CAAC,MAAM,EAAE,CAAC8G,IAAI,EAAE9B,GAAG,KAAK;UAC3B,MAAMH,aAAa,GAAGmD,QAAQ,IAAIA,QAAQ,CAACtG,GAAG,CAAC,eAAe,CAAC;UAC/D,IAAImD,aAAa,IAAIG,GAAG,EAAE;YACtB,IAAI;cACA;cACAA,GAAG,CAACJ,OAAO,IAAIC,aAAa,CAACE,2BAA2B,CAAC,CAAC;YAC9D,CAAC,CACD,OAAOJ,KAAK,EAAE,CAAE;UACpB;QACJ,CAAC,CAAC;QACF,OAAO2E,WAAW,CAACrI,IAAI,CAAC,IAAI,EAAEiD,EAAE,CAAC;MACrC,CAAC;IACL,CAAC,EAAE6D,KAAK,CAACwB,MAAM,CAAC7K,SAAS,CAAC8K,OAAO,EAAEzB,KAAK,CAACwB,MAAM,CAAC7K,SAAS,CAACwE,GAAG,CAAC;EAClE,CAAC,CAAC;AACN;AAEA,MAAMwG,QAAQ,GAAGC,UAAU;AAC3B;AACA;AACA,SAAStK,UAAUA,CAACgG,IAAI,EAAE;EACtB,MAAMuE,YAAY,GAAGF,QAAQ,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;EAC1E,OAAOE,YAAY,GAAGvE,IAAI;AAC9B;AAEA,MAAMwE,QAAQ,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAM,OAAOxC,IAAI,KAAK,WAAW,IAAIA,IAAK,IAAIrJ,MAAM;AAC7G,MAAM8L,iBAAiB,CAAC;EACpB;EACA;EACA;EACA,WAAWC,sBAAsBA,CAAA,EAAG;IAChC,OAAO3K,UAAU,CAAC,kBAAkB,CAAC;EACzC;EACAZ,WAAWA,CAACwL,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAE;IAClD,IAAI,CAACF,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,OAAO,GAAG3M,IAAI,CAACoB,OAAO;IAC3B,IAAI,CAACwL,6BAA6B,GAAG,CAAC;IACtC,IAAI,CAACC,mCAAmC,GAAG,KAAK;IAChD,IAAI,CAACvF,IAAI,GAAG,oBAAoB,GAAG8E,UAAU;IAC7C,IAAI,CAACU,UAAU,GAAG;MAAE,mBAAmB,EAAE;IAAK,CAAC;IAC/C,IAAI,CAACD,mCAAmC,GACpCf,QAAQ,CAACxK,UAAU,CAAC,qCAAqC,CAAC,CAAC,KAAK,IAAI;EAC5E;EACAyL,iCAAiCA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACH,6BAA6B,GAAG,CAAC;EACjD;EACAI,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACP,oBAAoB,KAAK,IAAI,EAAE;MACpCjG,YAAY,CAAC,IAAI,CAACiG,oBAAoB,CAAC;MACvC,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;IACA,IAAI,EAAE,IAAI,CAACJ,kBAAkB,IACzB,IAAI,CAACC,kBAAkB,IACtB,IAAI,CAACO,mCAAmC,IAAI,IAAI,CAACE,iCAAiC,CAAC,CAAE,CAAC,EAAE;MACzF;MACA;MACA,IAAI,CAACJ,OAAO,CAACxH,GAAG,CAAC,MAAM;QACnB,IAAI,CAACsH,oBAAoB,GAAGlG,UAAU,CAAC,MAAM;UACzC,IAAI,CAAC,IAAI,CAACgG,eAAe,IAAI,EAAE,IAAI,CAACF,kBAAkB,IAAI,IAAI,CAACC,kBAAkB,CAAC,EAAE;YAChF,IAAI,CAACJ,cAAc,CAAC,CAAC;UACzB;QACJ,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC;IACN;EACJ;EACAe,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACJ,mCAAmC,EAAE;MAC3C;IACJ;IACA,MAAMI,mBAAmB,GAAGC,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,qBAAqB,CAAC,CAAC;IAC3E,IAAI2L,mBAAmB,EAAE;MACrBA,mBAAmB,CAAC,CAAC;IACzB;EACJ;EACAE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACN,mCAAmC,EAAE;MAC3C;IACJ;IACA,MAAMM,qBAAqB,GAAGD,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAC/E,IAAI6L,qBAAqB,EAAE;MACvBA,qBAAqB,CAAC,CAAC;IAC3B;EACJ;EACAC,cAAcA,CAAC9D,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEC,IAAI,EAAE;IAC5C,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACf,OAAO,GAAG,KAAK;IACxB;IACA,IAAIc,IAAI,CAACC,IAAI,KAAK,WAAW,IAAID,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,YAAYN,OAAO,EAAE;MACxE;MACA,IAAII,IAAI,CAACE,IAAI,CAACxB,iBAAiB,CAACC,sBAAsB,CAAC,KAAK,IAAI,EAAE;QAC9D;QACA,IAAI,CAACW,6BAA6B,EAAE;MACxC;IACJ;IACA,OAAOtD,QAAQ,CAACmE,YAAY,CAACJ,MAAM,EAAEC,IAAI,CAAC;EAC9C;EACAI,YAAYA,CAACpE,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEC,IAAI,EAAEhI,SAAS,EAAEqI,SAAS,EAAE;IAChE,IAAIL,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACf,OAAO,GAAG,KAAK;IACxB;IACA,OAAOlD,QAAQ,CAACsE,UAAU,CAACP,MAAM,EAAEC,IAAI,EAAEhI,SAAS,EAAEqI,SAAS,CAAC;EAClE;EACAE,YAAYA,CAACvE,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEC,IAAI,EAAE;IAC1C,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACf,OAAO,GAAG,KAAK;IACxB;IACA,OAAOlD,QAAQ,CAACwE,UAAU,CAACT,MAAM,EAAEC,IAAI,CAAC;EAC5C;EACA;EACA;EACA;EACA;EACA;EACAS,QAAQA,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE5E,QAAQ,EAAEhE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,EAAE;IAC1F,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAGpD,QAAQ;IACjC;IACA,IAAI;MACA,IAAI,CAACkD,OAAO,GAAG,IAAI;MACnB,OAAOwB,kBAAkB,CAACI,MAAM,CAACF,UAAU,EAAE5E,QAAQ,EAAEhE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IACxF,CAAC,SACO;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC3B,OAAO,IAAI,IAAI,CAACE,aAAa,KAAKpD,QAAQ,EAAE;QACjD,IAAI,CAAC0D,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACAqB,aAAaA,CAACL,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;IAC9D;IACA,MAAMvE,MAAM,GAAG2L,kBAAkB,CAACM,WAAW,CAACJ,UAAU,EAAEtH,KAAK,CAAC;IAChE,IAAIvE,MAAM,EAAE;MACR,IAAI,CAAC8J,YAAY,CAACvF,KAAK,CAAC;MACxB,IAAI,CAAC2F,eAAe,GAAG,IAAI;IAC/B;IACA,OAAO,KAAK;EAChB;EACAgC,SAASA,CAACjF,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEmB,YAAY,EAAE;IAC/ClF,QAAQ,CAACmF,OAAO,CAACpB,MAAM,EAAEmB,YAAY,CAAC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIpN,OAAO,KAAKiM,MAAM,EAAE;MACpB;IACJ;IACA,IAAImB,YAAY,CAACE,MAAM,IAAI,WAAW,EAAE;MACpC,IAAI,CAACrC,kBAAkB,GAAGmC,YAAY,CAACG,SAAS;MAChD,IAAI,CAAC3B,qBAAqB,CAAC,CAAC;IAChC,CAAC,MACI,IAAIwB,YAAY,CAACE,MAAM,IAAI,WAAW,EAAE;MACzC,IAAI,CAACpC,kBAAkB,GAAGkC,YAAY,CAACI,SAAS;MAChD,IAAI,CAAC5B,qBAAqB,CAAC,CAAC;IAChC;EACJ;AACJ;AACA,SAAS6B,cAAcA,CAAC7O,IAAI,EAAE;EAC1B;EACA;EACAA,IAAI,CAAC,mBAAmB,CAAC,GAAGgM,iBAAiB;EAC7ChM,IAAI,CAACC,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IAClD;AACR;AACA;AACA;IACQH,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,SAASqJ,SAASA,CAACvE,EAAE,EAAE;MACnD;MACA;MACA,IAAIjG,MAAM,CAACc,OAAO,EAAE;QAChB;QACA,OAAO,UAAUwE,IAAI,EAAE;UACnB,IAAI,CAACA,IAAI,EAAE;YACP;YACA;YACAA,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;YACtBA,IAAI,CAACsJ,IAAI,GAAG,UAAUpD,CAAC,EAAE;cACrB,MAAMA,CAAC;YACX,CAAC;UACL;UACAtG,aAAa,CAACe,EAAE,EAAE,IAAI,EAAEX,IAAI,EAAGyB,GAAG,IAAK;YACnC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACzB,OAAOzB,IAAI,CAACsJ,IAAI,CAAC,IAAIhO,KAAK,CAACmG,GAAG,CAAC,CAAC;YACpC,CAAC,MACI;cACDzB,IAAI,CAACsJ,IAAI,CAAC7H,GAAG,CAAC;YAClB;UACJ,CAAC,CAAC;QACN,CAAC;MACL;MACA;MACA;MACA;MACA;MACA,OAAO,YAAY;QACf,OAAO,IAAIiG,OAAO,CAAC,CAAChB,cAAc,EAAEC,YAAY,KAAK;UACjD/G,aAAa,CAACe,EAAE,EAAE,IAAI,EAAE+F,cAAc,EAAEC,YAAY,CAAC;QACzD,CAAC,CAAC;MACN,CAAC;IACL,CAAC;IACD,SAAS/G,aAAaA,CAACe,EAAE,EAAEsB,OAAO,EAAEyE,cAAc,EAAEC,YAAY,EAAE;MAC9D,MAAM8B,WAAW,GAAGjO,IAAI,CAACoB,OAAO;MAChC,MAAM4K,iBAAiB,GAAGhM,IAAI,CAAC,mBAAmB,CAAC;MACnD,IAAIgM,iBAAiB,KAAK+C,SAAS,EAAE;QACjC,MAAM,IAAIjO,KAAK,CAAC,kFAAkF,GAC9F,4EAA4E,CAAC;MACrF;MACA,MAAMI,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;MAC3C,IAAI,CAACkB,aAAa,EAAE;QAChB,MAAM,IAAIJ,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;MAChF;MACA,MAAMgG,aAAa,GAAG5F,aAAa,CAACyC,GAAG,CAAC,CAAC;MACzCzC,aAAa,CAAC8N,aAAa,CAAC,CAAC;MAC7B;MACA;MACA,MAAMrH,SAAS,GAAG3H,IAAI,CAACoB,OAAO,CAAC6N,WAAW,CAAC,eAAe,CAAC;MAC3D,MAAMC,gBAAgB,GAAGpI,aAAa,CAACqI,WAAW,CAAC,CAAC;MACpDxH,SAAS,CAACN,MAAM,CAAClC,GAAG,CAAC,MAAM;QACvB,MAAMiK,YAAY,GAAG,IAAIpD,iBAAiB,CAAC,MAAM;UAC7C;UACA,IAAIlF,aAAa,CAACqI,WAAW,CAAC,CAAC,IAAIC,YAAY,EAAE;YAC7C;YACA;YACA;YACAtI,aAAa,CAACuI,WAAW,CAACH,gBAAgB,CAAC;UAC/C;UACAE,YAAY,CAACjC,qBAAqB,CAAC,CAAC;UACpCc,WAAW,CAAC9I,GAAG,CAAC,MAAM;YAClB+G,cAAc,CAAC,CAAC;UACpB,CAAC,CAAC;QACN,CAAC,EAAGtF,KAAK,IAAK;UACV;UACA,IAAIE,aAAa,CAACqI,WAAW,CAAC,CAAC,IAAIC,YAAY,EAAE;YAC7C;YACAtI,aAAa,CAACuI,WAAW,CAACH,gBAAgB,CAAC;UAC/C;UACAE,YAAY,CAACjC,qBAAqB,CAAC,CAAC;UACpCc,WAAW,CAAC9I,GAAG,CAAC,MAAM;YAClBgH,YAAY,CAACvF,KAAK,CAAC;UACvB,CAAC,CAAC;QACN,CAAC,EAAE,MAAM,CAAC;QACVE,aAAa,CAACuI,WAAW,CAACD,YAAY,CAAC;QACvCA,YAAY,CAACnC,mBAAmB,CAAC,CAAC;MACtC,CAAC,CAAC;MACF,OAAOjN,IAAI,CAACoB,OAAO,CAACkO,UAAU,CAACnJ,EAAE,EAAEsB,OAAO,CAAC;IAC/C;EACJ,CAAC,CAAC;AACN;AAEA,MAAM8H,QAAQ,GAAI,OAAOxD,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAM,OAAOxC,IAAI,KAAK,QAAQ,IAAIA,IAAK,IAAIqC,UAAU,CAAC1L,MAAM;AAClH,MAAMsP,YAAY,GAAGD,QAAQ,CAACvL,IAAI;AAClC;AACA;AACA;AACA;AACA,SAASyL,QAAQA,CAAA,EAAG;EAChB,IAAIlN,SAAS,CAACwB,MAAM,KAAK,CAAC,EAAE;IACxB,MAAM1D,CAAC,GAAG,IAAImP,YAAY,CAAC,CAAC;IAC5BnP,CAAC,CAACqP,OAAO,CAACD,QAAQ,CAACE,GAAG,CAAC,CAAC,CAAC;IACzB,OAAOtP,CAAC;EACZ,CAAC,MACI;IACD,MAAMiE,IAAI,GAAGC,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACX,SAAS,CAAC;IAClD,OAAO,IAAIiN,YAAY,CAAC,GAAGlL,IAAI,CAAC;EACpC;AACJ;AACAmL,QAAQ,CAACE,GAAG,GAAG,YAAY;EACvB,MAAMC,qBAAqB,GAAG5P,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;EACvE,IAAIiM,qBAAqB,EAAE;IACvB,OAAOA,qBAAqB,CAACC,iBAAiB,CAAC,CAAC;EACpD;EACA,OAAOL,YAAY,CAACG,GAAG,CAACrN,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAClD,CAAC;AACDkN,QAAQ,CAACK,GAAG,GAAGN,YAAY,CAACM,GAAG;AAC/BL,QAAQ,CAACM,KAAK,GAAGP,YAAY,CAACO,KAAK;AACnC;AACA,IAAIC,aAAa;AACjB,MAAMC,eAAe,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AACvC,MAAMC,SAAS,CAAC;EACZ;EACA;IAAS,IAAI,CAACC,YAAY,GAAG,CAAC;EAAE;EAChC;IAAS,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAAE;EAC3B1P,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC2P,eAAe,GAAG,EAAE;IACzB;IACA,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB;IACA,IAAI,CAACC,0BAA0B,GAAGf,YAAY,CAACG,GAAG,CAAC,CAAC;IACpD;IACA,IAAI,CAACa,kCAAkC,GAAG,EAAE;EAChD;EACA,OAAOC,SAASA,CAAA,EAAG;IACf,MAAMC,EAAE,GAAGV,aAAa,CAAC3J,gBAAgB,CAACnD,IAAI,CAACqM,QAAQ,EAAEU,eAAe,EAAE,CAAC,CAAC;IAC5ED,aAAa,CAAC1J,kBAAkB,CAACpD,IAAI,CAACqM,QAAQ,EAAEmB,EAAE,CAAC;IACnD,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MACxB,OAAOA,EAAE;IACb;IACA;IACA;IACA,OAAOR,SAAS,CAACC,YAAY,EAAE;EACnC;EACAQ,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACL,gBAAgB;EAChC;EACAT,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACU,0BAA0B,GAAG,IAAI,CAACD,gBAAgB;EAClE;EACArM,qBAAqBA,CAAC2M,kBAAkB,EAAE;IACtC,IAAI,CAACL,0BAA0B,GAAGK,kBAAkB;EACxD;EACApH,iBAAiBA,CAAA,EAAG;IAChB,OAAOgG,YAAY,CAACG,GAAG,CAAC,CAAC;EAC7B;EACAkB,gBAAgBA,CAACC,EAAE,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACjCA,OAAO,GAAG;MACN,GAAG;QACC1M,IAAI,EAAE,EAAE;QACR2M,UAAU,EAAE,KAAK;QACjBC,uBAAuB,EAAE,KAAK;QAC9BR,EAAE,EAAE,CAAC,CAAC;QACNS,iBAAiB,EAAE;MACvB,CAAC;MACD,GAAGH;IACP,CAAC;IACD,IAAII,SAAS,GAAGJ,OAAO,CAACN,EAAE,GAAG,CAAC,GAAGR,SAAS,CAACE,MAAM,GAAGY,OAAO,CAACN,EAAE;IAC9DR,SAAS,CAACE,MAAM,GAAGF,SAAS,CAACO,SAAS,CAAC,CAAC;IACxC,IAAIY,OAAO,GAAG,IAAI,CAACf,gBAAgB,GAAGS,KAAK;IAC3C;IACA,IAAIO,QAAQ,GAAG;MACXD,OAAO,EAAEA,OAAO;MAChBX,EAAE,EAAEU,SAAS;MACbG,IAAI,EAAET,EAAE;MACRxM,IAAI,EAAE0M,OAAO,CAAC1M,IAAI;MAClByM,KAAK,EAAEA,KAAK;MACZE,UAAU,EAAED,OAAO,CAACC,UAAU;MAC9BC,uBAAuB,EAAEF,OAAO,CAACE;IACrC,CAAC;IACD,IAAIF,OAAO,CAACG,iBAAiB,EAAE;MAC3B,IAAI,CAACX,kCAAkC,CAACgB,IAAI,CAACF,QAAQ,CAAC;IAC1D;IACA,IAAI3G,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAAC0F,eAAe,CAACtM,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACzC,IAAI8G,YAAY,GAAG,IAAI,CAACpB,eAAe,CAAC1F,CAAC,CAAC;MAC1C,IAAI2G,QAAQ,CAACD,OAAO,GAAGI,YAAY,CAACJ,OAAO,EAAE;QACzC;MACJ;IACJ;IACA,IAAI,CAAChB,eAAe,CAACqB,MAAM,CAAC/G,CAAC,EAAE,CAAC,EAAE2G,QAAQ,CAAC;IAC3C,OAAOF,SAAS;EACpB;EACAO,6BAA6BA,CAACjB,EAAE,EAAE;IAC9B,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC0F,eAAe,CAACtM,MAAM,EAAE4G,CAAC,EAAE,EAAE;MAClD,IAAI,IAAI,CAAC0F,eAAe,CAAC1F,CAAC,CAAC,CAAC+F,EAAE,IAAIA,EAAE,EAAE;QAClC,IAAI,CAACL,eAAe,CAACqB,MAAM,CAAC/G,CAAC,EAAE,CAAC,CAAC;QACjC;MACJ;IACJ;EACJ;EACAiH,SAASA,CAAA,EAAG;IACR,IAAI,CAACvB,eAAe,GAAG,EAAE;EAC7B;EACAvG,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACuG,eAAe,CAACtM,MAAM;EACtC;EACA6F,UAAUA,CAACiI,IAAI,GAAG,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAE;IACtC,IAAI,IAAI,CAAC1B,eAAe,CAACtM,MAAM,GAAG8N,IAAI,EAAE;MACpC;IACJ;IACA;IACA;IACA,MAAMG,SAAS,GAAG,IAAI,CAAC1B,gBAAgB;IACvC,MAAM2B,UAAU,GAAG,IAAI,CAAC5B,eAAe,CAACwB,IAAI,GAAG,CAAC,CAAC;IACjD,IAAI,CAACpO,IAAI,CAACwO,UAAU,CAACZ,OAAO,GAAGW,SAAS,EAAEF,MAAM,EAAEC,WAAW,CAAC;EAClE;EACAtO,IAAIA,CAACyO,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,EAAE;IAClC,IAAII,SAAS,GAAG,IAAI,CAAC7B,gBAAgB,GAAG4B,MAAM;IAC9C,IAAIE,eAAe,GAAG,CAAC;IACvBL,WAAW,GAAGnR,MAAM,CAACyR,MAAM,CAAC;MAAEC,iCAAiC,EAAE;IAAK,CAAC,EAAEP,WAAW,CAAC;IACrF;IACA;IACA;IACA,MAAMQ,cAAc,GAAGR,WAAW,CAACO,iCAAiC,GAC9D,IAAI,CAACjC,eAAe,GACpB,IAAI,CAACA,eAAe,CAAC7L,KAAK,CAAC,CAAC;IAClC,IAAI+N,cAAc,CAACxO,MAAM,KAAK,CAAC,IAAI+N,MAAM,EAAE;MACvCA,MAAM,CAACI,MAAM,CAAC;MACd;IACJ;IACA,OAAOK,cAAc,CAACxO,MAAM,GAAG,CAAC,EAAE;MAC9B;MACA,IAAI,CAACyM,kCAAkC,GAAG,EAAE;MAC5C,IAAIpP,OAAO,GAAGmR,cAAc,CAAC,CAAC,CAAC;MAC/B,IAAIJ,SAAS,GAAG/Q,OAAO,CAACiQ,OAAO,EAAE;QAC7B;QACA;MACJ,CAAC,MACI;QACD;QACA,IAAIjQ,OAAO,GAAGmR,cAAc,CAACC,KAAK,CAAC,CAAC;QACpC,IAAI,CAACT,WAAW,CAACO,iCAAiC,EAAE;UAChD,MAAMG,GAAG,GAAG,IAAI,CAACpC,eAAe,CAACqC,OAAO,CAACtR,OAAO,CAAC;UACjD,IAAIqR,GAAG,IAAI,CAAC,EAAE;YACV,IAAI,CAACpC,eAAe,CAACqB,MAAM,CAACe,GAAG,EAAE,CAAC,CAAC;UACvC;QACJ;QACAL,eAAe,GAAG,IAAI,CAAC9B,gBAAgB;QACvC,IAAI,CAACA,gBAAgB,GAAGlP,OAAO,CAACiQ,OAAO;QACvC,IAAIS,MAAM,EAAE;UACRA,MAAM,CAAC,IAAI,CAACxB,gBAAgB,GAAG8B,eAAe,CAAC;QACnD;QACA,IAAIO,MAAM,GAAGvR,OAAO,CAACmQ,IAAI,CAACjP,KAAK,CAACiN,QAAQ,EAAEnO,OAAO,CAAC8P,uBAAuB,GAAG,CAAC,IAAI,CAACZ,gBAAgB,CAAC,GAAGlP,OAAO,CAACkD,IAAI,CAAC;QACnH,IAAI,CAACqO,MAAM,EAAE;UACT;UACA;QACJ;QACA;QACA;QACA,IAAI,CAACZ,WAAW,CAACO,iCAAiC,EAAE;UAChD,IAAI,CAAC9B,kCAAkC,CAAC/N,OAAO,CAAE6O,QAAQ,IAAK;YAC1D,IAAI3G,CAAC,GAAG,CAAC;YACT,OAAOA,CAAC,GAAG4H,cAAc,CAACxO,MAAM,EAAE4G,CAAC,EAAE,EAAE;cACnC,MAAM8G,YAAY,GAAGc,cAAc,CAAC5H,CAAC,CAAC;cACtC,IAAI2G,QAAQ,CAACD,OAAO,GAAGI,YAAY,CAACJ,OAAO,EAAE;gBACzC;cACJ;YACJ;YACAkB,cAAc,CAACb,MAAM,CAAC/G,CAAC,EAAE,CAAC,EAAE2G,QAAQ,CAAC;UACzC,CAAC,CAAC;QACN;MACJ;IACJ;IACAc,eAAe,GAAG,IAAI,CAAC9B,gBAAgB;IACvC,IAAI,CAACA,gBAAgB,GAAG6B,SAAS;IACjC,IAAIL,MAAM,EAAE;MACRA,MAAM,CAAC,IAAI,CAACxB,gBAAgB,GAAG8B,eAAe,CAAC;IACnD;EACJ;EACAzI,sBAAsBA,CAACmI,MAAM,EAAE;IAC3B,IAAI,IAAI,CAACzB,eAAe,CAACtM,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,CAAC;IACZ;IACA;IACA;IACA,MAAMiO,SAAS,GAAG,IAAI,CAAC1B,gBAAgB;IACvC,MAAMsC,QAAQ,GAAG,IAAI,CAACvC,eAAe,CAAC,IAAI,CAACA,eAAe,CAACtM,MAAM,GAAG,CAAC,CAAC;IACtE,IAAI,CAACN,IAAI,CAACmP,QAAQ,CAACvB,OAAO,GAAGW,SAAS,EAAEF,MAAM,EAAE;MAAEQ,iCAAiC,EAAE;IAAM,CAAC,CAAC;IAC7F,OAAO,IAAI,CAAChC,gBAAgB,GAAG0B,SAAS;EAC5C;EACAtI,KAAKA,CAACmJ,KAAK,GAAG,EAAE,EAAEC,aAAa,GAAG,KAAK,EAAEhB,MAAM,EAAE;IAC7C,IAAIgB,aAAa,EAAE;MACf,OAAO,IAAI,CAACA,aAAa,CAAChB,MAAM,CAAC;IACrC,CAAC,MACI;MACD,OAAO,IAAI,CAACiB,gBAAgB,CAACF,KAAK,EAAEf,MAAM,CAAC;IAC/C;EACJ;EACAgB,aAAaA,CAAChB,MAAM,EAAE;IAClB,IAAI,IAAI,CAACzB,eAAe,CAACtM,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,CAAC;IACZ;IACA;IACA;IACA,MAAMiO,SAAS,GAAG,IAAI,CAAC1B,gBAAgB;IACvC,MAAMsC,QAAQ,GAAG,IAAI,CAACvC,eAAe,CAAC,IAAI,CAACA,eAAe,CAACtM,MAAM,GAAG,CAAC,CAAC;IACtE,IAAI,CAACN,IAAI,CAACmP,QAAQ,CAACvB,OAAO,GAAGW,SAAS,EAAEF,MAAM,CAAC;IAC/C,OAAO,IAAI,CAACxB,gBAAgB,GAAG0B,SAAS;EAC5C;EACAe,gBAAgBA,CAACF,KAAK,EAAEf,MAAM,EAAE;IAC5B,MAAME,SAAS,GAAG,IAAI,CAAC1B,gBAAgB;IACvC,IAAI8B,eAAe,GAAG,CAAC;IACvB,IAAIY,KAAK,GAAG,CAAC;IACb,OAAO,IAAI,CAAC3C,eAAe,CAACtM,MAAM,GAAG,CAAC,EAAE;MACpCiP,KAAK,EAAE;MACP,IAAIA,KAAK,GAAGH,KAAK,EAAE;QACf,MAAM,IAAI/R,KAAK,CAAC,2CAA2C,GACvD+R,KAAK,GACL,+CAA+C,CAAC;MACxD;MACA;MACA;MACA,IAAI,IAAI,CAACxC,eAAe,CAAC4C,MAAM,CAAE3F,IAAI,IAAK,CAACA,IAAI,CAAC2D,UAAU,IAAI,CAAC3D,IAAI,CAAC4D,uBAAuB,CAAC,CACvFnN,MAAM,KAAK,CAAC,EAAE;QACf;MACJ;MACA,MAAM3C,OAAO,GAAG,IAAI,CAACiP,eAAe,CAACmC,KAAK,CAAC,CAAC;MAC5CJ,eAAe,GAAG,IAAI,CAAC9B,gBAAgB;MACvC,IAAI,CAACA,gBAAgB,GAAGlP,OAAO,CAACiQ,OAAO;MACvC,IAAIS,MAAM,EAAE;QACR;QACAA,MAAM,CAAC,IAAI,CAACxB,gBAAgB,GAAG8B,eAAe,CAAC;MACnD;MACA,MAAMO,MAAM,GAAGvR,OAAO,CAACmQ,IAAI,CAACjP,KAAK,CAACiN,QAAQ,EAAEnO,OAAO,CAACkD,IAAI,CAAC;MACzD,IAAI,CAACqO,MAAM,EAAE;QACT;QACA;MACJ;IACJ;IACA,OAAO,IAAI,CAACrC,gBAAgB,GAAG0B,SAAS;EAC5C;AACJ;AACA,MAAM7N,qBAAqB,CAAC;EACxB,OAAO+O,YAAYA,CAAA,EAAG;IAClB,IAAIlT,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC,IAAI,IAAI,EAAE;MACnD,MAAM,IAAI7C,KAAK,CAAC,wEAAwE,CAAC;IAC7F;EACJ;EACAJ,WAAWA,CAAC0L,UAAU,EAAE+G,iCAAiC,GAAG,KAAK,EAAEC,gBAAgB,EAAE;IACjF,IAAI,CAACD,iCAAiC,GAAGA,iCAAiC;IAC1E,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,UAAU,GAAG,IAAInD,SAAS,CAAC,CAAC;IACjC,IAAI,CAACoD,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,sBAAsB,GAAGtG,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAC/E,IAAI,CAACmS,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC7G,UAAU,GAAG;MAAE,uBAAuB,EAAE;IAAK,CAAC;IACnD,IAAI,CAACxF,IAAI,GAAG,wBAAwB,GAAG8E,UAAU;IACjD;IACA;IACA,IAAI,CAAC,IAAI,CAACgH,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG7D,QAAQ,CAACvP,IAAI,CAACsB,UAAU,CAAC,wBAAwB,CAAC,CAAC;IAC/E;EACJ;EACAsS,WAAWA,CAACzN,EAAE,EAAE0N,UAAU,EAAE;IACxB,OAAO,CAAC,GAAGvP,IAAI,KAAK;MAChB6B,EAAE,CAAC7D,KAAK,CAACiN,QAAQ,EAAEjL,IAAI,CAAC;MACxB,IAAI,IAAI,CAACiP,UAAU,KAAK,IAAI,EAAE;QAC1B;QACA,IAAIM,UAAU,CAACC,SAAS,IAAI,IAAI,EAAE;UAC9BD,UAAU,CAACC,SAAS,CAACxR,KAAK,CAACiN,QAAQ,CAAC;QACxC;QACA;QACA,IAAI,CAAC9F,eAAe,CAAC,CAAC;MAC1B,CAAC,MACI;QACD;QACA,IAAIoK,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;UAC5BF,UAAU,CAACE,OAAO,CAACzR,KAAK,CAACiN,QAAQ,CAAC;QACtC;MACJ;MACA;MACA,OAAO,IAAI,CAACgE,UAAU,KAAK,IAAI;IACnC,CAAC;EACL;EACA,OAAOS,YAAYA,CAACC,MAAM,EAAEvD,EAAE,EAAE;IAC5B,IAAIwD,KAAK,GAAGD,MAAM,CAACvB,OAAO,CAAChC,EAAE,CAAC;IAC9B,IAAIwD,KAAK,GAAG,CAAC,CAAC,EAAE;MACZD,MAAM,CAACvC,MAAM,CAACwC,KAAK,EAAE,CAAC,CAAC;IAC3B;EACJ;EACAC,aAAaA,CAACzD,EAAE,EAAE;IACd,OAAO,MAAM;MACTvM,qBAAqB,CAAC6P,YAAY,CAAC,IAAI,CAACN,aAAa,EAAEhD,EAAE,CAAC;IAC9D,CAAC;EACL;EACA0D,qBAAqBA,CAACjO,EAAE,EAAEkO,QAAQ,EAAE/P,IAAI,EAAEoM,EAAE,EAAE;IAC1C,OAAO,MAAM;MACT;MACA,IAAI,IAAI,CAAC+C,qBAAqB,CAACf,OAAO,CAAChC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/C,IAAI,CAAC2C,UAAU,CAACxC,gBAAgB,CAAC1K,EAAE,EAAEkO,QAAQ,EAAE;UAC3C/P,IAAI;UACJ2M,UAAU,EAAE,IAAI;UAChBP,EAAE;UACFS,iBAAiB,EAAE;QACvB,CAAC,CAAC;MACN;IACJ,CAAC;EACL;EACAmD,qBAAqBA,CAAC5D,EAAE,EAAE;IACtB,OAAO,MAAM;MACTvM,qBAAqB,CAAC6P,YAAY,CAAC,IAAI,CAACP,qBAAqB,EAAE/C,EAAE,CAAC;IACtE,CAAC;EACL;EACA6D,WAAWA,CAACpO,EAAE,EAAE4K,KAAK,EAAEzM,IAAI,EAAEkQ,OAAO,GAAG,IAAI,EAAE;IACzC,IAAIC,aAAa,GAAG,IAAI,CAACN,aAAa,CAACjE,SAAS,CAACE,MAAM,CAAC;IACxD;IACA,IAAIU,EAAE,GAAG,IAAI,CAAC8C,WAAW,CAACzN,EAAE,EAAE;MAAE2N,SAAS,EAAEW,aAAa;MAAEV,OAAO,EAAEU;IAAc,CAAC,CAAC;IACnF,IAAI/D,EAAE,GAAG,IAAI,CAAC2C,UAAU,CAACxC,gBAAgB,CAACC,EAAE,EAAEC,KAAK,EAAE;MAAEzM,IAAI;MAAE4M,uBAAuB,EAAE,CAACsD;IAAQ,CAAC,CAAC;IACjG,IAAIA,OAAO,EAAE;MACT,IAAI,CAACd,aAAa,CAAClC,IAAI,CAACd,EAAE,CAAC;IAC/B;IACA,OAAOA,EAAE;EACb;EACAgE,aAAaA,CAAChE,EAAE,EAAE;IACdvM,qBAAqB,CAAC6P,YAAY,CAAC,IAAI,CAACN,aAAa,EAAEhD,EAAE,CAAC;IAC1D,IAAI,CAAC2C,UAAU,CAAC1B,6BAA6B,CAACjB,EAAE,CAAC;EACrD;EACAiE,YAAYA,CAACxO,EAAE,EAAEkO,QAAQ,EAAE/P,IAAI,EAAE;IAC7B,IAAIoM,EAAE,GAAGR,SAAS,CAACE,MAAM;IACzB,IAAIyD,UAAU,GAAG;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI,CAACO,qBAAqB,CAAC5D,EAAE;IAAE,CAAC;IAC7E,IAAII,EAAE,GAAG,IAAI,CAAC8C,WAAW,CAACzN,EAAE,EAAE0N,UAAU,CAAC;IACzC;IACAA,UAAU,CAACC,SAAS,GAAG,IAAI,CAACM,qBAAqB,CAACtD,EAAE,EAAEuD,QAAQ,EAAE/P,IAAI,EAAEoM,EAAE,CAAC;IACzE;IACA,IAAI,CAAC2C,UAAU,CAACxC,gBAAgB,CAACC,EAAE,EAAEuD,QAAQ,EAAE;MAAE/P,IAAI;MAAE2M,UAAU,EAAE;IAAK,CAAC,CAAC;IAC1E,IAAI,CAACwC,qBAAqB,CAACjC,IAAI,CAACd,EAAE,CAAC;IACnC,OAAOA,EAAE;EACb;EACAkE,cAAcA,CAAClE,EAAE,EAAE;IACfvM,qBAAqB,CAAC6P,YAAY,CAAC,IAAI,CAACP,qBAAqB,EAAE/C,EAAE,CAAC;IAClE,IAAI,CAAC2C,UAAU,CAAC1B,6BAA6B,CAACjB,EAAE,CAAC;EACrD;EACAmE,uBAAuBA,CAAA,EAAG;IACtB,IAAIjO,KAAK,GAAG,IAAI,CAAC2M,UAAU,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACA,sBAAsB,CAACzP,MAAM,GAAG,CAAC;IACtC,IAAI,CAACwP,UAAU,GAAG,IAAI;IACtB,MAAM3M,KAAK;EACf;EACA+J,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC0C,UAAU,CAAC1C,kBAAkB,CAAC,CAAC;EAC/C;EACAd,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACwD,UAAU,CAACxD,iBAAiB,CAAC,CAAC;EAC9C;EACA5L,qBAAqBA,CAAC6Q,QAAQ,EAAE;IAC5B,IAAI,CAACzB,UAAU,CAACpP,qBAAqB,CAAC6Q,QAAQ,CAAC;EACnD;EACAtL,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC6J,UAAU,CAAC7J,iBAAiB,CAAC,CAAC;EAC9C;EACA,OAAOuL,SAASA,CAAA,EAAG;IACf,IAAI,CAAC,CAACxF,QAAQ,CAACvP,IAAI,CAACsB,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE;MACpD;MACA;MACA;MACA;MACA;MACA;IACJ;IACA,IAAIiO,QAAQ,CAAC,MAAM,CAAC,KAAKE,QAAQ,EAAE;MAC/B;MACA;IACJ;IACAF,QAAQ,CAAC,MAAM,CAAC,GAAGE,QAAQ;IAC3BA,QAAQ,CAAC9O,SAAS,GAAG6O,YAAY,CAAC7O,SAAS;IAC3C;IACA;IACA;IACAwD,qBAAqB,CAAC6Q,eAAe,CAAC,CAAC;EAC3C;EACA,OAAOC,SAASA,CAAA,EAAG;IACf,IAAI1F,QAAQ,CAAC,MAAM,CAAC,KAAKE,QAAQ,EAAE;MAC/BF,QAAQ,CAAC,MAAM,CAAC,GAAGC,YAAY;IACnC;EACJ;EACA,OAAOwF,eAAeA,CAAA,EAAG;IACrB,IAAI,CAAChF,aAAa,EAAE;MAChB,MAAM,IAAIlP,KAAK,CAAC,uCAAuC,CAAC;IAC5D;IACA,IAAIyO,QAAQ,CAAChJ,UAAU,KAAKyJ,aAAa,CAACzJ,UAAU,EAAE;MAClDgJ,QAAQ,CAAChJ,UAAU,GAAGyJ,aAAa,CAACzJ,UAAU;MAC9CgJ,QAAQ,CAAC/I,YAAY,GAAGwJ,aAAa,CAACxJ,YAAY;IACtD;IACA,IAAI+I,QAAQ,CAAC2F,WAAW,KAAKlF,aAAa,CAACkF,WAAW,EAAE;MACpD3F,QAAQ,CAAC2F,WAAW,GAAGlF,aAAa,CAACkF,WAAW;MAChD3F,QAAQ,CAAC4F,aAAa,GAAGnF,aAAa,CAACmF,aAAa;IACxD;EACJ;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACzB,eAAe,GAAG,IAAI;IAC3BxP,qBAAqB,CAAC4Q,SAAS,CAAC,CAAC;EACrC;EACAM,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC1B,eAAe,GAAG,KAAK;IAC5BxP,qBAAqB,CAAC8Q,SAAS,CAAC,CAAC;EACrC;EACArL,UAAUA,CAAC0L,KAAK,GAAG,CAAC,EAAExD,MAAM,EAAEC,WAAW,GAAG;IAAEO,iCAAiC,EAAE;EAAK,CAAC,EAAE;IACrF,IAAIgD,KAAK,IAAI,CAAC,EAAE;MACZ;IACJ;IACAnR,qBAAqB,CAAC+O,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC4J,UAAU,CAACzJ,UAAU,CAAC0L,KAAK,EAAExD,MAAM,EAAEC,WAAW,CAAC;IACtD,IAAI,IAAI,CAACwB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACsB,uBAAuB,CAAC,CAAC;IAClC;EACJ;EACApR,IAAIA,CAACyO,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,GAAG;IAAEO,iCAAiC,EAAE;EAAK,CAAC,EAAE;IAChFnO,qBAAqB,CAAC+O,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC4J,UAAU,CAAC5P,IAAI,CAACyO,MAAM,EAAEJ,MAAM,EAAEC,WAAW,CAAC;IACjD,IAAI,IAAI,CAACwB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACsB,uBAAuB,CAAC,CAAC;IAClC;EACJ;EACApL,eAAeA,CAAA,EAAG;IACdtF,qBAAqB,CAAC+O,YAAY,CAAC,CAAC;IACpC,MAAMqC,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAI,IAAI,CAAChC,UAAU,KAAK,IAAI,IAAI,IAAI,CAACC,sBAAsB,CAACzP,MAAM,EAAE;QAChE;QACA,IAAI,CAAC8Q,uBAAuB,CAAC,CAAC;MAClC;IACJ,CAAC;IACD,OAAO,IAAI,CAACvB,WAAW,CAACvP,MAAM,GAAG,CAAC,EAAE;MAChC,IAAIyR,SAAS,GAAG,IAAI,CAAClC,WAAW,CAACd,KAAK,CAAC,CAAC;MACxCgD,SAAS,CAACjE,IAAI,CAACjP,KAAK,CAACkT,SAAS,CAACnI,MAAM,EAAEmI,SAAS,CAAClR,IAAI,CAAC;IAC1D;IACAiR,WAAW,CAAC,CAAC;EACjB;EACA7L,KAAKA,CAACmJ,KAAK,EAAEC,aAAa,EAAEhB,MAAM,EAAE;IAChC3N,qBAAqB,CAAC+O,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,MAAMgM,OAAO,GAAG,IAAI,CAACpC,UAAU,CAAC3J,KAAK,CAACmJ,KAAK,EAAEC,aAAa,EAAEhB,MAAM,CAAC;IACnE,IAAI,IAAI,CAACyB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACsB,uBAAuB,CAAC,CAAC;IAClC;IACA,OAAOY,OAAO;EAClB;EACA9L,sBAAsBA,CAACmI,MAAM,EAAE;IAC3B3N,qBAAqB,CAAC+O,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,MAAMgM,OAAO,GAAG,IAAI,CAACpC,UAAU,CAAC1J,sBAAsB,CAACmI,MAAM,CAAC;IAC9D,IAAI,IAAI,CAACyB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACsB,uBAAuB,CAAC,CAAC;IAClC;IACA,OAAOY,OAAO;EAClB;EACA5L,eAAeA,CAAA,EAAG;IACd1F,qBAAqB,CAAC+O,YAAY,CAAC,CAAC;IACpC,IAAI,CAACG,UAAU,CAACzB,SAAS,CAAC,CAAC;IAC3B,IAAI,CAAC6B,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,aAAa,GAAG,EAAE;EAC3B;EACA5J,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACuJ,UAAU,CAACvJ,aAAa,CAAC,CAAC,GAAG,IAAI,CAACwJ,WAAW,CAACvP,MAAM;EACpE;EACAqJ,cAAcA,CAAC9D,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEC,IAAI,EAAE;IAC5C,QAAQA,IAAI,CAACC,IAAI;MACb,KAAK,WAAW;QACZ,IAAIjJ,IAAI,GAAGgJ,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAAClJ,IAAI;QACtC;QACA;QACA;QACA,IAAIoR,cAAc;QAClB,IAAIpR,IAAI,EAAE;UACN,IAAIqR,aAAa,GAAGrI,IAAI,CAACE,IAAI,CAACoI,KAAK;UACnC,IAAI,OAAOtR,IAAI,CAACP,MAAM,KAAK,QAAQ,IAAIO,IAAI,CAACP,MAAM,GAAG4R,aAAa,GAAG,CAAC,EAAE;YACpED,cAAc,GAAGnR,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACoB,IAAI,EAAEqR,aAAa,GAAG,CAAC,CAAC;UACxE;QACJ;QACA,IAAI,CAACrC,WAAW,CAAC9B,IAAI,CAAC;UAClBD,IAAI,EAAEjE,IAAI,CAACc,MAAM;UACjB9J,IAAI,EAAEoR,cAAc;UACpBrI,MAAM,EAAEC,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACH;QACnC,CAAC,CAAC;QACF;MACJ,KAAK,WAAW;QACZ,QAAQC,IAAI,CAACa,MAAM;UACf,KAAK,YAAY;YACbb,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+G,WAAW,CAACjH,IAAI,CAACc,MAAM,EAAEd,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC,EAAEjJ,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACoK,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3H;UACJ,KAAK,cAAc;YACfF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+G,WAAW,CAACjH,IAAI,CAACc,MAAM,EAAE,CAAC,EAAE7J,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACoK,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1G;UACJ,KAAK,aAAa;YACdF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAACmH,YAAY,CAACrH,IAAI,CAACc,MAAM,EAAEd,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC,EAAEjJ,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACoK,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5H;UACJ,KAAK,qBAAqB;YACtB,MAAM,IAAI1M,KAAK,CAAC,+DAA+D,GAC3EwM,IAAI,CAACE,IAAI,CAAC,KAAK,CAAC,CAAC;UACzB,KAAK,uBAAuB;UAC5B,KAAK,6BAA6B;UAClC,KAAK,0BAA0B;YAC3B;YACA;YACAF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+G,WAAW,CAACjH,IAAI,CAACc,MAAM,EAAE,EAAE,EAAEd,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC2F,iCAAiC,CAAC;YACpH;UACJ;YACI;YACA;YACA,MAAM0C,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACxI,IAAI,CAAC;YACtD,IAAIuI,eAAe,EAAE;cACjB,MAAMvR,IAAI,GAAGgJ,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC;cAC3C,MAAMuD,KAAK,GAAGzM,IAAI,IAAIA,IAAI,CAACP,MAAM,GAAG,CAAC,GAAGO,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;cACnD,IAAIyR,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGzR,IAAI;cACrF,IAAI,CAAC,CAACuR,eAAe,CAAC5E,UAAU,EAAE;gBAC9B;gBACA3D,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAACmH,YAAY,CAACrH,IAAI,CAACc,MAAM,EAAE2C,KAAK,EAAEgF,YAAY,CAAC;gBAC3EzI,IAAI,CAACE,IAAI,CAACyD,UAAU,GAAG,IAAI;cAC/B,CAAC,MACI;gBACD;gBACA3D,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+G,WAAW,CAACjH,IAAI,CAACc,MAAM,EAAE2C,KAAK,EAAEgF,YAAY,CAAC;cAC9E;cACA;YACJ;YACA,MAAM,IAAIjV,KAAK,CAAC,kDAAkD,GAAGwM,IAAI,CAACa,MAAM,CAAC;QACzF;QACA;MACJ,KAAK,WAAW;QACZb,IAAI,GAAGhE,QAAQ,CAACmE,YAAY,CAACJ,MAAM,EAAEC,IAAI,CAAC;QAC1C;IACR;IACA,OAAOA,IAAI;EACf;EACAO,YAAYA,CAACvE,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEC,IAAI,EAAE;IAC1C,QAAQA,IAAI,CAACa,MAAM;MACf,KAAK,YAAY;MACjB,KAAK,uBAAuB;MAC5B,KAAK,6BAA6B;MAClC,KAAK,0BAA0B;QAC3B,OAAO,IAAI,CAACuG,aAAa,CAACpH,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC;MACpD,KAAK,aAAa;QACd,OAAO,IAAI,CAACoH,cAAc,CAACtH,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC;MACrD;QACI;QACA;QACA,MAAMqI,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACxI,IAAI,CAAC;QACtD,IAAIuI,eAAe,EAAE;UACjB,MAAMG,QAAQ,GAAG1I,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC;UACtC,OAAOqI,eAAe,CAAC5E,UAAU,GAC3B,IAAI,CAAC2D,cAAc,CAACoB,QAAQ,CAAC,GAC7B,IAAI,CAACtB,aAAa,CAACsB,QAAQ,CAAC;QACtC;QACA,OAAO1M,QAAQ,CAACwE,UAAU,CAACT,MAAM,EAAEC,IAAI,CAAC;IAChD;EACJ;EACAS,QAAQA,CAACzE,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAE4I,QAAQ,EAAE3Q,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,EAAE;IACxE,IAAI;MACAhK,qBAAqB,CAAC4Q,SAAS,CAAC,CAAC;MACjC,OAAOzL,QAAQ,CAAC8E,MAAM,CAACf,MAAM,EAAE4I,QAAQ,EAAE3Q,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IAC1E,CAAC,SACO;MACJ,IAAI,CAAC,IAAI,CAACwF,eAAe,EAAE;QACvBxP,qBAAqB,CAAC8Q,SAAS,CAAC,CAAC;MACrC;IACJ;EACJ;EACAa,mBAAmBA,CAACxI,IAAI,EAAE;IACtB,IAAI,CAAC,IAAI,CAAC8F,gBAAgB,EAAE;MACxB,OAAO,IAAI;IACf;IACA,KAAK,IAAIzI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyI,gBAAgB,CAACrP,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACnD,MAAMkL,eAAe,GAAG,IAAI,CAACzC,gBAAgB,CAACzI,CAAC,CAAC;MAChD,IAAIkL,eAAe,CAAC1H,MAAM,KAAKb,IAAI,CAACa,MAAM,EAAE;QACxC,OAAO0H,eAAe;MAC1B;IACJ;IACA,OAAO,IAAI;EACf;EACAxH,aAAaA,CAACL,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;IAC9D,IAAI,CAAC2M,UAAU,GAAG3M,KAAK;IACvB,OAAO,KAAK,CAAC,CAAC;EAClB;AACJ;AACA,IAAIsP,sBAAsB,GAAG,IAAI;AACjC,SAASC,gBAAgBA,CAAA,EAAG;EACxB,OAAOnW,IAAI,IAAIA,IAAI,CAAC,eAAe,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoW,kBAAkBA,CAAA,EAAG;EAC1B,IAAIF,sBAAsB,EAAE;IACxBA,sBAAsB,CAACb,eAAe,CAAC,CAAC;EAC5C;EACAa,sBAAsB,GAAG,IAAI;EAC7B;EACAC,gBAAgB,CAAC,CAAC,IAAIA,gBAAgB,CAAC,CAAC,CAACnH,aAAa,CAAC,CAAC,CAACqH,aAAa,CAAC,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxQ,SAASA,CAACM,EAAE,EAAE6K,OAAO,GAAG,CAAC,CAAC,EAAE;EACjC,MAAM;IAAEtH,KAAK,GAAG;EAAM,CAAC,GAAGsH,OAAO;EACjC;EACA,MAAMsF,WAAW,GAAG,SAAAA,CAAU,GAAGhS,IAAI,EAAE;IACnC,MAAMpD,aAAa,GAAGiV,gBAAgB,CAAC,CAAC;IACxC,IAAI,CAACjV,aAAa,EAAE;MAChB,MAAM,IAAIJ,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;IAChF;IACA,MAAMgG,aAAa,GAAG5F,aAAa,CAAC8N,aAAa,CAAC,CAAC;IACnD,IAAIhP,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC,EAAE;MAC3C,MAAM,IAAI7C,KAAK,CAAC,qCAAqC,CAAC;IAC1D;IACA,IAAI;MACA;MACA,IAAI,CAACoV,sBAAsB,EAAE;QACzB,MAAM/R,qBAAqB,GAAGnE,IAAI,IAAIA,IAAI,CAAC,uBAAuB,CAAC;QACnE,IAAI8G,aAAa,CAACqI,WAAW,CAAC,CAAC,YAAYhL,qBAAqB,EAAE;UAC9D,MAAM,IAAIrD,KAAK,CAAC,qCAAqC,CAAC;QAC1D;QACAoV,sBAAsB,GAAG,IAAI/R,qBAAqB,CAAC,CAAC;MACxD;MACA,IAAIoS,GAAG;MACP,MAAMC,iBAAiB,GAAG1P,aAAa,CAACqI,WAAW,CAAC,CAAC;MACrDrI,aAAa,CAACuI,WAAW,CAAC6G,sBAAsB,CAAC;MACjDA,sBAAsB,CAACd,aAAa,CAAC,CAAC;MACtC,IAAI;QACAmB,GAAG,GAAGpQ,EAAE,CAAC7D,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QAC1B,IAAIoF,KAAK,EAAE;UACPwM,sBAAsB,CAACxM,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC;QAC1C,CAAC,MACI;UACDD,eAAe,CAAC,CAAC;QACrB;MACJ,CAAC,SACO;QACJ3C,aAAa,CAACuI,WAAW,CAACmH,iBAAiB,CAAC;MAChD;MACA,IAAI,CAAC9M,KAAK,EAAE;QACR,IAAIwM,sBAAsB,CAACzC,qBAAqB,CAAC1P,MAAM,GAAG,CAAC,EAAE;UACzD,MAAM,IAAIjD,KAAK,CAAC,GAAGoV,sBAAsB,CAACzC,qBAAqB,CAAC1P,MAAM,GAAG,GACrE,uCAAuC,CAAC;QAChD;QACA,IAAImS,sBAAsB,CAACxC,aAAa,CAAC3P,MAAM,GAAG,CAAC,EAAE;UACjD,MAAM,IAAIjD,KAAK,CAAC,GAAGoV,sBAAsB,CAACxC,aAAa,CAAC3P,MAAM,+BAA+B,CAAC;QAClG;MACJ;MACA,OAAOwS,GAAG;IACd,CAAC,SACO;MACJH,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC;EACDE,WAAW,CAACnO,WAAW,GAAG,IAAI;EAC9B,OAAOmO,WAAW;AACtB;AACA,SAASG,qBAAqBA,CAAA,EAAG;EAC7B,IAAIP,sBAAsB,IAAI,IAAI,EAAE;IAChCA,sBAAsB,GAAGlW,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;IAClE,IAAIuS,sBAAsB,IAAI,IAAI,EAAE;MAChC,MAAM,IAAIpV,KAAK,CAAC,wEAAwE,CAAC;IAC7F;EACJ;EACA,OAAOoV,sBAAsB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzS,IAAIA,CAACyO,MAAM,GAAG,CAAC,EAAEwE,mBAAmB,GAAG,KAAK,EAAE;EACnDD,qBAAqB,CAAC,CAAC,CAAChT,IAAI,CAACyO,MAAM,EAAE,IAAI,EAAEwE,mBAAmB,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShN,KAAKA,CAACiN,QAAQ,EAAE;EACrB,OAAOF,qBAAqB,CAAC,CAAC,CAAC/M,KAAK,CAACiN,QAAQ,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,QAAQ,GAAGJ,qBAAqB,CAAC,CAAC;EACxCI,QAAQ,CAACpD,qBAAqB;EAC9BoD,QAAQ,CAACpD,qBAAqB,CAAC1P,MAAM,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0F,eAAeA,CAAA,EAAG;EACvBgN,qBAAqB,CAAC,CAAC,CAAChN,eAAe,CAAC,CAAC;AAC7C;AACA,SAASqN,kBAAkBA,CAAC9W,IAAI,EAAE;EAC9B;EACA;EACAA,IAAI,CAAC,uBAAuB,CAAC,GAAGmE,qBAAqB;EACrDnE,IAAI,CAACC,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IAClDH,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG;MAChC+U,kBAAkB;MAClB3M,eAAe;MACfmN,oBAAoB;MACpBnT,IAAI;MACJiG,KAAK;MACL7D;IACJ,CAAC;EACL,CAAC,EAAE,IAAI,CAAC;EACRmK,aAAa,GAAG;IACZzJ,UAAU,EAAEgJ,QAAQ,CAAChJ,UAAU;IAC/B2O,WAAW,EAAE3F,QAAQ,CAAC2F,WAAW;IACjC1O,YAAY,EAAE+I,QAAQ,CAAC/I,YAAY;IACnC2O,aAAa,EAAE5F,QAAQ,CAAC4F,aAAa;IACrC9O,gBAAgB,EAAEkJ,QAAQ,CAACvP,IAAI,CAACsB,UAAU,CAAC,YAAY,CAAC,CAAC;IACzDgF,kBAAkB,EAAEiJ,QAAQ,CAACvP,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC;EAChE,CAAC;EACD4O,SAAS,CAACE,MAAM,GAAGF,SAAS,CAACO,SAAS,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,SAASsG,mBAAmBA,CAAC/W,IAAI,EAAE;EAC/B,MAAMgX,OAAO,GAAG,IAAI;EACpB,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,aAAa,GAAG,mBAAmB;EACzC,MAAMC,SAAS,GAAG,qBAAqB;EACvC,MAAMC,OAAO,GAAG,aAAa;EAC7B,IAAIC,WAAW,GAAGD,OAAO,GAAG,WAAW;EACvC,MAAME,cAAc,CAAC;IACjB5W,WAAWA,CAAA,EAAG;MACV,IAAI,CAACkG,KAAK,GAAG2Q,aAAa,CAAC,CAAC;MAC5B,IAAI,CAACC,SAAS,GAAG,IAAIxT,IAAI,CAAC,CAAC;IAC/B;EACJ;EACA,SAASyT,8BAA8BA,CAAA,EAAG;IACtC,OAAO,IAAI3W,KAAK,CAACqW,SAAS,CAAC;EAC/B;EACA,SAASO,4BAA4BA,CAAA,EAAG;IACpC,IAAI;MACA,MAAMD,8BAA8B,CAAC,CAAC;IAC1C,CAAC,CACD,OAAOxQ,GAAG,EAAE;MACR,OAAOA,GAAG;IACd;EACJ;EACA;EACA;EACA,MAAML,KAAK,GAAG6Q,8BAA8B,CAAC,CAAC;EAC9C,MAAME,WAAW,GAAGD,4BAA4B,CAAC,CAAC;EAClD,MAAMH,aAAa,GAAG3Q,KAAK,CAACgR,KAAK,GAC3BH,8BAA8B,GAC9BE,WAAW,CAACC,KAAK,GACbF,4BAA4B,GAC5BD,8BAA8B;EACxC,SAASI,SAASA,CAACjR,KAAK,EAAE;IACtB,OAAOA,KAAK,CAACgR,KAAK,GAAGhR,KAAK,CAACgR,KAAK,CAACE,KAAK,CAACd,OAAO,CAAC,GAAG,EAAE;EACxD;EACA,SAASe,aAAaA,CAACC,KAAK,EAAEpR,KAAK,EAAE;IACjC,IAAIqR,KAAK,GAAGJ,SAAS,CAACjR,KAAK,CAAC;IAC5B,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsN,KAAK,CAAClU,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACnC,MAAMuN,KAAK,GAAGD,KAAK,CAACtN,CAAC,CAAC;MACtB;MACA,IAAI,CAACsM,aAAa,CAACzW,cAAc,CAAC0X,KAAK,CAAC,EAAE;QACtCF,KAAK,CAACxG,IAAI,CAACyG,KAAK,CAACtN,CAAC,CAAC,CAAC;MACxB;IACJ;EACJ;EACA,SAASwN,oBAAoBA,CAACC,MAAM,EAAER,KAAK,EAAE;IACzC,MAAMS,SAAS,GAAG,CAACT,KAAK,GAAGA,KAAK,CAACU,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IAC7C,IAAIF,MAAM,EAAE;MACR,IAAIZ,SAAS,GAAG,IAAIxT,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC;MACpC,KAAK,IAAIyG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyN,MAAM,CAACrU,MAAM,EAAE4G,CAAC,EAAE,EAAE;QACpC,MAAM4N,WAAW,GAAGH,MAAM,CAACzN,CAAC,CAAC;QAC7B,MAAM6N,QAAQ,GAAGD,WAAW,CAACf,SAAS;QACtC,IAAIiB,SAAS,GAAG,+BAA+BjB,SAAS,GAAGgB,QAAQ,CAACtU,OAAO,CAAC,CAAC,YAAYsU,QAAQ,EAAE;QACnGC,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;QAC9CL,SAAS,CAAC7G,IAAI,CAAC6F,WAAW,CAACqB,OAAO,CAACtB,OAAO,EAAEqB,SAAS,CAAC,CAAC;QACvDV,aAAa,CAACM,SAAS,EAAEE,WAAW,CAAC3R,KAAK,CAAC;QAC3C4Q,SAAS,GAAGgB,QAAQ,CAACtU,OAAO,CAAC,CAAC;MAClC;IACJ;IACA,OAAOmU,SAAS,CAACM,IAAI,CAAC3B,OAAO,CAAC;EAClC;EACA;EACA;EACA;EACA;EACA,SAAS4B,kBAAkBA,CAAA,EAAG;IAC1B;IACA;IACA,OAAO9X,KAAK,CAAC+X,eAAe,GAAG,CAAC;EACpC;EACA7Y,IAAI,CAAC,wBAAwB,CAAC,GAAG;IAC7BsH,IAAI,EAAE,kBAAkB;IACxBwR,mBAAmB,EAAE,EAAE;IAAE;IACzB;IACA;IACAC,iBAAiB,EAAE,SAAAA,CAAUnS,KAAK,EAAE;MAChC,IAAI,CAACA,KAAK,EAAE;QACR,OAAOmI,SAAS;MACpB;MACA,MAAMkJ,KAAK,GAAGrR,KAAK,CAAC5G,IAAI,CAACsB,UAAU,CAAC,kBAAkB,CAAC,CAAC;MACxD,IAAI,CAAC2W,KAAK,EAAE;QACR,OAAOrR,KAAK,CAACgR,KAAK;MACtB;MACA,OAAOO,oBAAoB,CAACF,KAAK,EAAErR,KAAK,CAACgR,KAAK,CAAC;IACnD,CAAC;IACDxK,cAAc,EAAE,SAAAA,CAAUY,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,EAAE;MACzE,IAAIsL,kBAAkB,CAAC,CAAC,EAAE;QACtB,MAAMrR,WAAW,GAAGvH,IAAI,CAACuH,WAAW;QACpC,IAAI0Q,KAAK,GAAI1Q,WAAW,IAAIA,WAAW,CAACiG,IAAI,IAAIjG,WAAW,CAACiG,IAAI,CAAC0J,aAAa,CAAC,IAAK,EAAE;QACtFe,KAAK,GAAG,CAAC,IAAIX,cAAc,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAACf,KAAK,CAAC;QAC5C,IAAIA,KAAK,CAAClU,MAAM,GAAG,IAAI,CAAC+U,mBAAmB,EAAE;UACzCb,KAAK,CAAClU,MAAM,GAAG,IAAI,CAAC+U,mBAAmB;QAC3C;QACA,IAAI,CAACxL,IAAI,CAACE,IAAI,EACVF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC;QAClB,IAAIF,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACAD,IAAI,CAACE,IAAI,GAAG;YAAE,GAAGF,IAAI,CAACE;UAAK,CAAC;QAChC;QACAF,IAAI,CAACE,IAAI,CAAC0J,aAAa,CAAC,GAAGe,KAAK;MACpC;MACA,OAAOjK,kBAAkB,CAACP,YAAY,CAACS,UAAU,EAAEZ,IAAI,CAAC;IAC5D,CAAC;IACDe,aAAa,EAAE,SAAAA,CAAUL,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;MACzE,IAAIgS,kBAAkB,CAAC,CAAC,EAAE;QACtB,MAAMK,UAAU,GAAGjZ,IAAI,CAACuH,WAAW,IAAIX,KAAK,CAAC0G,IAAI;QACjD,IAAI1G,KAAK,YAAY9F,KAAK,IAAImY,UAAU,EAAE;UACtC,MAAMC,SAAS,GAAGf,oBAAoB,CAACc,UAAU,CAACzL,IAAI,IAAIyL,UAAU,CAACzL,IAAI,CAAC0J,aAAa,CAAC,EAAEtQ,KAAK,CAACgR,KAAK,CAAC;UACtG,IAAI;YACAhR,KAAK,CAACgR,KAAK,GAAGhR,KAAK,CAACsS,SAAS,GAAGA,SAAS;UAC7C,CAAC,CACD,OAAOjS,GAAG,EAAE,CAAE;QAClB;MACJ;MACA,OAAO+G,kBAAkB,CAACM,WAAW,CAACJ,UAAU,EAAEtH,KAAK,CAAC;IAC5D;EACJ,CAAC;EACD,SAASuS,kBAAkBA,CAACC,WAAW,EAAEpG,KAAK,EAAE;IAC5C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACXoG,WAAW,CAAC5H,IAAI,CAACqG,SAAS,CAAC,IAAIP,cAAc,CAAC,CAAC,CAAC1Q,KAAK,CAAC,CAAC;MACvDuS,kBAAkB,CAACC,WAAW,EAAEpG,KAAK,GAAG,CAAC,CAAC;IAC9C;EACJ;EACA,SAASqG,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACT,kBAAkB,CAAC,CAAC,EAAE;MACvB;IACJ;IACA,MAAMR,MAAM,GAAG,EAAE;IACjBe,kBAAkB,CAACf,MAAM,EAAE,CAAC,CAAC;IAC7B,MAAMkB,OAAO,GAAGlB,MAAM,CAAC,CAAC,CAAC;IACzB,MAAMmB,OAAO,GAAGnB,MAAM,CAAC,CAAC,CAAC;IACzB,KAAK,IAAIzN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2O,OAAO,CAACvV,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACrC,MAAM6O,MAAM,GAAGF,OAAO,CAAC3O,CAAC,CAAC;MACzB,IAAI6O,MAAM,CAAC9G,OAAO,CAACyE,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;QACjC,IAAIsC,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,WAAW,CAAC;QACrC,IAAIA,KAAK,EAAE;UACPpC,WAAW,GAAGoC,KAAK,CAAC,CAAC,CAAC,GAAGrC,OAAO,GAAG,qBAAqB;UACxD;QACJ;MACJ;IACJ;IACA,KAAK,IAAIzM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2O,OAAO,CAACvV,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACrC,MAAM6O,MAAM,GAAGF,OAAO,CAAC3O,CAAC,CAAC;MACzB,MAAM+O,MAAM,GAAGH,OAAO,CAAC5O,CAAC,CAAC;MACzB,IAAI6O,MAAM,KAAKE,MAAM,EAAE;QACnBzC,aAAa,CAACuC,MAAM,CAAC,GAAG,IAAI;MAChC,CAAC,MACI;QACD;MACJ;IACJ;EACJ;EACAH,mBAAmB,CAAC,CAAC;AACzB;AAEA,MAAMnY,aAAa,CAAC;EAChB,OAAOyC,GAAGA,CAAA,EAAG;IACT,OAAO3D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,eAAe,CAAC;EAC5C;EACA,OAAOgW,QAAQA,CAAA,EAAG;IACd,OAAOzY,aAAa,CAACyC,GAAG,CAAC,CAAC,YAAYzC,aAAa;EACvD;EACA,OAAO8N,aAAaA,CAAA,EAAG;IACnB,IAAI,CAAC9N,aAAa,CAACyY,QAAQ,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAI7Y,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACA,OAAOI,aAAa,CAACyC,GAAG,CAAC,CAAC;EAC9B;EACAjD,WAAWA,CAACkZ,mBAAmB,GAAG,IAAI,EAAE;IACpC,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACtS,IAAI,GAAG,WAAW;IACvB,IAAI,CAACuS,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC/M,UAAU,GAAG;MAAE,eAAe,EAAE;IAAK,CAAC;IAC3C,IAAI,CAACgN,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAAC5K,WAAW,CAACuK,mBAAmB,CAAC;EACzC;EACAvK,WAAWA,CAAC6K,YAAY,EAAE;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACN,aAAa,KAAKK,YAAY;IACzD,IAAI,CAACL,aAAa,GAAGK,YAAY;IACjC,IAAI,CAACJ,YAAY,IAAI,IAAI,CAACA,YAAY,CAACrX,OAAO,CAAE2X,GAAG,IAAK,OAAO,IAAI,CAACtN,UAAU,CAACsN,GAAG,CAAC,CAAC;IACpF,IAAI,CAACN,YAAY,GAAG,IAAI;IACxB,IAAII,YAAY,IAAIA,YAAY,CAACpN,UAAU,EAAE;MACzC,IAAI,CAACgN,YAAY,GAAGlZ,MAAM,CAACyZ,IAAI,CAACH,YAAY,CAACpN,UAAU,CAAC;MACxD,IAAI,CAACgN,YAAY,CAACrX,OAAO,CAAE6X,CAAC,IAAM,IAAI,CAACxN,UAAU,CAACwN,CAAC,CAAC,GAAGJ,YAAY,CAACpN,UAAU,CAACwN,CAAC,CAAE,CAAC;IACvF;IACA;IACA,IAAIH,aAAa,IACb,IAAI,CAACJ,aAAa,KACjB,IAAI,CAACA,aAAa,CAACnL,SAAS,IAAI,IAAI,CAACmL,aAAa,CAACpL,SAAS,CAAC,EAAE;MAChE,IAAI,CAACqL,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACA7K,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC0K,aAAa;EAC7B;EACAxD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAClH,WAAW,CAAC,CAAC;IAClB,IAAI,CAACE,WAAW,CAAC,IAAI,CAACuK,mBAAmB,CAAC;EAC9C;EACAW,iBAAiBA,CAACvM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE;IAC3D,IAAI,IAAI,CAAC8L,sBAAsB,IAAI,IAAI,CAACD,aAAa,EAAE;MACnD;MACA;MACA,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACzL,SAAS,CAACP,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE,IAAI,CAAC6L,aAAa,CAAC;IACnF;EACJ;EACAS,eAAeA,CAAClN,IAAI,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC2M,KAAK,EAAE;MACb;IACJ;IACA,KAAK,IAAItP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsP,KAAK,CAAClW,MAAM,EAAE4G,CAAC,EAAE,EAAE;MACxC,IAAI,IAAI,CAACsP,KAAK,CAACtP,CAAC,CAAC,KAAK2C,IAAI,EAAE;QACxB,IAAI,CAAC2M,KAAK,CAACvI,MAAM,CAAC/G,CAAC,EAAE,CAAC,CAAC;QACvB;MACJ;IACJ;EACJ;EACA3D,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACiT,KAAK,CAAClW,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO,EAAE;IACb;IACA,MAAM0W,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACS,GAAG,CAAEpN,IAAI,IAAK;MACtC,MAAMqN,QAAQ,GAAGrN,IAAI,CAACE,IAAI,IACtB5M,MAAM,CAACyZ,IAAI,CAAC/M,IAAI,CAACE,IAAI,CAAC,CACjBkN,GAAG,CAAEN,GAAG,IAAK;QACd,OAAOA,GAAG,GAAG,GAAG,GAAG9M,IAAI,CAACE,IAAI,CAAC4M,GAAG,CAAC;MACrC,CAAC,CAAC,CACGzB,IAAI,CAAC,GAAG,CAAC;MAClB,OAAO,SAASrL,IAAI,CAACC,IAAI,aAAaD,IAAI,CAACa,MAAM,YAAYwM,QAAQ,GAAG;IAC5E,CAAC,CAAC;IACF,MAAM5T,gBAAgB,GAAG,8BAA8B,GAAG0T,QAAQ,GAAG,GAAG;IACxE;IACA,IAAI,CAACR,KAAK,GAAG,EAAE;IACf,OAAOlT,gBAAgB;EAC3B;EACA6T,MAAMA,CAAC5M,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2I,QAAQ,EAAE;IAC1D,IAAI,IAAI,CAACgD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACe,MAAM,EAAE;MACjD,OAAO,IAAI,CAACf,aAAa,CAACe,MAAM,CAAC5M,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2I,QAAQ,CAAC;IAC3F,CAAC,MACI;MACD,OAAO7I,kBAAkB,CAAC9I,IAAI,CAACgJ,UAAU,EAAE2I,QAAQ,CAAC;IACxD;EACJ;EACAgE,WAAWA,CAAC7M,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE5E,QAAQ,EAAE6E,MAAM,EAAE;IACvE,IAAI,IAAI,CAAC0L,aAAa,IAAI,IAAI,CAACA,aAAa,CAACgB,WAAW,EAAE;MACtD,OAAO,IAAI,CAAChB,aAAa,CAACgB,WAAW,CAAC7M,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE5E,QAAQ,EAAE6E,MAAM,CAAC;IACxG,CAAC,MACI;MACD,OAAOH,kBAAkB,CAAC8M,SAAS,CAAC5M,UAAU,EAAE5E,QAAQ,EAAE6E,MAAM,CAAC;IACrE;EACJ;EACAJ,QAAQA,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE5E,QAAQ,EAAEhE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,EAAE;IAC1F,IAAI,CAACoM,iBAAiB,CAACvM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAAC2L,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC9L,QAAQ,EAAE;MACnD,OAAO,IAAI,CAAC8L,aAAa,CAAC9L,QAAQ,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE5E,QAAQ,EAAEhE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IAC3H,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACI,MAAM,CAACF,UAAU,EAAE5E,QAAQ,EAAEhE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IACxF;EACJ;EACAE,aAAaA,CAACL,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;IAC9D,IAAI,IAAI,CAACiT,aAAa,IAAI,IAAI,CAACA,aAAa,CAACxL,aAAa,EAAE;MACxD,OAAO,IAAI,CAACwL,aAAa,CAACxL,aAAa,CAACL,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,CAAC;IAC/F,CAAC,MACI;MACD,OAAOoH,kBAAkB,CAACM,WAAW,CAACJ,UAAU,EAAEtH,KAAK,CAAC;IAC5D;EACJ;EACAwG,cAAcA,CAACY,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,EAAE;IAC9D,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC0M,KAAK,CAACzI,IAAI,CAAClE,IAAI,CAAC;IACzB;IACA,IAAI,IAAI,CAACuM,aAAa,IAAI,IAAI,CAACA,aAAa,CAACzM,cAAc,EAAE;MACzD,OAAO,IAAI,CAACyM,aAAa,CAACzM,cAAc,CAACY,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,CAAC;IAC/F,CAAC,MACI;MACD,OAAOU,kBAAkB,CAACP,YAAY,CAACS,UAAU,EAAEZ,IAAI,CAAC;IAC5D;EACJ;EACAI,YAAYA,CAACM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,EAAEhI,SAAS,EAAEqI,SAAS,EAAE;IAClF,IAAIL,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACiN,eAAe,CAAClN,IAAI,CAAC;IAC9B;IACA,IAAI,CAACiN,iBAAiB,CAACvM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAAC2L,aAAa,IAAI,IAAI,CAACA,aAAa,CAACnM,YAAY,EAAE;MACvD,OAAO,IAAI,CAACmM,aAAa,CAACnM,YAAY,CAACM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,EAAEhI,SAAS,EAAEqI,SAAS,CAAC;IACnH,CAAC,MACI;MACD,OAAOK,kBAAkB,CAACJ,UAAU,CAACM,UAAU,EAAEZ,IAAI,EAAEhI,SAAS,EAAEqI,SAAS,CAAC;IAChF;EACJ;EACAE,YAAYA,CAACG,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,EAAE;IAC5D,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACiN,eAAe,CAAClN,IAAI,CAAC;IAC9B;IACA,IAAI,CAACiN,iBAAiB,CAACvM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAAC2L,aAAa,IAAI,IAAI,CAACA,aAAa,CAAChM,YAAY,EAAE;MACvD,OAAO,IAAI,CAACgM,aAAa,CAAChM,YAAY,CAACG,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEZ,IAAI,CAAC;IAC7F,CAAC,MACI;MACD,OAAOU,kBAAkB,CAACF,UAAU,CAACI,UAAU,EAAEZ,IAAI,CAAC;IAC1D;EACJ;EACAiB,SAASA,CAACjF,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEmB,YAAY,EAAE;IAC/C,IAAI,CAACuL,aAAa,GAAGvL,YAAY;IACjC,IAAI,IAAI,CAACqL,aAAa,IAAI,IAAI,CAACA,aAAa,CAACtL,SAAS,EAAE;MACpD,IAAI,CAACsL,aAAa,CAACtL,SAAS,CAACjF,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEmB,YAAY,CAAC;IACzE,CAAC,MACI;MACDlF,QAAQ,CAACmF,OAAO,CAACpB,MAAM,EAAEmB,YAAY,CAAC;IAC1C;EACJ;AACJ;AACA,SAASuM,kBAAkBA,CAAC/a,IAAI,EAAE;EAC9B;EACA;EACAA,IAAI,CAAC,eAAe,CAAC,GAAGkB,aAAa;AACzC;AAEA,SAAS8Z,aAAaA,CAAChb,IAAI,EAAE;EACzB,MAAMiB,gBAAgB,CAAC;IACnBP,WAAWA,CAAC0L,UAAU,EAAE;MACpB,IAAI,CAACO,OAAO,GAAG3M,IAAI,CAACoB,OAAO;MAC3B,IAAI,CAACkG,IAAI,GAAG,mBAAmB,GAAG8E,UAAU;IAChD;IACAgB,cAAcA,CAAC9D,QAAQ,EAAElI,OAAO,EAAEiM,MAAM,EAAEC,IAAI,EAAE;MAC5C,QAAQA,IAAI,CAACC,IAAI;QACb,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,MAAM,IAAIzM,KAAK,CAAC,eAAewM,IAAI,CAACa,MAAM,6BAA6B,IAAI,CAAC7G,IAAI,IAAI,CAAC;QACzF,KAAK,WAAW;UACZgG,IAAI,GAAGhE,QAAQ,CAACmE,YAAY,CAACJ,MAAM,EAAEC,IAAI,CAAC;UAC1C;MACR;MACA,OAAOA,IAAI;IACf;EACJ;EACA;EACA;EACAtN,IAAI,CAAC,kBAAkB,CAAC,GAAGiB,gBAAgB;AAC/C;AAEA,SAASga,mBAAmBA,CAACjb,IAAI,EAAE;EAC/B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,IAAI,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IACvD,MAAM+a,WAAW,GAAG/a,GAAG,CAACkB,MAAM,CAAC,OAAO,CAAC;IACvC,MAAM8Z,UAAU,GAAG,IAAI;IACvB,MAAMlP,sBAAsB,GAAG9L,GAAG,CAACkB,MAAM,CAAC,kBAAkB,CAAC;IAC7D;IACA;IACA;IACA;IACA;IACA;IACA;IACA6L,OAAO,CAAC/M,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,SAAS4L,mBAAmBA,CAAA,EAAG;MACxE,IAAImO,OAAO,GAAGlO,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC;MACzD,IAAI8Z,OAAO,EAAE;QACT;MACJ;MACAA,OAAO,GAAGlO,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAG4L,OAAO,CAACvM,SAAS,CAAC0a,IAAI;MAC9EnO,OAAO,CAACvM,SAAS,CAAC0a,IAAI,GAAG,YAAY;QACjC,MAAMC,OAAO,GAAGF,OAAO,CAAC9Y,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAC9C,IAAI,IAAI,CAAC2Y,WAAW,CAAC,KAAKC,UAAU,EAAE;UAClC;UACA,MAAMI,iBAAiB,GAAGvb,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,mBAAmB,CAAC;UAC/D,IAAI4X,iBAAiB,EAAE;YACnBA,iBAAiB,CAAC3O,6BAA6B,EAAE;YACjD0O,OAAO,CAACrP,sBAAsB,CAAC,GAAG,IAAI;UAC1C;QACJ;QACA,OAAOqP,OAAO;MAClB,CAAC;IACL,CAAC;IACDpO,OAAO,CAAC/M,GAAG,CAACkB,MAAM,CAAC,uBAAuB,CAAC,CAAC,GAAG,SAASma,qBAAqBA,CAAA,EAAG;MAC5E;MACA,MAAMJ,OAAO,GAAGlO,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC;MAC3D,IAAI8Z,OAAO,EAAE;QACTlO,OAAO,CAACvM,SAAS,CAAC0a,IAAI,GAAGD,OAAO;QAChClO,OAAO,CAAClN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAGyN,SAAS;MAC3D;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAAS0M,aAAaA,CAACzb,IAAI,EAAE;EACzB+W,mBAAmB,CAAC/W,IAAI,CAAC;EACzB+a,kBAAkB,CAAC/a,IAAI,CAAC;EACxBgb,aAAa,CAAChb,IAAI,CAAC;EACnBD,YAAY,CAACC,IAAI,CAAC;EAClBwH,SAAS,CAACxH,IAAI,CAAC;EACf+J,UAAU,CAAC/J,IAAI,CAAC;EAChB6O,cAAc,CAAC7O,IAAI,CAAC;EACpB8W,kBAAkB,CAAC9W,IAAI,CAAC;EACxBib,mBAAmB,CAACjb,IAAI,CAAC;AAC7B;AAEAyb,aAAa,CAACzb,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}