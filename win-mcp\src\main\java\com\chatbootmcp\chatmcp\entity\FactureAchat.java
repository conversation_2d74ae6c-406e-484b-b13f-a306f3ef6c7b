package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "facture_achats")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FactureAchat {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "num_facture")
    private String numFacture;
    
    @Column(name = "date_facture")
    private LocalDateTime dateFacture;
    
    @Column(name = "date_echeance")
    private LocalDateTime dateEcheance;
    
    @Column(name = "montant_ht", precision = 19, scale = 2)
    private BigDecimal montantHt;
    
    @Column(name = "montant_tva", precision = 19, scale = 2)
    private BigDecimal montantTva;
    
    @Column(name = "montant_ttc", precision = 19, scale = 2)
    private BigDecimal montantTtc;
    
    @Column(name = "montant_remise", precision = 19, scale = 2)
    private BigDecimal montantRemise;
    
    @Column(name = "taux_remise", precision = 5, scale = 2)
    private BigDecimal tauxRemise;
    
    @Column(name = "statut")
    private String statut;
    
    @Column(name = "est_reglee")
    private Boolean estReglee;
    
    @Column(name = "montant_regle", precision = 19, scale = 2)
    private BigDecimal montantRegle;
    
    @Column(name = "reste_a_payer", precision = 19, scale = 2)
    private BigDecimal resteAPayer;
    
    @Column(name = "observations")
    private String observations;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fournisseur_id")
    private Fournisseur fournisseur;
    
    @OneToMany(mappedBy = "factureAchat", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<DetailFactureAchat> details;
}
