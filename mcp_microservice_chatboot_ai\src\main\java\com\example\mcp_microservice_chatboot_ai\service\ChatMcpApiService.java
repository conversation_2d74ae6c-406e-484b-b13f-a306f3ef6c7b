package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.ChatMessage;
import com.example.mcp_microservice_chatboot_ai.model.Conversation;
import com.example.mcp_microservice_chatboot_ai.model.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Service for communicating with the chat-mcp API.
 */

/**
 * Role: Service for communicating with the chat-mcp API
    Purpose:
    Handles authentication with the chat-mcp API
    Sends messages to the chat-mcp API
    Creates and retrieves conversations
    Manages authentication tokens in a cache
 */
@Service
public class ChatMcpApiService {

    private final WebClient webClient;

    @Value("${chat-mcp.api.auth-endpoint}")
    private String authEndpoint;

    @Value("${chat-mcp.api.messages-endpoint}")
    private String messagesEndpoint;

    @Value("${chat-mcp.api.conversations-endpoint}")
    private String conversationsEndpoint;

    @Value("${chat-mcp.api.user-data-endpoint:/users/data}")
    private String userDataEndpoint;

    // Cache for auth tokens
    private final Map<String, String> authTokenCache = new HashMap<>();

    @Autowired
    public ChatMcpApiService(WebClient chatMcpWebClient) {
        this.webClient = chatMcpWebClient;
    }

    /**
     * Authenticates a user with the chat-mcp API.
     *
     * @param authRequest The authentication request
     * @return A Mono containing the authentication response
     */
    public Mono<AuthResponse> authenticate(AuthRequest authRequest) {
        return webClient.post()
                .uri(authEndpoint)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(authRequest)
                .retrieve()
                .bodyToMono(AuthResponse.class)
                .doOnSuccess(response -> {
                    // Cache the token for future use
                    if (response != null && response.getToken() != null) {
                        authTokenCache.put(authRequest.getUsername(), response.getToken());
                    }
                });
    }

    /**
     * Gets the authentication token for a user.
     *
     * @param username The username
     * @return The authentication token, or null if not found
     */
    public String getAuthToken(String username) {
        return authTokenCache.get(username);
    }

    /**
     * Sends a message to the chat-mcp API.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response
     */
    public Mono<ChatResponse> sendMessage(ChatRequest chatRequest) {
        String token = getAuthToken(chatRequest.getUsername());
        if (token == null) {
            return Mono.error(new RuntimeException("User not authenticated"));
        }

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("conversationId", chatRequest.getConversationId());
        requestBody.put("content", chatRequest.getContent());

        return webClient.post()
                .uri(messagesEndpoint)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + token)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    // Convert the response to a ChatResponse
                    ChatMessage message = ChatMessage.builder()
                            .id(response.get("id").toString())
                            .conversationId(response.get("conversationId").toString())
                            .role(ChatMessage.MessageRole.ASSISTANT)
                            .content(response.get("content").toString())
                            .timestamp(LocalDateTime.now())
                            .build();

                    return ChatResponse.fromChatMessage(message);
                });
    }

    /**
     * Creates a new conversation in the chat-mcp API.
     *
     * @param conversationRequest The conversation request
     * @return A Mono containing the conversation response
     */
    public Mono<ConversationResponse> createConversation(ConversationRequest conversationRequest) {
        String token = getAuthToken(conversationRequest.getUsername());
        if (token == null) {
            return Mono.error(new RuntimeException("User not authenticated"));
        }

        return webClient.post()
                .uri(conversationsEndpoint)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + token)
                .bodyValue(conversationRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    // Convert the response to a ConversationResponse
                    Conversation conversation = Conversation.builder()
                            .id(response.get("id").toString())
                            .title(response.get("title").toString())
                            .username(conversationRequest.getUsername())
                            .createdAt(LocalDateTime.now())
                            .updatedAt(LocalDateTime.now())
                            .build();

                    return ConversationResponse.fromConversation(conversation);
                });
    }

    /**
     * Gets all conversations for a user from the chat-mcp API.
     *
     * @param username The username
     * @return A Flux containing the conversation responses
     */
    public Flux<ConversationResponse> getUserConversations(String username) {
        String token = getAuthToken(username);
        if (token == null) {
            return Flux.error(new RuntimeException("User not authenticated"));
        }

        return webClient.get()
                .uri(conversationsEndpoint)
                .header("Authorization", "Bearer " + token)
                .retrieve()
                .bodyToFlux(Map.class)
                .map(response -> {
                    // Convert the response to a ConversationResponse
                    Conversation conversation = Conversation.builder()
                            .id(response.get("id").toString())
                            .title(response.get("title").toString())
                            .username(username)
                            .createdAt(LocalDateTime.now()) // Assuming the API doesn't return timestamps
                            .updatedAt(LocalDateTime.now())
                            .build();

                    return ConversationResponse.fromConversation(conversation);
                });
    }

    /**
     * Gets user data from the chat-mcp API.
     *
     * @param username The username
     * @return A Mono containing the user data
     */
    public Mono<Object> getUserData(String username) {
        // Use real data from the chat-mcp API
        System.out.println("Fetching real user data for: " + username);
        return fetchUserData(username);
    }

    /**
     * Fetches user data from the chat-mcp API.
     *
     * @param username The username
     * @return A Mono containing the user data
     */
    private Mono<Object> fetchUserData(String username) {
        // First, authenticate the user
        return authenticateUser(username)
                .flatMap(token -> {
                    System.out.println("Authenticated user: " + username + ", fetching data...");
                    return webClient.get()
                            .uri(userDataEndpoint)
                            .header("Authorization", "Bearer " + token)
                            .retrieve()
                            .bodyToMono(Object.class)
                            .doOnSuccess(data -> System.out.println("Successfully fetched real user data for: " + username))
                            .onErrorResume(e -> {
                                System.out.println("Error fetching user data from API: " + e.getMessage());
                                e.printStackTrace();
                                return Mono.error(new RuntimeException("Failed to fetch user data from chat-mcp API: " + e.getMessage()));
                            });
                })
                .onErrorResume(e -> {
                    System.out.println("Authentication failed for user: " + username + ", error: " + e.getMessage());
                    e.printStackTrace();
                    return Mono.error(new RuntimeException("Authentication failed for user: " + username + ", error: " + e.getMessage()));
                });
    }

    /**
     * Authenticates a user and returns the token.
     *
     * @param username The username
     * @return A Mono containing the authentication token
     */
    public Mono<String> authenticateUser(String username) {
        // Check if we already have a cached token
        String cachedToken = getAuthToken(username);
        if (cachedToken != null) {
            return Mono.just(cachedToken);
        }

        // Create authentication request
        AuthRequest authRequest = new AuthRequest();
        authRequest.setUsername(username);
        authRequest.setPassword("password"); // Default password for demo users

        return authenticate(authRequest)
                .map(AuthResponse::getToken)
                .doOnSuccess(token -> System.out.println("Authentication successful for user: " + username));
    }

    /**
     * Gets mock user data for demo purposes.
     * THIS METHOD IS DISABLED - ONLY REAL DATA FROM CHAT-MCP API SHOULD BE USED
     *
     * @param username The username
     * @return Mock user data
     */
    @Deprecated
    public Map<String, Object> getMockUserData(String username) {
        throw new UnsupportedOperationException("Mock data is disabled. Only real data from chat-mcp API should be used.");
    }
}
