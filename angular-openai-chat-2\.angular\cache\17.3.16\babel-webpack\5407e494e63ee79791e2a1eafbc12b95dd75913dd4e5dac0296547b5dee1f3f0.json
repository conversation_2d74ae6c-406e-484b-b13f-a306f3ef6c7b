{"ast": null, "code": "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}", "map": {"version": 3, "names": ["bindCallbackInternals", "bindNodeCallback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler"], "sources": ["C:/Users/<USER>/Downloads/Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/rxjs/dist/esm/internal/observable/bindNodeCallback.js"], "sourcesContent": ["import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n    return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,yBAAyB;AAC/D,OAAO,SAASC,gBAAgBA,CAACC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACtE,OAAOJ,qBAAqB,CAAC,IAAI,EAAEE,YAAY,EAAEC,cAAc,EAAEC,SAAS,CAAC;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}