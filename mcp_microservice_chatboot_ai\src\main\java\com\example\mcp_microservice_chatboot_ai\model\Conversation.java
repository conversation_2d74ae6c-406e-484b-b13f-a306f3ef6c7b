package com.example.mcp_microservice_chatboot_ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Represents a conversation in the system.
 */

/**
 * Role: Entity class representing a conversation
    Purpose:
    Stores conversation data (ID, title, username, timestamps)
    Provides builder pattern for easy creation
    Represents a chat session between a user and the AI
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Conversation {
    
    /**
     * The unique identifier of the conversation.
     */
    private String id;
    
    /**
     * The title of the conversation.
     */
    private String title;
    
    /**
     * The username of the user who owns the conversation.
     */
    private String username;
    
    /**
     * The timestamp when the conversation was created.
     */
    private LocalDateTime createdAt;
    
    /**
     * The timestamp when the conversation was last updated.
     */
    private LocalDateTime updatedAt;
}
