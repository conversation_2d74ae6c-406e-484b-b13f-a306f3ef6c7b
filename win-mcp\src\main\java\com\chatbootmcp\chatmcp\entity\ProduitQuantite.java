package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * ProduitQuantite entity representing product quantities in transfers
 */
@Entity
@Table(name = "produit_quantite")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProduitQuantite {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "quantite", precision = 15, scale = 2)
    private BigDecimal quantite;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transfert_stock_id")
    private TransfertStock transfertStock;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;
}
