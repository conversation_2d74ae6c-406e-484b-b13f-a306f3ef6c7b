


export class SyntheseBlAchat {
    tauxRemise?: number;
    totalMontantAchatAvoir?: number;
    totalMontantAchatBrutCalculeTtc?: number;
    totalMontantAchatBrutEffectifTtc?: number;
    totalMontantAchatBrutHt?: number;
    totalMontantAchatLivraison?: number;
    totalMontantAchatNetCalculeTtc?: number;
    totalMontantAchatNetEffectifTtc?: number;
    totalMontantAchatNetHt?: number;
    totalMontantAchatPpv?: number;
    totalMontantAchatRegle?: number;
    totalMontantRemise?: number;
    totalMontantTva?: number;
    totalNombreAchats?: number;
    totalNombreLignesAchats?: number;
    totalNombreProduits?: number;
    totalQuantitesCommandees?: number;
    totalQuantitesRecues?: number;
    totalQuantitesUg?: number;
    diffMontantAchatBrutEffectifAndCalculeTtc?: number
    diffMontantAchatNetEffectifAndCalculeTtc?: number
    totalMontantAchatUgPpv?: number
    totalQuantitesAvoirInterne?: number

}
