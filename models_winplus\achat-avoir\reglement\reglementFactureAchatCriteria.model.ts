import { ReglementFactureStatutCriteria } from 'src/app/winpharm/enums/achat-avoir/ReglementFactureStatutCriteria.enum';

// import { Moment } from 'moment';

import { EnteteFactureAchat } from 'src/app/winpharm/models/achat-avoir/facture/enteteFactureAchat.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { Fournisseur } from '../../tiers/fournisseur/fournisseur.model';
import { ModePaiement } from 'src/app/winpharm/enums/common/ModePaiement.enum';


export class ReglementFactureAchatCriteria {
    dateDebut?: any;
    dateFin?: any;
    enteteFactureAchat?: EnteteFactureAchat;
    fournisseurId?: number;
    operateur?: Operateur;
    reference?: string;
    statut?: ReglementFactureStatutCriteria;
    fournisseur?: Fournisseur;

    /// test
    modePaiement?: ModePaiement
}

