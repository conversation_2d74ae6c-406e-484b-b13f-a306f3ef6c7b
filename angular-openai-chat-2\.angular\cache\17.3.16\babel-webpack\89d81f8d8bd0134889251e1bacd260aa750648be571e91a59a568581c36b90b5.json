{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abder<PERSON>mane_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\n// src/app/chat/services/message-storage.service.ts\nimport { Injectable } from '@angular/core';\nlet MessageStorageService = class MessageStorageService {\n  constructor() {\n    this.conversations = [];\n    this.messages = [];\n    // Load from localStorage if available\n    const savedConversations = localStorage.getItem('conversations');\n    const savedMessages = localStorage.getItem('messages');\n    if (savedConversations) {\n      this.conversations = JSON.parse(savedConversations);\n    }\n    if (savedMessages) {\n      this.messages = JSON.parse(savedMessages);\n      // Convert string dates back to Date objects\n      this.messages.forEach(msg => {\n        msg.timestamp = new Date(msg.timestamp);\n      });\n    }\n  }\n  saveToStorage() {\n    localStorage.setItem('conversations', JSON.stringify(this.conversations));\n    localStorage.setItem('messages', JSON.stringify(this.messages));\n  }\n  createConversation(title = 'Nouvelle Conversation') {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const id = Date.now().toString();\n      _this.conversations.push({\n        id,\n        title,\n        lastUpdated: new Date()\n      });\n      _this.saveToStorage();\n      return id;\n    })();\n  }\n  getConversations() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return [..._this2.conversations].sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());\n    })();\n  }\n  getMessages(conversationId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return _this3.messages.filter(msg => msg.conversationId === conversationId).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n    })();\n  }\n  saveMessage(message) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const id = _this4.messages.length > 0 ? Math.max(..._this4.messages.map(m => m.id || 0)) + 1 : 1;\n      const newMessage = {\n        ...message,\n        id\n      };\n      _this4.messages.push(newMessage);\n      // Update conversation lastUpdated\n      const conversation = _this4.conversations.find(c => c.id === message.conversationId);\n      if (conversation) {\n        conversation.lastUpdated = new Date();\n      }\n      _this4.saveToStorage();\n      return id;\n    })();\n  }\n  deleteConversation(conversationId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      _this5.messages = _this5.messages.filter(msg => msg.conversationId !== conversationId);\n      _this5.conversations = _this5.conversations.filter(conv => conv.id !== conversationId);\n      _this5.saveToStorage();\n    })();\n  }\n  updateConversation(conversation) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const index = _this6.conversations.findIndex(c => c.id === conversation.id);\n      if (index !== -1) {\n        _this6.conversations[index] = {\n          ...conversation\n        };\n        _this6.saveToStorage();\n      }\n    })();\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nMessageStorageService = __decorate([Injectable({\n  providedIn: 'root'\n})], MessageStorageService);\nexport { MessageStorageService };", "map": {"version": 3, "names": ["Injectable", "MessageStorageService", "constructor", "conversations", "messages", "savedConversations", "localStorage", "getItem", "savedMessages", "JSON", "parse", "for<PERSON>ach", "msg", "timestamp", "Date", "saveToStorage", "setItem", "stringify", "createConversation", "title", "_this", "_asyncToGenerator", "id", "now", "toString", "push", "lastUpdated", "getConversations", "_this2", "sort", "a", "b", "getTime", "getMessages", "conversationId", "_this3", "filter", "saveMessage", "message", "_this4", "length", "Math", "max", "map", "m", "newMessage", "conversation", "find", "c", "deleteConversation", "_this5", "conv", "updateConversation", "_this6", "index", "findIndex", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\services\\message-storage.service.ts"], "sourcesContent": ["// src/app/chat/services/message-storage.service.ts\r\nimport { Injectable } from '@angular/core';\r\n\r\nexport interface ChatMessage {\r\n  id?: number;\r\n  conversationId: string;\r\n  role: 'user' | 'assistant' | 'system';\r\n  content: string;\r\n  timestamp: Date;\r\n}\r\n\r\nexport interface Conversation {\r\n  id?: string;\r\n  title: string;\r\n  lastUpdated: Date;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class MessageStorageService {\r\n  private conversations: Conversation[] = [];\r\n  private messages: ChatMessage[] = [];\r\n\r\n  constructor() {\r\n    // Load from localStorage if available\r\n    const savedConversations = localStorage.getItem('conversations');\r\n    const savedMessages = localStorage.getItem('messages');\r\n    \r\n    if (savedConversations) {\r\n      this.conversations = JSON.parse(savedConversations);\r\n    }\r\n    \r\n    if (savedMessages) {\r\n      this.messages = JSON.parse(savedMessages);\r\n      // Convert string dates back to Date objects\r\n      this.messages.forEach(msg => {\r\n        msg.timestamp = new Date(msg.timestamp);\r\n      });\r\n    }\r\n  }\r\n\r\n  private saveToStorage() {\r\n    localStorage.setItem('conversations', JSON.stringify(this.conversations));\r\n    localStorage.setItem('messages', JSON.stringify(this.messages));\r\n  }\r\n\r\n  async createConversation(title: string = 'Nouvelle Conversation'): Promise<string> {\r\n    const id = Date.now().toString();\r\n    this.conversations.push({\r\n      id,\r\n      title,\r\n      lastUpdated: new Date()\r\n    });\r\n    this.saveToStorage();\r\n    return id;\r\n  }\r\n\r\n  async getConversations(): Promise<Conversation[]> {\r\n    return [...this.conversations].sort((a, b) => \r\n      new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()\r\n    );\r\n  }\r\n\r\n  async getMessages(conversationId: string): Promise<ChatMessage[]> {\r\n    return this.messages\r\n      .filter(msg => msg.conversationId === conversationId)\r\n      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\r\n  }\r\n\r\n  async saveMessage(message: ChatMessage): Promise<number> {\r\n    const id = this.messages.length > 0 ? \r\n      Math.max(...this.messages.map(m => m.id || 0)) + 1 : 1;\r\n    \r\n    const newMessage = {\r\n      ...message,\r\n      id\r\n    };\r\n    \r\n    this.messages.push(newMessage);\r\n    \r\n    // Update conversation lastUpdated\r\n    const conversation = this.conversations.find(c => c.id === message.conversationId);\r\n    if (conversation) {\r\n      conversation.lastUpdated = new Date();\r\n    }\r\n    \r\n    this.saveToStorage();\r\n    return id;\r\n  }\r\n\r\n  async deleteConversation(conversationId: string): Promise<void> {\r\n    this.messages = this.messages.filter(msg => msg.conversationId !== conversationId);\r\n    this.conversations = this.conversations.filter(conv => conv.id !== conversationId);\r\n    this.saveToStorage();\r\n  }\r\n\r\n  async updateConversation(conversation: Conversation): Promise<void> {\r\n    const index = this.conversations.findIndex(c => c.id === conversation.id);\r\n    if (index !== -1) {\r\n      this.conversations[index] = { ...conversation };\r\n      this.saveToStorage();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;AACA,SAASA,UAAU,QAAQ,eAAe;AAmBnC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAIhCC,YAAA;IAHQ,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,QAAQ,GAAkB,EAAE;IAGlC;IACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAChE,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEtD,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACF,aAAa,GAAGM,IAAI,CAACC,KAAK,CAACL,kBAAkB,CAAC;IACrD;IAEA,IAAIG,aAAa,EAAE;MACjB,IAAI,CAACJ,QAAQ,GAAGK,IAAI,CAACC,KAAK,CAACF,aAAa,CAAC;MACzC;MACA,IAAI,CAACJ,QAAQ,CAACO,OAAO,CAACC,GAAG,IAAG;QAC1BA,GAAG,CAACC,SAAS,GAAG,IAAIC,IAAI,CAACF,GAAG,CAACC,SAAS,CAAC;MACzC,CAAC,CAAC;IACJ;EACF;EAEQE,aAAaA,CAAA;IACnBT,YAAY,CAACU,OAAO,CAAC,eAAe,EAAEP,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACd,aAAa,CAAC,CAAC;IACzEG,YAAY,CAACU,OAAO,CAAC,UAAU,EAAEP,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACb,QAAQ,CAAC,CAAC;EACjE;EAEMc,kBAAkBA,CAACC,KAAA,GAAgB,uBAAuB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC9D,MAAMC,EAAE,GAAGR,IAAI,CAACS,GAAG,EAAE,CAACC,QAAQ,EAAE;MAChCJ,KAAI,CAACjB,aAAa,CAACsB,IAAI,CAAC;QACtBH,EAAE;QACFH,KAAK;QACLO,WAAW,EAAE,IAAIZ,IAAI;OACtB,CAAC;MACFM,KAAI,CAACL,aAAa,EAAE;MACpB,OAAOO,EAAE;IAAC;EACZ;EAEMK,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MACpB,OAAO,CAAC,GAAGO,MAAI,CAACzB,aAAa,CAAC,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACvC,IAAIjB,IAAI,CAACiB,CAAC,CAACL,WAAW,CAAC,CAACM,OAAO,EAAE,GAAG,IAAIlB,IAAI,CAACgB,CAAC,CAACJ,WAAW,CAAC,CAACM,OAAO,EAAE,CACtE;IAAC;EACJ;EAEMC,WAAWA,CAACC,cAAsB;IAAA,IAAAC,MAAA;IAAA,OAAAd,iBAAA;MACtC,OAAOc,MAAI,CAAC/B,QAAQ,CACjBgC,MAAM,CAACxB,GAAG,IAAIA,GAAG,CAACsB,cAAc,KAAKA,cAAc,CAAC,CACpDL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,SAAS,CAACmB,OAAO,EAAE,GAAGD,CAAC,CAAClB,SAAS,CAACmB,OAAO,EAAE,CAAC;IAAC;EACnE;EAEMK,WAAWA,CAACC,OAAoB;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MACpC,MAAMC,EAAE,GAAGiB,MAAI,CAACnC,QAAQ,CAACoC,MAAM,GAAG,CAAC,GACjCC,IAAI,CAACC,GAAG,CAAC,GAAGH,MAAI,CAACnC,QAAQ,CAACuC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACtB,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAExD,MAAMuB,UAAU,GAAG;QACjB,GAAGP,OAAO;QACVhB;OACD;MAEDiB,MAAI,CAACnC,QAAQ,CAACqB,IAAI,CAACoB,UAAU,CAAC;MAE9B;MACA,MAAMC,YAAY,GAAGP,MAAI,CAACpC,aAAa,CAAC4C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAKgB,OAAO,CAACJ,cAAc,CAAC;MAClF,IAAIY,YAAY,EAAE;QAChBA,YAAY,CAACpB,WAAW,GAAG,IAAIZ,IAAI,EAAE;MACvC;MAEAyB,MAAI,CAACxB,aAAa,EAAE;MACpB,OAAOO,EAAE;IAAC;EACZ;EAEM2B,kBAAkBA,CAACf,cAAsB;IAAA,IAAAgB,MAAA;IAAA,OAAA7B,iBAAA;MAC7C6B,MAAI,CAAC9C,QAAQ,GAAG8C,MAAI,CAAC9C,QAAQ,CAACgC,MAAM,CAACxB,GAAG,IAAIA,GAAG,CAACsB,cAAc,KAAKA,cAAc,CAAC;MAClFgB,MAAI,CAAC/C,aAAa,GAAG+C,MAAI,CAAC/C,aAAa,CAACiC,MAAM,CAACe,IAAI,IAAIA,IAAI,CAAC7B,EAAE,KAAKY,cAAc,CAAC;MAClFgB,MAAI,CAACnC,aAAa,EAAE;IAAC;EACvB;EAEMqC,kBAAkBA,CAACN,YAA0B;IAAA,IAAAO,MAAA;IAAA,OAAAhC,iBAAA;MACjD,MAAMiC,KAAK,GAAGD,MAAI,CAAClD,aAAa,CAACoD,SAAS,CAACP,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAKwB,YAAY,CAACxB,EAAE,CAAC;MACzE,IAAIgC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBD,MAAI,CAAClD,aAAa,CAACmD,KAAK,CAAC,GAAG;UAAE,GAAGR;QAAY,CAAE;QAC/CO,MAAI,CAACtC,aAAa,EAAE;MACtB;IAAC;EACH;;;;;AAnFWd,qBAAqB,GAAAuD,UAAA,EAHjCxD,UAAU,CAAC;EACVyD,UAAU,EAAE;CACb,CAAC,C,EACWxD,qBAAqB,CAoFjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}