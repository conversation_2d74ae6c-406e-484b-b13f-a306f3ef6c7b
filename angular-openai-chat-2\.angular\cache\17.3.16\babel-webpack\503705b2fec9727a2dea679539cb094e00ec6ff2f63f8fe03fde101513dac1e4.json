{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abder<PERSON><PERSON><PERSON>_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { ChatWidgetComponent } from './chat-widget.component';\ndescribe('ChatWidgetComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [ChatWidgetComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ChatWidgetComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ChatWidgetComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-widget\\chat-widget.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { ChatWidgetComponent } from './chat-widget.component';\r\n\r\ndescribe('ChatWidgetComponent', () => {\r\n  let component: ChatWidgetComponent;\r\n  let fixture: ComponentFixture<ChatWidgetComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [ChatWidgetComponent]\r\n    })\r\n    .compileComponents();\r\n    \r\n    fixture = TestBed.createComponent(ChatWidgetComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,SAA8B;EAClC,IAAIC,OAA8C;EAElDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,mBAAmB;KAC9B,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,mBAAmB,CAAC;IACtDE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}