package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.entity.*;
import com.chatbootmcp.chatmcp.exception.ResourceNotFoundException;
import com.chatbootmcp.chatmcp.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnhancedUserDataService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private OrderRepository orderRepository;

    /**
     * Get comprehensive user data including profile, transactions, invoices, and orders
     */
    public Map<String, Object> getComprehensiveUserData(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        Map<String, Object> userData = new HashMap<>();

        // Basic user information
        userData.put("username", user.getUsername());
        userData.put("email", user.getEmail());
        userData.put("roles", user.getRoles());
        userData.put("createdAt", user.getCreatedAt());
        userData.put("updatedAt", user.getUpdatedAt());

        // User profile information
        UserProfile profile = userProfileRepository.findByUser(user).orElse(null);
        if (profile != null) {
            userData.putAll(getUserProfileData(profile));
        }

        // Financial summary
        userData.putAll(getFinancialSummary(user));

        // Recent transactions
        userData.put("recentTransactions", getRecentTransactions(user));

        // Recent invoices
        userData.put("recentInvoices", getRecentInvoices(user));

        // Recent orders
        userData.put("recentOrders", getRecentOrders(user));

        // Account statistics
        userData.putAll(getAccountStatistics(user));

        return userData;
    }

    /**
     * Get user profile data
     */
    private Map<String, Object> getUserProfileData(UserProfile profile) {
        Map<String, Object> profileData = new HashMap<>();

        // Personal information
        profileData.put("fullName", profile.getFullName());
        profileData.put("phoneNumber", profile.getPhoneNumber());
        profileData.put("address", profile.getAddress());
        profileData.put("city", profile.getCity());
        profileData.put("postalCode", profile.getPostalCode());
        profileData.put("country", profile.getCountry());
        profileData.put("dateOfBirth", profile.getDateOfBirth());

        // Professional information
        profileData.put("jobTitle", profile.getJobTitle());
        profileData.put("company", profile.getCompany());
        profileData.put("department", profile.getDepartment());
        profileData.put("employeeId", profile.getEmployeeId());

        // Account information
        profileData.put("accountType", profile.getAccountType());
        profileData.put("subscriptionStatus", profile.getSubscriptionStatus());
        profileData.put("subscriptionStartDate", profile.getSubscriptionStartDate());
        profileData.put("subscriptionEndDate", profile.getSubscriptionEndDate());
        profileData.put("subscriptionType", profile.getSubscriptionType());
        profileData.put("subscriptionPrice", profile.getSubscriptionPrice());

        // Financial information
        profileData.put("currentBalance", profile.getCurrentBalance());
        profileData.put("creditLimit", profile.getCreditLimit());
        profileData.put("paymentMethod", profile.getPaymentMethod());
        profileData.put("cardLastFour", profile.getCardLastFour());
        profileData.put("cardExpiryDate", profile.getCardExpiryDate());
        profileData.put("nextPaymentDue", profile.getNextPaymentDue());

        // Preferences
        profileData.put("languagePreference", profile.getLanguagePreference());
        profileData.put("timezone", profile.getTimezone());
        profileData.put("notificationPreferences", profile.getNotificationPreferences());

        // Metadata
        profileData.put("profileCompletionPercentage", profile.getProfileCompletionPercentage());
        profileData.put("lastProfileUpdate", profile.getLastProfileUpdate());

        return profileData;
    }

    /**
     * Get financial summary
     */
    private Map<String, Object> getFinancialSummary(User user) {
        Map<String, Object> financialData = new HashMap<>();

        Double totalCredits = transactionRepository.getTotalCreditsForUser(user);
        Double totalDebits = transactionRepository.getTotalDebitsForUser(user);
        Double totalPaidInvoices = invoiceRepository.getTotalPaidAmountForUser(user);
        Double totalUnpaidInvoices = invoiceRepository.getTotalUnpaidAmountForUser(user);
        Double totalOrderAmount = orderRepository.getTotalOrderAmountForUser(user);

        financialData.put("totalCredits", totalCredits != null ? totalCredits : 0.0);
        financialData.put("totalDebits", totalDebits != null ? totalDebits : 0.0);
        financialData.put("netBalance", (totalCredits != null ? totalCredits : 0.0) - (totalDebits != null ? totalDebits : 0.0));
        financialData.put("totalPaidInvoices", totalPaidInvoices != null ? totalPaidInvoices : 0.0);
        financialData.put("totalUnpaidInvoices", totalUnpaidInvoices != null ? totalUnpaidInvoices : 0.0);
        financialData.put("totalOrderAmount", totalOrderAmount != null ? totalOrderAmount : 0.0);

        return financialData;
    }

    /**
     * Get recent transactions
     */
    private List<Map<String, Object>> getRecentTransactions(User user) {
        Pageable pageable = PageRequest.of(0, 10);
        List<Transaction> transactions = transactionRepository.findByUserOrderByCreatedAtDesc(user);
        return transactions.stream()
                .limit(10)
                .map(this::convertTransactionToMap)
                .collect(Collectors.toList());
    }

    /**
     * Get recent invoices
     */
    private List<Map<String, Object>> getRecentInvoices(User user) {
        List<Invoice> invoices = invoiceRepository.findByUserOrderByInvoiceDateDesc(user);
        return invoices.stream()
                .limit(5)
                .map(this::convertInvoiceToMap)
                .collect(Collectors.toList());
    }

    /**
     * Get recent orders
     */
    private List<Map<String, Object>> getRecentOrders(User user) {
        List<Order> orders = orderRepository.findByUserOrderByOrderDateDesc(user);
        return orders.stream()
                .limit(5)
                .map(this::convertOrderToMap)
                .collect(Collectors.toList());
    }

    /**
     * Get account statistics
     */
    private Map<String, Object> getAccountStatistics(User user) {
        Map<String, Object> stats = new HashMap<>();

        Long totalOrders = orderRepository.getTotalOrderCountForUser(user);
        Double averageOrderValue = orderRepository.getAverageOrderValueForUser(user);
        List<Invoice> overdueInvoices = invoiceRepository.findOverdueInvoicesForUser(user, LocalDate.now());
        List<Order> activeOrders = orderRepository.findActiveOrdersForUser(user);

        stats.put("totalOrders", totalOrders != null ? totalOrders : 0L);
        stats.put("averageOrderValue", averageOrderValue != null ? averageOrderValue : 0.0);
        stats.put("overdueInvoicesCount", overdueInvoices.size());
        stats.put("activeOrdersCount", activeOrders.size());

        return stats;
    }

    /**
     * Convert Transaction to Map
     */
    private Map<String, Object> convertTransactionToMap(Transaction transaction) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", transaction.getId());
        map.put("transactionId", transaction.getTransactionId());
        map.put("type", transaction.getTransactionType());
        map.put("amount", transaction.getAmount());
        map.put("currency", transaction.getCurrency());
        map.put("description", transaction.getDescription());
        map.put("category", transaction.getCategory());
        map.put("status", transaction.getStatus());
        map.put("paymentMethod", transaction.getPaymentMethod());
        map.put("createdAt", transaction.getCreatedAt());
        map.put("balanceAfter", transaction.getBalanceAfter());
        return map;
    }

    /**
     * Convert Invoice to Map
     */
    private Map<String, Object> convertInvoiceToMap(Invoice invoice) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", invoice.getId());
        map.put("invoiceNumber", invoice.getInvoiceNumber());
        map.put("invoiceDate", invoice.getInvoiceDate());
        map.put("dueDate", invoice.getDueDate());
        map.put("totalAmount", invoice.getTotalAmount());
        map.put("currency", invoice.getCurrency());
        map.put("status", invoice.getStatus());
        map.put("paymentStatus", invoice.getPaymentStatus());
        map.put("description", invoice.getDescription());
        map.put("serviceType", invoice.getServiceType());
        return map;
    }

    /**
     * Convert Order to Map
     */
    private Map<String, Object> convertOrderToMap(Order order) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", order.getId());
        map.put("orderNumber", order.getOrderNumber());
        map.put("orderDate", order.getOrderDate());
        map.put("status", order.getStatus());
        map.put("totalAmount", order.getTotalAmount());
        map.put("currency", order.getCurrency());
        map.put("paymentStatus", order.getPaymentStatus());
        map.put("itemsDescription", order.getItemsDescription());
        map.put("itemsCount", order.getItemsCount());
        map.put("trackingNumber", order.getTrackingNumber());
        return map;
    }
}
