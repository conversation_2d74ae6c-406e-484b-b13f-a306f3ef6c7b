{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nimport { environment } from './environments/environment';\nlet AppComponent = class AppComponent {\n  constructor() {\n    this.title = 'angular-openai-chat-2';\n    this.useMcpServer = true; // Default to MCP server\n  }\n  ngOnInit() {\n    // Initialize from environment\n    this.useMcpServer = environment.useMcpServer;\n    // Check if there's a saved preference in localStorage\n    const savedPreference = localStorage.getItem('useMcpServer');\n    if (savedPreference !== null) {\n      this.useMcpServer = savedPreference === 'true';\n    }\n  }\n  toggleChatMode(event) {\n    const select = event.target;\n    this.useMcpServer = select.value === 'mcp';\n    // Save preference to localStorage\n    localStorage.setItem('useMcpServer', this.useMcpServer.toString());\n    // Reload the page to apply the change\n    // This is a simple approach - in a more complex app, you might want to handle this differently\n    window.location.reload();\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "names": ["Component", "environment", "AppComponent", "constructor", "title", "useMcpServer", "ngOnInit", "savedPreference", "localStorage", "getItem", "toggleChatMode", "event", "select", "target", "value", "setItem", "toString", "window", "location", "reload", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { environment } from './environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'angular-openai-chat-2';\r\n  useMcpServer = true; // Default to MCP server\r\n\r\n  ngOnInit() {\r\n    // Initialize from environment\r\n    this.useMcpServer = environment.useMcpServer;\r\n\r\n    // Check if there's a saved preference in localStorage\r\n    const savedPreference = localStorage.getItem('useMcpServer');\r\n    if (savedPreference !== null) {\r\n      this.useMcpServer = savedPreference === 'true';\r\n    }\r\n  }\r\n\r\n  toggleChatMode(event: Event) {\r\n    const select = event.target as HTMLSelectElement;\r\n    this.useMcpServer = select.value === 'mcp';\r\n\r\n    // Save preference to localStorage\r\n    localStorage.setItem('useMcpServer', this.useMcpServer.toString());\r\n\r\n    // Reload the page to apply the change\r\n    // This is a simple approach - in a more complex app, you might want to handle this differently\r\n    window.location.reload();\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,WAAW,QAAQ,4BAA4B;AAOjD,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAAlBC,YAAA;IACL,KAAAC,KAAK,GAAG,uBAAuB;IAC/B,KAAAC,YAAY,GAAG,IAAI,CAAC,CAAC;EAwBvB;EAtBEC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,YAAY,GAAGJ,WAAW,CAACI,YAAY;IAE5C;IACA,MAAME,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAC5D,IAAIF,eAAe,KAAK,IAAI,EAAE;MAC5B,IAAI,CAACF,YAAY,GAAGE,eAAe,KAAK,MAAM;IAChD;EACF;EAEAG,cAAcA,CAACC,KAAY;IACzB,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAA2B;IAChD,IAAI,CAACR,YAAY,GAAGO,MAAM,CAACE,KAAK,KAAK,KAAK;IAE1C;IACAN,YAAY,CAACO,OAAO,CAAC,cAAc,EAAE,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE,CAAC;IAElE;IACA;IACAC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;EAC1B;CACD;AA1BYjB,YAAY,GAAAkB,UAAA,EALxBpB,SAAS,CAAC;EACTqB,QAAQ,EAAE,UAAU;EACpBC,QAAA,EAAAC,oBAAmC;;CAEpC,CAAC,C,EACWrB,YAAY,CA0BxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}