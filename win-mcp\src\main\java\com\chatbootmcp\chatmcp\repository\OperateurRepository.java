package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Operateur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Operateur entity
 */
@Repository
public interface OperateurRepository extends JpaRepository<Operateur, Long> {
    
    Optional<Operateur> findByUsername(String username);
    
    Optional<Operateur> findByEmail(String email);
    
    List<Operateur> findByActifTrue();
    
    @Query("SELECT o FROM Operateur o WHERE o.actif = true ORDER BY o.lastname, o.firstname")
    List<Operateur> findActiveOperateursOrderByName();
    
    boolean existsByUsername(String username);
    
    boolean existsByEmail(String email);
}
