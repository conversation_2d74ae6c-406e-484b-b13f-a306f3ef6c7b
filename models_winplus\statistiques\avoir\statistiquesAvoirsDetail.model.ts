import { CauseDemandeAvoir } from 'src/app/winpharm/enums/achat-avoir/CauseDemandeAvoir.enum';

// import { Moment } from 'moment';

import { FournisseurAbrege } from 'src/app/winpharm/models/tiers/fournisseur/fournisseurAbrege.model';
import { Produit } from '../../produit/base/produit.model';


export class StatistiquesAvoirsDetail { 
    cause?: CauseDemandeAvoir;
    date?: any;
    fournisseur?: FournisseurAbrege;
    montantPph?: number;
    montantPpv?: number;
    numero?: number;
    produit?: Produit;
    quantite?: number;
    tauxRemise?: number;
    totalStock?: number;
}

