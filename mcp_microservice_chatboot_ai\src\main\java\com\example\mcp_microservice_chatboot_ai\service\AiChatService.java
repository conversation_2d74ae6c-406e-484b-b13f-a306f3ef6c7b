package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.ChatMessage;
import com.example.mcp_microservice_chatboot_ai.model.ChatMessage.MessageRole;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatResponse;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.service.OpenAiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Refactored AI Chat Service - Business Logic Only
 *
 * This service handles the core AI chat functionality and conversation flow.
 * Data communication is delegated to specialized tool function services:
 * - ChatMcpToolsFunctions: for chat-mcp backend communication
 * - WinMcpToolsFunctions: for win-mcp (WinPlus) backend communication
 */
@Service
public class AiChatService {

    private final OpenAiService openAiService;
    private final ChatMcpToolsFunctions chatMcpTools;
    private final WinMcpToolsFunctions winMcpTools;

    @Value("${openai.model}")
    private String model;

    @Value("${openai.temperature}")
    private float temperature;

    @Value("${openai.max-tokens}")
    private int maxTokens;

    @Autowired
    public AiChatService(OpenAiService openAiService,
                        ChatMcpToolsFunctions chatMcpTools,
                        WinMcpToolsFunctions winMcpTools) {
        this.openAiService = openAiService;
        this.chatMcpTools = chatMcpTools;
        this.winMcpTools = winMcpTools;
    }

    // Map to track conversation state for each user
    private final Map<String, ConversationState> conversationStates = new HashMap<>();

    // Map to store the last question for each conversation
    private final Map<String, String> lastQuestions = new HashMap<>();

    // Map to store the selected backend for each conversation
    private final Map<String, String> selectedBackends = new HashMap<>();

    // Enum to track the state of a conversation
    private enum ConversationState {
        INITIAL,                // Initial state
        AWAITING_BACKEND,       // Waiting for user to choose backend (chat-mcp or win-mcp)
        AWAITING_SOURCE,        // Waiting for user to choose a source (1 or 2)
        USING_DATABASE,         // Using database information
        USING_GENERAL           // Using general knowledge
    }

    /**
     * Processes a chat request and generates a response using the AI model.
     * Now supports both chat-mcp and win-mcp backends.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response
     */
    public Mono<ChatResponse> processChat(ChatRequest chatRequest) {
        String userMessage = chatRequest.getContent();
        String conversationId = chatRequest.getConversationId();
        String username = chatRequest.getUsername();

        // Get or create conversation state
        ConversationState state = conversationStates.getOrDefault(conversationId, ConversationState.INITIAL);

        // Create the system prompt based on the current state
        String systemPromptText = createSystemPrompt();

        // Debug logging
        System.out.println("Processing chat: conversationId=" + conversationId + ", state=" + state + ", message=" + userMessage);

        // Handle different conversation states
        if (state == ConversationState.AWAITING_BACKEND) {
            if (userMessage.trim().equals("1")) {
                // User chose chat-mcp backend
                selectedBackends.put(conversationId, "chat-mcp");
                conversationStates.put(conversationId, ConversationState.AWAITING_SOURCE);
                return generateSourceSelectionResponse(conversationId, username);
            } else if (userMessage.trim().equals("2")) {
                // User chose win-mcp backend
                selectedBackends.put(conversationId, "win-mcp");
                conversationStates.put(conversationId, ConversationState.AWAITING_SOURCE);
                return generateSourceSelectionResponse(conversationId, username);
            }
        }

        if (state == ConversationState.AWAITING_SOURCE) {
            if (userMessage.trim().equals("1")) {
                // User chose database source
                conversationStates.put(conversationId, ConversationState.INITIAL);
                String lastQuestion = lastQuestions.getOrDefault(conversationId, "");
                String selectedBackend = selectedBackends.getOrDefault(conversationId, "chat-mcp");
                System.out.println("User chose database source for question: " + lastQuestion + " using backend: " + selectedBackend);
                return generateDatabaseResponse(lastQuestion, conversationId, username, systemPromptText, selectedBackend);
            } else if (userMessage.trim().equals("2")) {
                // User chose general knowledge
                conversationStates.put(conversationId, ConversationState.INITIAL);
                String lastQuestion = lastQuestions.getOrDefault(conversationId, "");
                System.out.println("User chose general knowledge for question: " + lastQuestion);
                return generateGeneralKnowledgeResponse(lastQuestion, conversationId, username, systemPromptText);
            }
        }

        // For all other cases, let the AI decide if this is a greeting or a question
        return determineMessageTypeAndRespond(userMessage, conversationId, username, systemPromptText);
    }

    /**
     * Creates the system prompt for the AI.
     *
     * @return The system prompt template
     */
    private String createSystemPrompt() {
        return """
                Tu es un assistant intelligent pour une application de gestion de pharmacie appelée WinPlusPharma.
                Tu réponds toujours en français, de manière professionnelle, claire et concise.

                🎯 Ton rôle est d'aider l'utilisateur à retrouver des informations depuis deux systèmes :
                1. Chat-MCP (système de démonstration)
                2. WinPlus-MCP (système de simulation réel)

                ---

                🧠 Si l'utilisateur envoie un message **amical ou introductif** (ex. : "Bonjour", "Salut", "Hi", "Comment ça va ?"),
                réponds simplement de façon amicale et naturelle.

                ---

                ❓ Si l'utilisateur pose une **véritable question**, demande-lui de choisir le système et la source :

                > "Choisissez le système à utiliser :
                > 1️⃣ Chat-MCP (système de démonstration)
                > 2️⃣ WinPlus-MCP (système de simulation réel)
                > Répondez par **1** ou **2**."

                Puis demande la source de données :
                > "Souhaitez-vous que je vous réponde en utilisant :
                > 1️⃣ La base de données (pour vos informations personnelles)
                > 2️⃣ Mes connaissances générales publiques ?
                > Répondez par **1** ou **2**."

                ---

                📂 Si l'utilisateur choisit la base de données :
                - Utilise uniquement les données du système sélectionné
                - Si aucune information n'est trouvée, réponds : "Je n'ai pas trouvé la réponse dans la base de données."

                🌍 Si l'utilisateur choisit les connaissances générales :
                - Utilise uniquement ton savoir GPT
                - Ne fais aucune référence aux bases de données

                🚫 Ne combine jamais les deux sources dans une même réponse.
                """;
    }

    /**
     * Determines if a message is a greeting or a question and responds accordingly.
     */
    private Mono<ChatResponse> determineMessageTypeAndRespond(String userMessage, String conversationId,
                                                           String username, String systemMessage) {
        // Check for common greeting patterns
        if (isGreeting(userMessage.toLowerCase().trim())) {
            System.out.println("Detected greeting message: " + userMessage);
            return generateGreetingResponse(userMessage, conversationId, username, systemMessage);
        }

        // Use AI to determine if it's a greeting or question
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system",
            "Détermine si le message est un simple salut amical ou une vraie question. " +
            "Réponds 'GREETING' pour un salut, 'QUESTION' pour une question."));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0)
                .maxTokens(10)
                .build();

        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent().trim().toUpperCase();

        if (responseContent.contains("GREETING")) {
            return generateGreetingResponse(userMessage, conversationId, username, systemMessage);
        } else {
            // For questions, ask user to choose backend first
            lastQuestions.put(conversationId, userMessage);
            conversationStates.put(conversationId, ConversationState.AWAITING_BACKEND);
            return generateBackendSelectionResponse(conversationId, username);
        }
    }

    /**
     * Checks if a message is a common greeting.
     *
     * @param message The message to check (lowercase)
     * @return True if the message is a greeting, false otherwise
     */
    private boolean isGreeting(String message) {
        // Common greetings in English and French
        String[] greetings = {
            // English greetings
            "hello", "hi", "hey", "how are you", "how's it going", "what's up", "good morning", "good afternoon", "good evening",
            "i'm fine", "i am fine", "i'm good", "i am good", "i'm great", "i am great", "fine thanks", "good thanks",
            "thank you", "thanks", "nice to meet you", "pleased to meet you", "good to see you",

            // French greetings
            "bonjour", "salut", "coucou", "comment ça va", "comment vas-tu", "ça va", "bonsoir", "je vais bien",
            "je suis bien", "bien merci", "merci", "enchanté", "ravi de vous rencontrer", "content de vous voir"
        };

        for (String greeting : greetings) {
            if (message.equals(greeting) ||
                message.startsWith(greeting + " ") ||
                message.endsWith(" " + greeting) ||
                message.contains(" " + greeting + " ")) {
                return true;
            }
        }

        // Check for common greeting patterns
        if (message.contains("i'm") && (message.contains("fine") || message.contains("good") || message.contains("great"))) {
            return true;
        }

        if (message.contains("je suis") && (message.contains("bien") || message.contains("bon"))) {
            return true;
        }

        return false;
    }

    /**
     * Generates a response for greeting messages.
     */
    private Mono<ChatResponse> generateGreetingResponse(String userMessage, String conversationId,
                                                       String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a response asking the user to select a backend.
     */
    private Mono<ChatResponse> generateBackendSelectionResponse(String conversationId, String username) {
        String responseContent = """
                Choisissez le système à utiliser :
                1️⃣ Chat-MCP (système de démonstration)
                2️⃣ WinPlus-MCP (système de simulation réel)
                Répondez simplement par **1** ou **2**.
                """;

        return createChatResponse(conversationId, responseContent);
    }

    /**
     * Generates a response asking the user to select a source.
     */
    private Mono<ChatResponse> generateSourceSelectionResponse(String conversationId, String username) {
        String responseContent = """
                Souhaitez-vous que je vous réponde en utilisant :
                1️⃣ La base de données (pour vos informations personnelles)
                2️⃣ Mes connaissances générales publiques ?
                Répondez simplement par **1** ou **2**.
                """;

        return createChatResponse(conversationId, responseContent);
    }

    /**
     * Generates a response using the database.
     */
    private Mono<ChatResponse> generateDatabaseResponse(String userMessage, String conversationId,
                                                      String username, String systemMessage, String selectedBackend) {
        try {
            String userData;

            if ("win-mcp".equals(selectedBackend)) {
                // Use WinPlus tools
                userData = getWinPlusData(username, userMessage);
            } else {
                // Use Chat-MCP tools (default)
                userData = getChatMcpData(username, userMessage);
            }

            return generateOpenAIResponse(userMessage, conversationId, userData, systemMessage);

        } catch (Exception e) {
            System.out.println("Error in generateDatabaseResponse: " + e.getMessage());
            e.printStackTrace();
            String errorMessage = "Erreur lors de la récupération des données depuis " +
                                (selectedBackend.equals("win-mcp") ? "WinPlus" : "Chat-MCP") + ": " + e.getMessage();
            return createChatResponse(conversationId, errorMessage);
        }
    }

    /**
     * Gets data from WinPlus backend using tool functions
     */
    private String getWinPlusData(String username, String userMessage) {
        String message = userMessage.toLowerCase();

        // Determine which WinPlus tool to use based on the message content
        if (message.contains("client") || message.contains("profil") || message.contains("info")) {
            return winMcpTools.getWinPlusUserProfileTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("vente") || message.contains("sale") || message.contains("achat")) {
            return winMcpTools.getWinPlusSalesTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("produit") || message.contains("product") || message.contains("médicament")) {
            return winMcpTools.getWinPlusProductsTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("achat") || message.contains("purchase") || message.contains("facture")) {
            return winMcpTools.getWinPlusPurchasesTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("dashboard") || message.contains("tableau") || message.contains("statistique")) {
            return winMcpTools.getWinPlusDashboardTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else {
            // Default to user profile
            return winMcpTools.getWinPlusUserProfileTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        }
    }

    /**
     * Gets data from Chat-MCP backend using tool functions
     */
    private String getChatMcpData(String username, String userMessage) {
        String message = userMessage.toLowerCase();

        // Determine which Chat-MCP tool to use based on the message content
        if (message.contains("facture") || message.contains("invoice")) {
            return chatMcpTools.getChatMcpUserInvoicesTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserInvoicesRequest(username, 10));
        } else if (message.contains("transaction") || message.contains("paiement")) {
            return chatMcpTools.getChatMcpUserTransactionsTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("commande") || message.contains("order")) {
            return chatMcpTools.getChatMcpUserOrdersTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else {
            // Default to user profile
            return chatMcpTools.getChatMcpUserProfileTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        }
    }

    /**
     * Creates a simple chat response
     */
    private Mono<ChatResponse> createChatResponse(String conversationId, String content) {
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(content)
                .timestamp(LocalDateTime.now())
                .build();

        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates OpenAI response with user data
     */
    private Mono<ChatResponse> generateOpenAIResponse(String userMessage, String conversationId,
                                                     String userData, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Voici les informations disponibles:\n" + userData + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT ces informations."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        return createChatResponse(conversationId, responseContent);
    }

    /**
     * Generates a response using general knowledge.
     */
    private Mono<ChatResponse> generateGeneralKnowledgeResponse(String userMessage, String conversationId,
                                                              String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT tes connaissances générales. " +
                "Ne fais AUCUNE supposition à partir des bases de données."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        return createChatResponse(conversationId, responseContent);
    }
}

    /**
     * Generates OpenAI response with user data
     */
    private Mono<ChatResponse> generateOpenAIResponse(String userMessage, String conversationId,
                                                     String userData, String systemMessage) {

        // Create a specific system message for database mode
        String databaseSystemMessage = "Tu es un assistant intelligent qui aide les utilisateurs à accéder à leurs informations personnelles " +
                "depuis la base de données MCP. Réponds toujours en français de manière claire et concise. " +
                "Tu as accès aux informations suivantes sur l'utilisateur: nom complet, adresse email, rôle, " +
                "pharmacie, adresse, numéro de téléphone, statut d'abonnement, date d'expiration de l'abonnement, " +
                "date de début d'abonnement, type d'abonnement, prix d'abonnement, dernière commande, historique des commandes, " +
                "nombre total de commandes, valeur moyenne des commandes, date de création du compte, dernière connexion, " +
                "statut du compte, type de compte, solde actuel, prochaine échéance de paiement, méthode de paiement, " +
                "date d'expiration de la carte, historique des factures. " +
                "Si tu ne peux pas trouver l'information demandée, dis simplement: " +
                "\"Je n'ai pas trouvé cette information dans la base de données MCP.\"";

        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", databaseSystemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Voici les informations de l'utilisateur depuis la base de données MCP:\n" + userData + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT ces informations. " +
                "Sois précis et concis dans ta réponse. " +
                "Si la question est 'Quel est mon nom complet?', réponds avec le nom complet de l'utilisateur. " +
                "Si la question est 'Quelle est mon adresse email?', réponds avec l'adresse email de l'utilisateur. " +
                "Si la question est 'Quel est mon rôle?', réponds avec le rôle de l'utilisateur. " +
                "Si la question est 'Quelle est ma pharmacie?', réponds avec la pharmacie de l'utilisateur. " +
                "Si la question est 'Quelle est mon adresse?', réponds avec l'adresse de l'utilisateur. " +
                "Si la question est 'Quel est mon statut d'abonnement?', réponds avec le statut d'abonnement de l'utilisateur. " +
                "Si la question est 'Quand expire mon abonnement?', réponds avec la date d'expiration de l'abonnement de l'utilisateur. " +
                "Si la question est 'Quelle est ma dernière commande?', réponds avec la dernière commande de l'utilisateur. " +
                "Si la question est 'Quelles sont mes commandes précédentes?', réponds avec l'historique des commandes de l'utilisateur. " +
                "Si tu ne trouves pas la réponse dans ces données, dis simplement: " +
                "\"Je n'ai pas trouvé la réponse dans la base de données MCP.\""));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0) // Use a deterministic temperature for database queries
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a response using general knowledge.
     */
    private Mono<ChatResponse> generateGeneralKnowledgeResponse(String userMessage, String conversationId,
                                                              String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT tes connaissances générales. " +
                "Ne fais AUCUNE supposition à partir de la base de données MCP."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a prompt response for general knowledge mode.
     */
    private Mono<ChatResponse> generateGeneralKnowledgePromptResponse(String conversationId, String username, String systemMessage) {
        String responseContent = "Bien sûr, je suis prêt à répondre en utilisant uniquement mes connaissances générales. Quelle question aimeriez-vous poser ?";

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Gets weather information for a location.
     *
     * @param location The location to get weather for
     * @return The weather information
     */
    public String getWeather(String location) {
        // This is a mock implementation
        return "The weather in " + location + " is currently sunny with a temperature of 25°C.";
    }

    /**
     * Gets user information.
     * This tool fetches user information from the MCP database.
     *
     * @param username The username to get information for
     * @return The user information
     */
    public String getUserInfo(String username) {
        try {
            return chatMcpApiService.getUserData(username)
                    .map(response -> formatUserData(response))
                    .doOnError(error -> {
                        System.out.println("Error in getUserInfo: " + error.getMessage());
                        error.printStackTrace();
                    })
                    .onErrorReturn("Erreur lors de la récupération des informations de l'utilisateur depuis la base de données MCP: " + username)
                    .subscribeOn(Schedulers.boundedElastic())
                    .blockOptional()
                    .orElse("Aucune information trouvée pour l'utilisateur " + username);
        } catch (Exception e) {
            System.out.println("Exception in getUserInfo: " + e.getMessage());
            e.printStackTrace();
            return "Erreur lors de la récupération des informations de l'utilisateur: " + e.getMessage();
        }
    }



    /**
     * Formats user data for display.
     *
     * @param userData The user data to format
     * @return The formatted user data
     */
    @SuppressWarnings("unchecked")
    private String formatUserData(Object userData) {
        if (userData == null) {
            System.out.println("formatUserData: userData is null");
            return "Aucune donnée utilisateur disponible";
        }

        System.out.println("formatUserData: userData class = " + userData.getClass().getName());

        try {
            // Try to cast to Map if it's a map
            if (userData instanceof Map) {
                Map<String, Object> userDataMap = (Map<String, Object>) userData;
                System.out.println("formatUserData: userDataMap keys = " + userDataMap.keySet());
                StringBuilder sb = new StringBuilder();

                // Format user data in a readable way
                sb.append("Informations de l'utilisateur:\n\n");

                // Process basic user info first
                String[] priorityFields = {"username", "fullName", "email", "role", "pharmacy", "address", "phoneNumber"};
                for (String field : priorityFields) {
                    if (userDataMap.containsKey(field)) {
                        sb.append("- ").append(formatFieldName(field)).append(": ")
                          .append(userDataMap.get(field)).append("\n");
                    }
                }

                sb.append("\n");

                // Process subscription info
                if (userDataMap.containsKey("subscriptionStatus")) {
                    sb.append("Informations d'abonnement:\n");
                    sb.append("- Statut: ").append(userDataMap.get("subscriptionStatus")).append("\n");

                    if (userDataMap.containsKey("subscriptionExpiry")) {
                        sb.append("- Date d'expiration: ").append(userDataMap.get("subscriptionExpiry")).append("\n");
                    }

                    if (userDataMap.containsKey("subscriptionStartDate")) {
                        sb.append("- Date de début: ").append(userDataMap.get("subscriptionStartDate")).append("\n");
                    }

                    if (userDataMap.containsKey("subscriptionType")) {
                        sb.append("- Type d'abonnement: ").append(userDataMap.get("subscriptionType")).append("\n");
                    }

                    if (userDataMap.containsKey("subscriptionPrice")) {
                        sb.append("- Prix: ").append(userDataMap.get("subscriptionPrice")).append("\n");
                    }

                    sb.append("\n");
                }

                // Process order history
                if (userDataMap.containsKey("lastOrder") || userDataMap.containsKey("orderHistory")) {
                    sb.append("Historique des commandes:\n");

                    if (userDataMap.containsKey("lastOrder")) {
                        sb.append("- Dernière commande: ").append(userDataMap.get("lastOrder")).append("\n");
                    }

                    if (userDataMap.containsKey("orderHistory")) {
                        sb.append("- Commandes précédentes:\n");
                        List<String> orderHistory = (List<String>) userDataMap.get("orderHistory");
                        for (String order : orderHistory) {
                            sb.append("  * ").append(order).append("\n");
                        }
                    }

                    if (userDataMap.containsKey("totalOrders")) {
                        sb.append("- Nombre total de commandes: ").append(userDataMap.get("totalOrders")).append("\n");
                    }

                    if (userDataMap.containsKey("averageOrderValue")) {
                        sb.append("- Valeur moyenne des commandes: ").append(userDataMap.get("averageOrderValue")).append("\n");
                    }

                    sb.append("\n");
                }

                // Process account information
                if (userDataMap.containsKey("accountCreationDate") || userDataMap.containsKey("accountStatus") ||
                    userDataMap.containsKey("accountType") || userDataMap.containsKey("lastLogin")) {
                    sb.append("Informations du compte:\n");

                    if (userDataMap.containsKey("accountCreationDate")) {
                        sb.append("- Date de création: ").append(userDataMap.get("accountCreationDate")).append("\n");
                    }

                    if (userDataMap.containsKey("accountStatus")) {
                        sb.append("- Statut du compte: ").append(userDataMap.get("accountStatus")).append("\n");
                    }

                    if (userDataMap.containsKey("accountType")) {
                        sb.append("- Type de compte: ").append(userDataMap.get("accountType")).append("\n");
                    }

                    if (userDataMap.containsKey("lastLogin")) {
                        sb.append("- Dernière connexion: ").append(userDataMap.get("lastLogin")).append("\n");
                    }

                    sb.append("\n");
                }

                // Process financial information
                if (userDataMap.containsKey("currentBalance") || userDataMap.containsKey("nextPaymentDue") ||
                    userDataMap.containsKey("paymentMethod") || userDataMap.containsKey("cardExpiryDate") ||
                    userDataMap.containsKey("invoiceHistory")) {
                    sb.append("Informations financières:\n");

                    if (userDataMap.containsKey("currentBalance")) {
                        sb.append("- Solde actuel: ").append(userDataMap.get("currentBalance")).append("\n");
                    }

                    if (userDataMap.containsKey("nextPaymentDue")) {
                        sb.append("- Prochaine échéance: ").append(userDataMap.get("nextPaymentDue")).append("\n");
                    }

                    if (userDataMap.containsKey("paymentMethod")) {
                        sb.append("- Méthode de paiement: ").append(userDataMap.get("paymentMethod")).append("\n");
                    }

                    if (userDataMap.containsKey("cardExpiryDate")) {
                        sb.append("- Date d'expiration de la carte: ").append(userDataMap.get("cardExpiryDate")).append("\n");
                    }

                    if (userDataMap.containsKey("invoiceHistory")) {
                        sb.append("- Historique des factures:\n");
                        List<String> invoiceHistory = (List<String>) userDataMap.get("invoiceHistory");
                        for (String invoice : invoiceHistory) {
                            sb.append("  * ").append(invoice).append("\n");
                        }
                    }

                    sb.append("\n");
                }

                // Process permissions for admin users
                if (userDataMap.containsKey("permissions")) {
                    sb.append("Permissions:\n");
                    List<String> permissions = (List<String>) userDataMap.get("permissions");
                    for (String permission : permissions) {
                        sb.append("- ").append(permission).append("\n");
                    }

                    sb.append("\n");
                }

                // Process any remaining fields
                sb.append("Autres informations:\n");
                for (Map.Entry<String, Object> entry : userDataMap.entrySet()) {
                    String key = entry.getKey();

                    // Skip fields we've already processed
                    if (contains(priorityFields, key) ||
                        key.equals("subscriptionStatus") ||
                        key.equals("subscriptionExpiry") ||
                        key.equals("subscriptionStartDate") ||
                        key.equals("subscriptionType") ||
                        key.equals("subscriptionPrice") ||
                        key.equals("lastOrder") ||
                        key.equals("orderHistory") ||
                        key.equals("totalOrders") ||
                        key.equals("averageOrderValue") ||
                        key.equals("accountCreationDate") ||
                        key.equals("accountStatus") ||
                        key.equals("accountType") ||
                        key.equals("lastLogin") ||
                        key.equals("currentBalance") ||
                        key.equals("nextPaymentDue") ||
                        key.equals("paymentMethod") ||
                        key.equals("cardExpiryDate") ||
                        key.equals("invoiceHistory") ||
                        key.equals("permissions")) {
                        continue;
                    }

                    Object value = entry.getValue();
                    if (!(value instanceof List)) {
                        sb.append("- ").append(formatFieldName(key)).append(": ")
                          .append(value).append("\n");
                    }
                }

                return sb.toString();
            } else {
                // If it's not a map, just return the string representation
                return userData.toString();
            }
        } catch (Exception e) {
            return "Erreur lors du formatage des données utilisateur: " + e.getMessage();
        }
    }

    /**
     * Formats a field name for display.
     *
     * @param fieldName The field name to format
     * @return The formatted field name
     */
    private String formatFieldName(String fieldName) {
        // Convert camelCase to Title Case with spaces
        String result = fieldName.replaceAll("([a-z])([A-Z])", "$1 $2");
        // Capitalize first letter
        return Character.toUpperCase(result.charAt(0)) + result.substring(1);
    }

    /**
     * Checks if an array contains a value.
     *
     * @param array The array to check
     * @param value The value to look for
     * @return True if the array contains the value, false otherwise
     */
    private boolean contains(String[] array, String value) {
        for (String item : array) {
            if (item.equals(value)) {
                return true;
            }
        }
        return false;
    }

    // ========== SPRING AI TOOL FUNCTIONS ==========

    /**
     * Tool function to get real user profile data
     */
    @Description("Get the real user profile information from the database")
    public Function<UserProfileRequest, String> getUserProfileTool() {
        return request -> {
            try {
                // Create WebClient for calling the real data API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first
                String token = chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (token == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Call the real data API
                Map<String, Object> profileData = webClient.get()
                        .uri("/real-data/profile")
                        .header("Authorization", "Bearer " + token)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (profileData == null) {
                    return "Aucune donnée de profil trouvée pour l'utilisateur: " + request.username();
                }

                return formatUserProfileData(profileData);

            } catch (Exception e) {
                System.out.println("Error in getUserProfileTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération du profil utilisateur: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get real user invoices
     */
    @Description("Get the real user invoice history from the database")
    public Function<UserInvoicesRequest, String> getUserInvoicesTool() {
        return request -> {
            try {
                // Create WebClient for calling the real data API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first
                String token = chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (token == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Call the real data API
                List<Map<String, Object>> invoices = webClient.get()
                        .uri("/real-data/invoices?limit=" + request.limit())
                        .header("Authorization", "Bearer " + token)
                        .retrieve()
                        .bodyToMono(List.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (invoices == null || invoices.isEmpty()) {
                    return "Aucune facture trouvée pour l'utilisateur: " + request.username();
                }

                return formatInvoiceData(invoices);

            } catch (Exception e) {
                System.out.println("Error in getUserInvoicesTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des factures: " + e.getMessage();
            }
        };
    }

    // Helper methods for formatting data
    private String formatUserProfileData(Map<String, Object> profileData) {
        StringBuilder formatted = new StringBuilder();

        if (profileData.containsKey("fullName")) {
            formatted.append("Nom complet: ").append(profileData.get("fullName")).append("\n");
        }
        if (profileData.containsKey("email")) {
            formatted.append("Email: ").append(profileData.get("email")).append("\n");
        }
        if (profileData.containsKey("currentBalance")) {
            formatted.append("Solde actuel: ").append(profileData.get("currentBalance")).append("€\n");
        }
        if (profileData.containsKey("company")) {
            formatted.append("Entreprise: ").append(profileData.get("company")).append("\n");
        }
        if (profileData.containsKey("jobTitle")) {
            formatted.append("Poste: ").append(profileData.get("jobTitle")).append("\n");
        }
        if (profileData.containsKey("address")) {
            formatted.append("Adresse: ").append(profileData.get("address")).append("\n");
        }
        if (profileData.containsKey("subscriptionStatus")) {
            formatted.append("Statut d'abonnement: ").append(profileData.get("subscriptionStatus")).append("\n");
        }

        return formatted.toString();
    }

    private String formatInvoiceData(List<Map<String, Object>> invoices) {
        StringBuilder formatted = new StringBuilder("Historique des factures:\n");

        for (Map<String, Object> invoice : invoices) {
            formatted.append("- ");
            if (invoice.containsKey("invoiceNumber")) {
                formatted.append("Facture ").append(invoice.get("invoiceNumber"));
            }
            if (invoice.containsKey("totalAmount")) {
                formatted.append(" - ").append(invoice.get("totalAmount")).append("€");
            }
            if (invoice.containsKey("invoiceDate")) {
                formatted.append(" - ").append(invoice.get("invoiceDate"));
            }
            if (invoice.containsKey("status")) {
                formatted.append(" - ").append(invoice.get("status"));
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    /**
     * Formats invoice list for display
     */
    @SuppressWarnings("unchecked")
    private String formatInvoiceList(List<Map<String, Object>> invoices) {
        if (invoices == null || invoices.isEmpty()) {
            return "Aucune facture trouvée.";
        }

        StringBuilder formatted = new StringBuilder("Vos factures:\n\n");

        for (Map<String, Object> invoice : invoices) {
            formatted.append("📄 Facture ");
            if (invoice.containsKey("invoiceNumber")) {
                formatted.append(invoice.get("invoiceNumber"));
            }
            formatted.append("\n");

            if (invoice.containsKey("invoiceDate")) {
                formatted.append("   Date: ").append(invoice.get("invoiceDate")).append("\n");
            }
            if (invoice.containsKey("totalAmount")) {
                formatted.append("   Montant: ").append(invoice.get("totalAmount")).append("€\n");
            }
            if (invoice.containsKey("status")) {
                formatted.append("   Statut: ").append(invoice.get("status")).append("\n");
            }
            if (invoice.containsKey("paymentStatus")) {
                formatted.append("   Paiement: ").append(invoice.get("paymentStatus")).append("\n");
            }
            if (invoice.containsKey("dueDate")) {
                formatted.append("   Échéance: ").append(invoice.get("dueDate")).append("\n");
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    /**
     * Formats transaction list for display
     */
    @SuppressWarnings("unchecked")
    private String formatTransactionList(List<Map<String, Object>> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            return "Aucune transaction trouvée.";
        }

        StringBuilder formatted = new StringBuilder("Vos transactions récentes:\n\n");

        for (Map<String, Object> transaction : transactions) {
            formatted.append("💳 Transaction ");
            if (transaction.containsKey("transactionId")) {
                formatted.append(transaction.get("transactionId"));
            }
            formatted.append("\n");

            if (transaction.containsKey("createdAt")) {
                formatted.append("   Date: ").append(transaction.get("createdAt")).append("\n");
            }
            if (transaction.containsKey("amount")) {
                formatted.append("   Montant: ").append(transaction.get("amount")).append("€\n");
            }
            if (transaction.containsKey("transactionType")) {
                formatted.append("   Type: ").append(transaction.get("transactionType")).append("\n");
            }
            if (transaction.containsKey("description")) {
                formatted.append("   Description: ").append(transaction.get("description")).append("\n");
            }
            if (transaction.containsKey("merchantName")) {
                formatted.append("   Marchand: ").append(transaction.get("merchantName")).append("\n");
            }
            if (transaction.containsKey("status")) {
                formatted.append("   Statut: ").append(transaction.get("status")).append("\n");
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    /**
     * Formats order list for display
     */
    @SuppressWarnings("unchecked")
    private String formatOrderList(List<Map<String, Object>> orders) {
        if (orders == null || orders.isEmpty()) {
            return "Aucune commande trouvée.";
        }

        StringBuilder formatted = new StringBuilder("Vos commandes:\n\n");

        for (Map<String, Object> order : orders) {
            formatted.append("📦 Commande ");
            if (order.containsKey("orderNumber")) {
                formatted.append(order.get("orderNumber"));
            }
            formatted.append("\n");

            if (order.containsKey("orderDate")) {
                formatted.append("   Date: ").append(order.get("orderDate")).append("\n");
            }
            if (order.containsKey("totalAmount")) {
                formatted.append("   Montant: ").append(order.get("totalAmount")).append("€\n");
            }
            if (order.containsKey("status")) {
                formatted.append("   Statut: ").append(order.get("status")).append("\n");
            }
            if (order.containsKey("itemsDescription")) {
                formatted.append("   Articles: ").append(order.get("itemsDescription")).append("\n");
            }
            if (order.containsKey("trackingNumber")) {
                formatted.append("   Suivi: ").append(order.get("trackingNumber")).append("\n");
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    // Request record classes for tool functions
    public record UserProfileRequest(String username) {}
    public record UserInvoicesRequest(String username, int limit) {}
}
