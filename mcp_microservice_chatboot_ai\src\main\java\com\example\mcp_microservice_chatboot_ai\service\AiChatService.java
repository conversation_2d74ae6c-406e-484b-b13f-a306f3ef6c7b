package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.ChatMessage;
import com.example.mcp_microservice_chatboot_ai.model.ChatMessage.MessageRole;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatResponse;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.service.OpenAiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Refactored AI Chat Service - Business Logic Only
 *
 * This service handles the core AI chat functionality and conversation flow.
 * Data communication is delegated to specialized tool function services:
 * - ChatMcpToolsFunctions: for chat-mcp backend communication
 * - WinMcpToolsFunctions: for win-mcp (WinPlus) backend communication
 */
@Service
public class AiChatService {

    private final OpenAiService openAiService;
    private final ChatMcpToolsFunctions chatMcpTools;
    private final WinMcpToolsFunctions winMcpTools;

    @Value("${openai.model}")
    private String model;

    @Value("${openai.temperature}")
    private float temperature;

    @Value("${openai.max-tokens}")
    private int maxTokens;

    @Value("${mcp.backend.type}")
    private String backendType;

    @Autowired
    public AiChatService(OpenAiService openAiService,
                        ChatMcpToolsFunctions chatMcpTools,
                        WinMcpToolsFunctions winMcpTools) {
        this.openAiService = openAiService;
        this.chatMcpTools = chatMcpTools;
        this.winMcpTools = winMcpTools;
    }

    // Map to track conversation state for each user
    private final Map<String, ConversationState> conversationStates = new HashMap<>();

    // Map to store the last question for each conversation
    private final Map<String, String> lastQuestions = new HashMap<>();

    // Map to store the selected backend for each conversation
    private final Map<String, String> selectedBackends = new HashMap<>();

    // Enum to track the state of a conversation
    private enum ConversationState {
        INITIAL,                // Initial state
        AWAITING_BACKEND,       // Waiting for user to choose backend (chat-mcp or win-mcp)
        AWAITING_SOURCE,        // Waiting for user to choose a source (1 or 2)
        USING_DATABASE,         // Using database information
        USING_GENERAL           // Using general knowledge
    }

    /**
     * Processes a chat request and generates a response using the AI model.
     * Now supports both chat-mcp and win-mcp backends.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response
     */
    public Mono<ChatResponse> processChat(ChatRequest chatRequest) {
        String userMessage = chatRequest.getContent();
        String conversationId = chatRequest.getConversationId();
        String username = chatRequest.getUsername();

        // Get or create conversation state
        ConversationState state = conversationStates.getOrDefault(conversationId, ConversationState.INITIAL);

        // Create the system prompt based on the current state
        String systemPromptText = createSystemPrompt();

        // Debug logging
        System.out.println("Processing chat: conversationId=" + conversationId + ", state=" + state + ", message=" + userMessage);

        // Handle different conversation states

        if (state == ConversationState.AWAITING_SOURCE) {
            if (userMessage.trim().equals("1")) {
                // User chose database source
                conversationStates.put(conversationId, ConversationState.INITIAL);
                String lastQuestion = lastQuestions.getOrDefault(conversationId, "");
                String configuredBackend = getConfiguredBackend();
                System.out.println("User chose database source for question: " + lastQuestion + " using backend: " + configuredBackend);
                return generateDatabaseResponse(lastQuestion, conversationId, username, systemPromptText, configuredBackend);
            } else if (userMessage.trim().equals("2")) {
                // User chose general knowledge
                conversationStates.put(conversationId, ConversationState.INITIAL);
                String lastQuestion = lastQuestions.getOrDefault(conversationId, "");
                System.out.println("User chose general knowledge for question: " + lastQuestion);
                return generateGeneralKnowledgeResponse(lastQuestion, conversationId, username, systemPromptText);
            }
        }

        // For all other cases, let the AI decide if this is a greeting or a question
        return determineMessageTypeAndRespond(userMessage, conversationId, username, systemPromptText);
    }

    /**
     * Creates the system prompt for the AI.
     *
     * @return The system prompt template
     */
    private String createSystemPrompt() {
        String currentBackend = getConfiguredBackend();
        String backendName = "WIN_MCP".equals(backendType) ? "WinPlus-MCP (système de simulation réel)" : "Chat-MCP (système de démonstration)";

        return """
                Tu es un assistant intelligent pour une application de gestion de pharmacie appelée WinPlusPharma.
                Tu réponds toujours en français, de manière professionnelle, claire et concise.

                🎯 Ton rôle est d'aider l'utilisateur à retrouver des informations depuis le système : """ + backendName + """

                ---

                🧠 Si l'utilisateur envoie un message **amical ou introductif** (ex. : "Bonjour", "Salut", "Hi", "Comment ça va ?"),
                réponds simplement de façon amicale et naturelle.

                ---

                ❓ Si l'utilisateur pose une **véritable question**, demande-lui de choisir la source de données :

                > "Souhaitez-vous que je vous réponde en utilisant :
                > 1️⃣ La base de données (pour vos informations personnelles)
                > 2️⃣ Mes connaissances générales publiques ?
                > Répondez par **1** ou **2**."

                ---

                📂 Si l'utilisateur choisit la base de données :
                - Utilise uniquement les données du système configuré
                - Si aucune information n'est trouvée, réponds : "Je n'ai pas trouvé la réponse dans la base de données."

                🌍 Si l'utilisateur choisit les connaissances générales :
                - Utilise uniquement ton savoir GPT
                - Ne fais aucune référence aux bases de données

                🚫 Ne combine jamais les deux sources dans une même réponse.
                """;
    }

    /**
     * Determines if a message is a greeting or a question and responds accordingly.
     */
    private Mono<ChatResponse> determineMessageTypeAndRespond(String userMessage, String conversationId,
                                                           String username, String systemMessage) {
        // Check for common greeting patterns
        if (isGreeting(userMessage.toLowerCase().trim())) {
            System.out.println("Detected greeting message: " + userMessage);
            return generateGreetingResponse(userMessage, conversationId, username, systemMessage);
        }

        // Use AI to determine if it's a greeting or question with the original detailed prompt
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system",
            "Tu dois déterminer si le message de l'utilisateur est un simple message amical/introductif " +
            "ou s'il contient une véritable question ou demande d'information. " +
            "Les messages comme 'bonjour', 'salut', 'hello', 'hi', 'hey', 'comment ça va', 'comment vas-tu', " +
            "'how are you', 'ça va', 'bonsoir', 'je vais bien', 'i am fine', 'i am good', 'i am great', " +
            "'je suis bien', 'merci', 'thank you', etc. sont des messages amicaux. " +
            "Réponds uniquement par 'GREETING' pour un message amical sans question, " +
            "ou 'QUESTION' pour une question ou demande d'information."));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0)
                .maxTokens(10)
                .build();

        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent().trim().toUpperCase();

        if (responseContent.contains("GREETING")) {
            return generateGreetingResponse(userMessage, conversationId, username, systemMessage);
        } else {
            // For questions, use configured backend and ask for source selection
            lastQuestions.put(conversationId, userMessage);
            conversationStates.put(conversationId, ConversationState.AWAITING_SOURCE);
            return generateSourceSelectionResponse(conversationId, username);
        }
    }

    /**
     * Gets the configured backend from application.properties
     */
    private String getConfiguredBackend() {
        if ("WIN_MCP".equals(backendType)) {
            return "win-mcp";
        } else {
            return "chat-mcp"; // default
        }
    }

    /**
     * Checks if a message is a common greeting.
     *
     * @param message The message to check (lowercase)
     * @return True if the message is a greeting, false otherwise
     */
    private boolean isGreeting(String message) {
        // Common greetings in English and French
        String[] greetings = {
            // English greetings
            "hello", "hi", "hey", "how are you", "how's it going", "what's up", "good morning", "good afternoon", "good evening",
            "i'm fine", "i am fine", "i'm good", "i am good", "i'm great", "i am great", "fine thanks", "good thanks",
            "thank you", "thanks", "nice to meet you", "pleased to meet you", "good to see you",

            // French greetings
            "bonjour", "salut", "coucou", "comment ça va", "comment vas-tu", "ça va", "bonsoir", "je vais bien",
            "je suis bien", "bien merci", "merci", "enchanté", "ravi de vous rencontrer", "content de vous voir"
        };

        for (String greeting : greetings) {
            if (message.equals(greeting) ||
                message.startsWith(greeting + " ") ||
                message.endsWith(" " + greeting) ||
                message.contains(" " + greeting + " ")) {
                return true;
            }
        }

        // Check for common greeting patterns
        if (message.contains("i'm") && (message.contains("fine") || message.contains("good") || message.contains("great"))) {
            return true;
        }

        if (message.contains("je suis") && (message.contains("bien") || message.contains("bon"))) {
            return true;
        }

        return false;
    }

    /**
     * Generates a response for greeting messages.
     */
    private Mono<ChatResponse> generateGreetingResponse(String userMessage, String conversationId,
                                                       String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a response asking the user to select a source.
     */
    private Mono<ChatResponse> generateSourceSelectionResponse(String conversationId, String username) {
        String responseContent = """
                Souhaitez-vous que je vous réponde en utilisant :
                1️⃣ La base de données (pour vos informations personnelles)
                2️⃣ Mes connaissances générales publiques ?
                Répondez simplement par **1** ou **2**.
                """;

        return createChatResponse(conversationId, responseContent);
    }

    /**
     * Generates a response using the database (Reactive version).
     */
    private Mono<ChatResponse> generateDatabaseResponse(String userMessage, String conversationId,
                                                      String username, String systemMessage, String selectedBackend) {
        try {
            if ("win-mcp".equals(selectedBackend)) {
                // Use WinPlus tools (reactive)
                return getChatMcpDataReactive(username, userMessage)
                        .flatMap(userData -> generateOpenAIResponseReactive(userMessage, conversationId, userData, systemMessage))
                        .onErrorResume(e -> {
                            System.out.println("Error in generateDatabaseResponse (WinPlus): " + e.getMessage());
                            e.printStackTrace();
                            String errorMessage = "Erreur lors de la récupération des données depuis WinPlus: " + e.getMessage();
                            return createChatResponse(conversationId, errorMessage);
                        });
            } else {
                // Use Chat-MCP tools (reactive)
                return getChatMcpDataReactive(username, userMessage)
                        .flatMap(userData -> generateOpenAIResponseReactive(userMessage, conversationId, userData, systemMessage))
                        .onErrorResume(e -> {
                            System.out.println("Error in generateDatabaseResponse (Chat-MCP): " + e.getMessage());
                            e.printStackTrace();
                            String errorMessage = "Erreur lors de la récupération des données depuis Chat-MCP: " + e.getMessage();
                            return createChatResponse(conversationId, errorMessage);
                        });
            }

        } catch (Exception e) {
            System.out.println("Error in generateDatabaseResponse: " + e.getMessage());
            e.printStackTrace();
            String errorMessage = "Erreur lors de la récupération des données depuis " +
                                (selectedBackend.equals("win-mcp") ? "WinPlus" : "Chat-MCP") + ": " + e.getMessage();
            return createChatResponse(conversationId, errorMessage);
        }
    }

    /**
     * Gets data from WinPlus backend using tool functions
     */
    private String getWinPlusData(String username, String userMessage) {
        String message = userMessage.toLowerCase();

        // Determine which WinPlus tool to use based on the message content
        if (message.contains("client") || message.contains("profil") || message.contains("info")) {
            return winMcpTools.getWinPlusUserProfileTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("vente") || message.contains("sale") || message.contains("achat")) {
            return winMcpTools.getWinPlusSalesTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("produit") || message.contains("product") || message.contains("médicament")) {
            return winMcpTools.getWinPlusProductsTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("achat") || message.contains("purchase") || message.contains("facture")) {
            return winMcpTools.getWinPlusPurchasesTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("dashboard") || message.contains("tableau") || message.contains("statistique")) {
            return winMcpTools.getWinPlusDashboardTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else {
            // Default to user profile
            return winMcpTools.getWinPlusUserProfileTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        }
    }

    /**
     * Gets data from Chat-MCP backend using SMART AI-powered tool selection (Reactive version)
     */
    private Mono<String> getChatMcpDataReactive(String username, String userMessage) {
        // Use AI to intelligently determine what data is needed and fetch ALL relevant data
        return chatMcpTools.getSmartChatMcpDataToolReactive()
                .apply(new com.example.mcp_microservice_chatboot_ai.model.SmartDataRequest(username, userMessage));
    }

    /**
     * Gets data from Chat-MCP backend using tool functions (Blocking version - kept for compatibility)
     */
    private String getChatMcpData(String username, String userMessage) {
        String message = userMessage.toLowerCase();

        // Determine which Chat-MCP tool to use based on the message content
        if (message.contains("facture") || message.contains("invoice")) {
            return chatMcpTools.getChatMcpUserInvoicesTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserInvoicesRequest(username, 10));
        } else if (message.contains("transaction") || message.contains("paiement")) {
            return chatMcpTools.getChatMcpUserTransactionsTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else if (message.contains("commande") || message.contains("order")) {
            return chatMcpTools.getChatMcpUserOrdersTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        } else {
            // Default to user profile
            return chatMcpTools.getChatMcpUserProfileTool().apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
        }
    }

    /**
     * Creates a simple chat response
     */
    private Mono<ChatResponse> createChatResponse(String conversationId, String content) {
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(content)
                .timestamp(LocalDateTime.now())
                .build();

        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates OpenAI response with user data (Reactive version)
     */
    private Mono<ChatResponse> generateOpenAIResponseReactive(String userMessage, String conversationId,
                                                             String userData, String systemMessage) {
        return Mono.fromCallable(() -> {
            // Create chat messages for OpenAI
            List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
            messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
            messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                    "L'utilisateur a demandé: " + userMessage + "\n\n" +
                    "Voici les informations disponibles dans la base de données:\n" + userData + "\n\n" +
                    "INSTRUCTIONS IMPORTANTES:\n" +
                    "1. Réponds en français en utilisant UNIQUEMENT ces informations de la base de données.\n" +
                    "2. Si la question concerne des informations qui ne sont PAS dans la base de données (comme la géographie, l'histoire, les connaissances générales), réponds EXACTEMENT: \"Cette information n'est pas disponible dans la base de données. Veuillez choisir l'option 2 pour les connaissances générales.\"\n" +
                    "3. Ne demande JAMAIS à l'utilisateur de choisir entre les options 1 et 2.\n" +
                    "4. Si tu ne trouves pas la réponse dans les données fournies, dis clairement que l'information n'est pas disponible dans la base de données."));

            // Create the chat completion request
            ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .temperature(0.0)
                    .maxTokens(maxTokens)
                    .build();

            // Call the OpenAI API
            ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
            String responseContent = result.getChoices().get(0).getMessage().getContent();

            return responseContent;
        })
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(responseContent -> createChatResponse(conversationId, responseContent));
    }

    /**
     * Generates OpenAI response with user data (Blocking version - kept for compatibility)
     */
    private Mono<ChatResponse> generateOpenAIResponse(String userMessage, String conversationId,
                                                     String userData, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Voici les informations disponibles dans la base de données:\n" + userData + "\n\n" +
                "INSTRUCTIONS IMPORTANTES:\n" +
                "1. Réponds en français en utilisant UNIQUEMENT ces informations de la base de données.\n" +
                "2. Si la question concerne des informations qui ne sont PAS dans la base de données (comme la géographie, l'histoire, les connaissances générales), réponds EXACTEMENT: \"Cette information n'est pas disponible dans la base de données. Veuillez choisir l'option 2 pour les connaissances générales.\"\n" +
                "3. Ne demande JAMAIS à l'utilisateur de choisir entre les options 1 et 2.\n" +
                "4. Si tu ne trouves pas la réponse dans les données fournies, dis clairement que l'information n'est pas disponible dans la base de données."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        return createChatResponse(conversationId, responseContent);
    }

    /**
     * Generates a response using general knowledge.
     */
    private Mono<ChatResponse> generateGeneralKnowledgeResponse(String userMessage, String conversationId,
                                                              String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT tes connaissances générales. " +
                "Ne fais AUCUNE supposition à partir des bases de données."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        return createChatResponse(conversationId, responseContent);
    }
}
