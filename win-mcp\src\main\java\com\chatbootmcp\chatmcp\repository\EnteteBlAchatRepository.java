package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.EnteteBlAchat;
import com.chatbootmcp.chatmcp.entity.Fournisseur;
import com.chatbootmcp.chatmcp.entity.Depot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for EnteteBlAchat entity
 */
@Repository
public interface EnteteBlAchatRepository extends JpaRepository<EnteteBlAchat, Long> {
    
    List<EnteteBlAchat> findByFournisseur(Fournisseur fournisseur);
    
    List<EnteteBlAchat> findByDepot(Depot depot);
    
    @Query("SELECT e FROM EnteteBlAchat e WHERE e.dateCreation BETWEEN :startDate AND :endDate")
    List<EnteteBlAchat> findByDateCreationBetween(@Param("startDate") LocalDateTime startDate, 
                                                  @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT e FROM EnteteBlAchat e WHERE e.mntNetTtc >= :minAmount")
    List<EnteteBlAchat> findHighValuePurchases(@Param("minAmount") BigDecimal minAmount);
    
    @Query("SELECT SUM(e.mntNetTtc) FROM EnteteBlAchat e WHERE e.dateCreation BETWEEN :startDate AND :endDate")
    BigDecimal getTotalPurchaseAmount(@Param("startDate") LocalDateTime startDate, 
                                     @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT e FROM EnteteBlAchat e ORDER BY e.dateCreation DESC")
    List<EnteteBlAchat> findRecentPurchases();
}
