package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.UserInvoicesRequest;
import com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Service containing tool functions for communicating with chat-mcp backend
 * This service handles all interactions with the original chat-mcp APIs
 */
@Service
public class ChatMcpToolsFunctions {

    @Autowired
    private ChatMcpApiService chatMcpApiService;

    /**
     * Tool function to get real user profile data from chat-mcp (Reactive version)
     */
    public Function<UserProfileRequest, Mono<String>> getChatMcpUserProfileToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/users/data")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(Map.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(userData -> {
                                        if (userData == null) {
                                            return "Aucune donnée utilisateur trouvée pour: " + request.username();
                                        }
                                        return formatChatMcpUserData(userData);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserProfileToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * Tool function to get real user profile from chat-mcp (Non-blocking version)
     */
    public Function<UserProfileRequest, String> getChatMcpUserProfileTool() {
        return request -> {
            try {
                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Create WebClient for calling the chat-mcp API
                        WebClient webClient = WebClient.builder()
                                .baseUrl("http://localhost:8080")
                                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                                .build();

                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        Map<String, Object> userData = webClient.get()
                                .uri("/api/users/data")
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(Map.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (userData == null) {
                            return "Aucune donnée utilisateur trouvée pour: " + request.username();
                        }

                        return formatChatMcpUserData(userData);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserProfileTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserProfileTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get real user invoices from chat-mcp (Reactive version)
     */
    public Function<UserInvoicesRequest, Mono<String>> getChatMcpUserInvoicesToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/real-data/invoices?limit=" + request.limit())
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(invoices -> {
                                        if (invoices == null || invoices.isEmpty()) {
                                            return "Aucune facture trouvée pour l'utilisateur: " + request.username();
                                        }
                                        return formatChatMcpInvoiceData(invoices);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des factures depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserInvoicesToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des factures depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * Tool function to get real user invoices from chat-mcp (Blocking version)
     */
    public Function<UserInvoicesRequest, String> getChatMcpUserInvoicesTool() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        List<Map<String, Object>> invoices = webClient.get()
                                .uri("/api/real-data/invoices?limit=" + request.limit())
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(List.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (invoices == null || invoices.isEmpty()) {
                            return "Aucune facture trouvée pour l'utilisateur: " + request.username();
                        }

                        return formatChatMcpInvoiceData(invoices);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserInvoicesTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération des factures depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserInvoicesTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des factures depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get user transactions from chat-mcp
     */
    public Function<UserProfileRequest, String> getChatMcpUserTransactionsTool() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        List<Map<String, Object>> transactions = webClient.get()
                                .uri("/api/real-data/transactions?limit=10")
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(List.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (transactions == null || transactions.isEmpty()) {
                            return "Aucune transaction trouvée pour l'utilisateur: " + request.username();
                        }

                        return formatChatMcpTransactionData(transactions);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserTransactionsTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération des transactions depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserTransactionsTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des transactions depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get user orders from chat-mcp
     */
    public Function<UserProfileRequest, String> getChatMcpUserOrdersTool() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        List<Map<String, Object>> orders = webClient.get()
                                .uri("/api/real-data/orders?limit=10")
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(List.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (orders == null || orders.isEmpty()) {
                            return "Aucune commande trouvée pour l'utilisateur: " + request.username();
                        }

                        return formatChatMcpOrderData(orders);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserOrdersTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération des commandes depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserOrdersTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des commandes depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get user transactions from chat-mcp (Reactive version)
     */
    public Function<UserProfileRequest, Mono<String>> getChatMcpUserTransactionsToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/real-data/transactions?limit=10")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(transactions -> {
                                        if (transactions == null || transactions.isEmpty()) {
                                            return "Aucune transaction trouvée pour l'utilisateur: " + request.username();
                                        }
                                        return formatChatMcpTransactionData(transactions);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des transactions depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserTransactionsToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des transactions depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * Tool function to get user orders from chat-mcp (Reactive version)
     */
    public Function<UserProfileRequest, Mono<String>> getChatMcpUserOrdersToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/real-data/orders?limit=10")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(orders -> {
                                        if (orders == null || orders.isEmpty()) {
                                            return "Aucune commande trouvée pour l'utilisateur: " + request.username();
                                        }
                                        return formatChatMcpOrderData(orders);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des commandes depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserOrdersToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des commandes depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    // ==================== FORMATTING METHODS ====================

    private String formatChatMcpUserData(Map<String, Object> userData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== PROFIL UTILISATEUR (Chat-MCP) ===\n");

        if (userData.containsKey("user")) {
            Map<String, Object> user = (Map<String, Object>) userData.get("user");
            sb.append("Nom d'utilisateur: ").append(user.get("username")).append("\n");
            sb.append("Email: ").append(user.get("email")).append("\n");
            sb.append("Nom complet: ").append(user.get("fullName")).append("\n");
        }

        if (userData.containsKey("profile")) {
            Map<String, Object> profile = (Map<String, Object>) userData.get("profile");
            sb.append("Téléphone: ").append(profile.get("phone")).append("\n");
            sb.append("Adresse: ").append(profile.get("address")).append("\n");
            sb.append("Solde: ").append(profile.get("balance")).append(" DH\n");
        }

        return sb.toString();
    }

    private String formatChatMcpInvoiceData(List<Map<String, Object>> invoices) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== FACTURES (Chat-MCP) ===\n");

        for (Map<String, Object> invoice : invoices) {
            sb.append("Facture #").append(invoice.get("id")).append("\n");
            sb.append("  Date: ").append(invoice.get("date")).append("\n");
            sb.append("  Montant: ").append(invoice.get("amount")).append(" DH\n");
            sb.append("  Statut: ").append(invoice.get("status")).append("\n");
            sb.append("  Description: ").append(invoice.get("description")).append("\n\n");
        }

        return sb.toString();
    }

    private String formatChatMcpTransactionData(List<Map<String, Object>> transactions) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== TRANSACTIONS (Chat-MCP) ===\n");

        for (Map<String, Object> transaction : transactions) {
            sb.append("Transaction #").append(transaction.get("id")).append("\n");
            sb.append("  Date: ").append(transaction.get("date")).append("\n");
            sb.append("  Montant: ").append(transaction.get("amount")).append(" DH\n");
            sb.append("  Type: ").append(transaction.get("type")).append("\n");
            sb.append("  Description: ").append(transaction.get("description")).append("\n\n");
        }

        return sb.toString();
    }

    private String formatChatMcpOrderData(List<Map<String, Object>> orders) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== COMMANDES (Chat-MCP) ===\n");

        for (Map<String, Object> order : orders) {
            sb.append("Commande #").append(order.get("id")).append("\n");
            sb.append("  Date: ").append(order.get("date")).append("\n");
            sb.append("  Montant: ").append(order.get("amount")).append(" DH\n");
            sb.append("  Statut: ").append(order.get("status")).append("\n");
            sb.append("  Articles: ").append(order.get("items")).append("\n\n");
        }

        return sb.toString();
    }
}
