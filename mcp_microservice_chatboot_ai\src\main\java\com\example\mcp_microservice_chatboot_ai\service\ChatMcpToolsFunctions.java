package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.UserInvoicesRequest;
import com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.Duration;
import java.util.function.Function;

/**
 * Service containing tool functions for communicating with chat-mcp backend
 * This service handles all interactions with the original chat-mcp APIs
 */
@Service
public class ChatMcpToolsFunctions {

    @Autowired
    private ChatMcpApiService chatMcpApiService;

    /**
     * Tool function to get real user profile data from chat-mcp (Reactive version)
     */
    public Function<UserProfileRequest, Mono<String>> getChatMcpUserProfileToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .doOnNext(token -> System.out.println("DEBUG: Authentication token received: " + (token != null ? "SUCCESS" : "NULL")))
                        .flatMap(token -> {
                            if (token == null) {
                                System.out.println("DEBUG: Authentication failed for user: " + request.username());
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            System.out.println("DEBUG: Calling /api/users/data with token for user: " + request.username());
                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/users/data")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(Map.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .doOnNext(userData -> System.out.println("DEBUG: Received user data: " + (userData != null ? userData.toString() : "NULL")))
                                    .map(userData -> {
                                        if (userData == null) {
                                            System.out.println("DEBUG: No user data found for: " + request.username());
                                            return "Aucune donnée utilisateur trouvée pour: " + request.username();
                                        }
                                        String formattedData = formatChatMcpUserData(userData);
                                        System.out.println("DEBUG: Formatted user data: " + formattedData);
                                        return formattedData;
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserProfileToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * Tool function to get real user profile from chat-mcp (Non-blocking version)
     */
    public Function<UserProfileRequest, String> getChatMcpUserProfileTool() {
        return request -> {
            try {
                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Create WebClient for calling the chat-mcp API
                        WebClient webClient = WebClient.builder()
                                .baseUrl("http://localhost:8080")
                                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                                .build();

                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        Map<String, Object> userData = webClient.get()
                                .uri("/api/users/data")
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(Map.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (userData == null) {
                            return "Aucune donnée utilisateur trouvée pour: " + request.username();
                        }

                        return formatChatMcpUserData(userData);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserProfileTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserProfileTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération du profil utilisateur depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get real user invoices from chat-mcp (Reactive version)
     */
    public Function<UserInvoicesRequest, Mono<String>> getChatMcpUserInvoicesToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/real-data/invoices?limit=" + request.limit())
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(invoices -> {
                                        if (invoices == null || invoices.isEmpty()) {
                                            return "Aucune facture trouvée pour l'utilisateur: " + request.username();
                                        }
                                        return formatChatMcpInvoiceData(invoices);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des factures depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserInvoicesToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des factures depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * Tool function to get real user invoices from chat-mcp (Blocking version)
     */
    public Function<UserInvoicesRequest, String> getChatMcpUserInvoicesTool() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        List<Map<String, Object>> invoices = webClient.get()
                                .uri("/api/real-data/invoices?limit=" + request.limit())
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(List.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (invoices == null || invoices.isEmpty()) {
                            return "Aucune facture trouvée pour l'utilisateur: " + request.username();
                        }

                        return formatChatMcpInvoiceData(invoices);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserInvoicesTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération des factures depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserInvoicesTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des factures depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get user transactions from chat-mcp
     */
    public Function<UserProfileRequest, String> getChatMcpUserTransactionsTool() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        List<Map<String, Object>> transactions = webClient.get()
                                .uri("/api/real-data/transactions?limit=10")
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(List.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (transactions == null || transactions.isEmpty()) {
                            return "Aucune transaction trouvée pour l'utilisateur: " + request.username();
                        }

                        return formatChatMcpTransactionData(transactions);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserTransactionsTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération des transactions depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserTransactionsTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des transactions depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get user orders from chat-mcp
     */
    public Function<UserProfileRequest, String> getChatMcpUserOrdersTool() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Use Mono.fromCallable to execute in a separate thread pool
                return Mono.fromCallable(() -> {
                    try {
                        // Authenticate first
                        String token = chatMcpApiService.authenticateUser(request.username())
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (token == null) {
                            return "Erreur d'authentification pour l'utilisateur: " + request.username();
                        }

                        // Call the chat-mcp API
                        List<Map<String, Object>> orders = webClient.get()
                                .uri("/api/real-data/orders?limit=10")
                                .header("Authorization", "Bearer " + token)
                                .retrieve()
                                .bodyToMono(List.class)
                                .subscribeOn(Schedulers.boundedElastic())
                                .block(Duration.ofSeconds(10));

                        if (orders == null || orders.isEmpty()) {
                            return "Aucune commande trouvée pour l'utilisateur: " + request.username();
                        }

                        return formatChatMcpOrderData(orders);

                    } catch (Exception e) {
                        System.out.println("Error in getChatMcpUserOrdersTool inner: " + e.getMessage());
                        return "Erreur lors de la récupération des commandes depuis chat-mcp: " + e.getMessage();
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .block(Duration.ofSeconds(30));

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserOrdersTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des commandes depuis chat-mcp: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get user transactions from chat-mcp (Reactive version)
     */
    public Function<UserProfileRequest, Mono<String>> getChatMcpUserTransactionsToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/real-data/transactions?limit=10")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(transactions -> {
                                        if (transactions == null || transactions.isEmpty()) {
                                            return "Aucune transaction trouvée pour l'utilisateur: " + request.username();
                                        }
                                        return formatChatMcpTransactionData(transactions);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des transactions depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserTransactionsToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des transactions depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * Tool function to get user orders from chat-mcp (Reactive version)
     */
    public Function<UserProfileRequest, Mono<String>> getChatMcpUserOrdersToolReactive() {
        return request -> {
            try {
                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then call the API
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            // Call the chat-mcp API
                            return webClient.get()
                                    .uri("/api/real-data/orders?limit=10")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .map(orders -> {
                                        if (orders == null || orders.isEmpty()) {
                                            return "Aucune commande trouvée pour l'utilisateur: " + request.username();
                                        }
                                        return formatChatMcpOrderData(orders);
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des commandes depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("Error in getChatMcpUserOrdersToolReactive: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des commandes depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    /**
     * SMART AI-POWERED TOOL: Dynamically fetches ALL relevant data and lets AI decide what to use
     */
    public Function<com.example.mcp_microservice_chatboot_ai.model.SmartDataRequest, Mono<String>> getSmartChatMcpDataToolReactive() {
        return request -> {
            try {
                System.out.println("🧠 SMART MCP: Processing question: " + request.userQuestion());

                // Create WebClient for calling the chat-mcp API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first and then fetch ALL available data
                return chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .flatMap(token -> {
                            if (token == null) {
                                return Mono.just("Erreur d'authentification pour l'utilisateur: " + request.username());
                            }

                            System.out.println("🔑 SMART MCP: Authentication successful, fetching comprehensive data...");

                            // Fetch ALL data types in parallel for maximum flexibility
                            Mono<Map> userDataMono = webClient.get()
                                    .uri("/api/users/data")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(Map.class)
                                    .onErrorReturn(new HashMap<>());

                            Mono<List> invoicesMono = webClient.get()
                                    .uri("/api/real-data/invoices?limit=50")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .onErrorReturn(new ArrayList<>());

                            Mono<List> transactionsMono = webClient.get()
                                    .uri("/api/real-data/transactions?limit=50")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .onErrorReturn(new ArrayList<>());

                            Mono<List> ordersMono = webClient.get()
                                    .uri("/api/real-data/orders?limit=50")
                                    .header("Authorization", "Bearer " + token)
                                    .retrieve()
                                    .bodyToMono(List.class)
                                    .onErrorReturn(new ArrayList<>());

                            // Combine all data and let AI process it intelligently
                            return Mono.zip(userDataMono, invoicesMono, transactionsMono, ordersMono)
                                    .map(tuple -> {
                                        Map<String, Object> userData = (Map<String, Object>) tuple.getT1();
                                        List<Map<String, Object>> invoices = (List<Map<String, Object>>) tuple.getT2();
                                        List<Map<String, Object>> transactions = (List<Map<String, Object>>) tuple.getT3();
                                        List<Map<String, Object>> orders = (List<Map<String, Object>>) tuple.getT4();

                                        System.out.println("📊 SMART MCP: Fetched " + invoices.size() + " invoices, " +
                                                         transactions.size() + " transactions, " + orders.size() + " orders");

                                        // Create comprehensive data structure for AI processing
                                        return formatSmartChatMcpData(userData, invoices, transactions, orders, request.userQuestion());
                                    });
                        })
                        .onErrorReturn("Erreur lors de la récupération des données depuis chat-mcp: " + request.username());

            } catch (Exception e) {
                System.out.println("❌ SMART MCP Error: " + e.getMessage());
                e.printStackTrace();
                return Mono.just("Erreur lors de la récupération des données depuis chat-mcp: " + e.getMessage());
            }
        };
    }

    // ==================== FORMATTING METHODS ====================

    private String formatChatMcpUserData(Map<String, Object> userData) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== PROFIL UTILISATEUR (Chat-MCP) ===\n");

        // The data comes as a flat structure, not nested
        if (userData.containsKey("username")) {
            sb.append("Nom d'utilisateur: ").append(userData.get("username")).append("\n");
        }
        if (userData.containsKey("email")) {
            sb.append("Email: ").append(userData.get("email")).append("\n");
        }
        if (userData.containsKey("fullName")) {
            sb.append("Nom complet: ").append(userData.get("fullName")).append("\n");
        }
        if (userData.containsKey("phoneNumber")) {
            sb.append("Téléphone: ").append(userData.get("phoneNumber")).append("\n");
        }
        if (userData.containsKey("address")) {
            sb.append("Adresse: ").append(userData.get("address")).append("\n");
        }
        if (userData.containsKey("currentBalance")) {
            sb.append("Solde actuel: ").append(userData.get("currentBalance")).append(" EUR\n");
        }
        if (userData.containsKey("company")) {
            sb.append("Entreprise: ").append(userData.get("company")).append("\n");
        }
        if (userData.containsKey("jobTitle")) {
            sb.append("Poste: ").append(userData.get("jobTitle")).append("\n");
        }
        if (userData.containsKey("accountType")) {
            sb.append("Type de compte: ").append(userData.get("accountType")).append("\n");
        }

        return sb.toString();
    }

    private String formatChatMcpInvoiceData(List<Map<String, Object>> invoices) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== FACTURES (Chat-MCP) ===\n");

        for (Map<String, Object> invoice : invoices) {
            sb.append("Facture #").append(invoice.get("id")).append("\n");
            sb.append("  Date: ").append(invoice.get("date")).append("\n");
            sb.append("  Montant: ").append(invoice.get("amount")).append(" DH\n");
            sb.append("  Statut: ").append(invoice.get("status")).append("\n");
            sb.append("  Description: ").append(invoice.get("description")).append("\n\n");
        }

        return sb.toString();
    }

    private String formatChatMcpTransactionData(List<Map<String, Object>> transactions) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== TRANSACTIONS (Chat-MCP) ===\n");

        for (Map<String, Object> transaction : transactions) {
            sb.append("Transaction #").append(transaction.get("id")).append("\n");
            sb.append("  Date: ").append(transaction.get("date")).append("\n");
            sb.append("  Montant: ").append(transaction.get("amount")).append(" DH\n");
            sb.append("  Type: ").append(transaction.get("type")).append("\n");
            sb.append("  Description: ").append(transaction.get("description")).append("\n\n");
        }

        return sb.toString();
    }

    private String formatChatMcpOrderData(List<Map<String, Object>> orders) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== COMMANDES (Chat-MCP) ===\n");

        for (Map<String, Object> order : orders) {
            sb.append("Commande #").append(order.get("id")).append("\n");
            sb.append("  Date: ").append(order.get("date")).append("\n");
            sb.append("  Montant: ").append(order.get("amount")).append(" DH\n");
            sb.append("  Statut: ").append(order.get("status")).append("\n");
            sb.append("  Articles: ").append(order.get("items")).append("\n\n");
        }

        return sb.toString();
    }

    /**
     * SMART FORMATTING: Creates comprehensive, AI-friendly data structure
     */
    private String formatSmartChatMcpData(Map<String, Object> userData,
                                         List<Map<String, Object>> invoices,
                                         List<Map<String, Object>> transactions,
                                         List<Map<String, Object>> orders,
                                         String userQuestion) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== DONNÉES COMPLÈTES UTILISATEUR (Chat-MCP) ===\n\n");

        // USER PROFILE DATA
        if (userData != null && !userData.isEmpty()) {
            sb.append("📋 PROFIL UTILISATEUR:\n");
            if (userData.containsKey("username")) sb.append("- Nom d'utilisateur: ").append(userData.get("username")).append("\n");
            if (userData.containsKey("email")) sb.append("- Email: ").append(userData.get("email")).append("\n");
            if (userData.containsKey("fullName")) sb.append("- Nom complet: ").append(userData.get("fullName")).append("\n");
            if (userData.containsKey("phoneNumber")) sb.append("- Téléphone: ").append(userData.get("phoneNumber")).append("\n");
            if (userData.containsKey("address")) sb.append("- Adresse: ").append(userData.get("address")).append("\n");
            if (userData.containsKey("company")) sb.append("- Entreprise: ").append(userData.get("company")).append("\n");
            if (userData.containsKey("jobTitle")) sb.append("- Poste: ").append(userData.get("jobTitle")).append("\n");
            if (userData.containsKey("currentBalance")) sb.append("- Solde actuel: ").append(userData.get("currentBalance")).append(" EUR\n");
            if (userData.containsKey("accountType")) sb.append("- Type de compte: ").append(userData.get("accountType")).append("\n");
            sb.append("\n");
        }

        // INVOICE DATA WITH CALCULATIONS
        if (invoices != null && !invoices.isEmpty()) {
            sb.append("💰 FACTURES (").append(invoices.size()).append(" factures):\n");

            double totalPaid = 0, totalOverdue = 0, totalSent = 0, totalAll = 0;
            int countPaid = 0, countOverdue = 0, countSent = 0;

            for (Map<String, Object> invoice : invoices) {
                String status = String.valueOf(invoice.get("status"));
                Object amountObj = invoice.get("totalAmount");
                double amount = 0;

                if (amountObj instanceof Number) {
                    amount = ((Number) amountObj).doubleValue();
                }

                totalAll += amount;

                if ("PAID".equalsIgnoreCase(status)) {
                    totalPaid += amount;
                    countPaid++;
                } else if ("OVERDUE".equalsIgnoreCase(status)) {
                    totalOverdue += amount;
                    countOverdue++;
                } else if ("SENT".equalsIgnoreCase(status)) {
                    totalSent += amount;
                    countSent++;
                }

                sb.append("  - Facture #").append(invoice.get("id"))
                  .append(" (").append(invoice.get("invoiceNumber")).append(")")
                  .append(" - ").append(amount).append(" EUR")
                  .append(" - Statut: ").append(status)
                  .append(" - Date: ").append(invoice.get("invoiceDate"))
                  .append("\n");
            }

            sb.append("\n📊 RÉSUMÉ FACTURES:\n");
            sb.append("- Total toutes factures: ").append(String.format("%.2f", totalAll)).append(" EUR\n");
            sb.append("- Factures payées: ").append(countPaid).append(" factures, ").append(String.format("%.2f", totalPaid)).append(" EUR\n");
            sb.append("- Factures en retard: ").append(countOverdue).append(" factures, ").append(String.format("%.2f", totalOverdue)).append(" EUR\n");
            sb.append("- Factures envoyées: ").append(countSent).append(" factures, ").append(String.format("%.2f", totalSent)).append(" EUR\n");
            sb.append("\n");
        }

        // TRANSACTION DATA WITH CALCULATIONS
        if (transactions != null && !transactions.isEmpty()) {
            sb.append("💳 TRANSACTIONS (").append(transactions.size()).append(" transactions):\n");

            double totalCredits = 0, totalDebits = 0, totalPayments = 0;
            int countCompleted = 0, countPending = 0, countFailed = 0;

            for (Map<String, Object> transaction : transactions) {
                String type = String.valueOf(transaction.get("type"));
                String status = String.valueOf(transaction.get("status"));
                Object amountObj = transaction.get("amount");
                double amount = 0;

                if (amountObj instanceof Number) {
                    amount = ((Number) amountObj).doubleValue();
                }

                if ("CREDIT".equalsIgnoreCase(type)) {
                    totalCredits += amount;
                } else if ("DEBIT".equalsIgnoreCase(type)) {
                    totalDebits += amount;
                } else if ("PAYMENT".equalsIgnoreCase(type)) {
                    totalPayments += amount;
                }

                if ("COMPLETED".equalsIgnoreCase(status)) {
                    countCompleted++;
                } else if ("PENDING".equalsIgnoreCase(status)) {
                    countPending++;
                } else if ("FAILED".equalsIgnoreCase(status)) {
                    countFailed++;
                }

                sb.append("  - Transaction #").append(transaction.get("id"))
                  .append(" (").append(transaction.get("transactionId")).append(")")
                  .append(" - ").append(amount).append(" EUR")
                  .append(" - Type: ").append(type)
                  .append(" - Statut: ").append(status)
                  .append(" - Date: ").append(transaction.get("createdAt"))
                  .append("\n");
            }

            sb.append("\n📊 RÉSUMÉ TRANSACTIONS:\n");
            sb.append("- Total crédits: ").append(String.format("%.2f", totalCredits)).append(" EUR\n");
            sb.append("- Total débits: ").append(String.format("%.2f", totalDebits)).append(" EUR\n");
            sb.append("- Total paiements: ").append(String.format("%.2f", totalPayments)).append(" EUR\n");
            sb.append("- Transactions complétées: ").append(countCompleted).append("\n");
            sb.append("- Transactions en attente: ").append(countPending).append("\n");
            sb.append("- Transactions échouées: ").append(countFailed).append("\n");
            sb.append("\n");
        }

        // ORDER DATA WITH CALCULATIONS
        if (orders != null && !orders.isEmpty()) {
            sb.append("📦 COMMANDES (").append(orders.size()).append(" commandes):\n");

            double totalOrderAmount = 0;
            int countDelivered = 0, countShipped = 0, countProcessing = 0;

            for (Map<String, Object> order : orders) {
                String status = String.valueOf(order.get("status"));
                Object amountObj = order.get("totalAmount");
                double amount = 0;

                if (amountObj instanceof Number) {
                    amount = ((Number) amountObj).doubleValue();
                }

                totalOrderAmount += amount;

                if ("DELIVERED".equalsIgnoreCase(status)) {
                    countDelivered++;
                } else if ("SHIPPED".equalsIgnoreCase(status)) {
                    countShipped++;
                } else if ("PROCESSING".equalsIgnoreCase(status)) {
                    countProcessing++;
                }

                sb.append("  - Commande #").append(order.get("id"))
                  .append(" (").append(order.get("orderNumber")).append(")")
                  .append(" - ").append(amount).append(" EUR")
                  .append(" - Statut: ").append(status)
                  .append(" - Articles: ").append(order.get("itemsDescription"))
                  .append(" - Date: ").append(order.get("orderDate"))
                  .append("\n");
            }

            sb.append("\n📊 RÉSUMÉ COMMANDES:\n");
            sb.append("- Total commandes: ").append(String.format("%.2f", totalOrderAmount)).append(" EUR\n");
            sb.append("- Commandes livrées: ").append(countDelivered).append("\n");
            sb.append("- Commandes expédiées: ").append(countShipped).append("\n");
            sb.append("- Commandes en traitement: ").append(countProcessing).append("\n");
            sb.append("\n");
        }

        sb.append("🎯 QUESTION UTILISATEUR: ").append(userQuestion).append("\n");
        sb.append("\n💡 INSTRUCTIONS: Utilisez TOUTES ces données pour répondre précisément à la question. ");
        sb.append("Calculez les totaux, analysez les tendances, et fournissez une réponse complète et détaillée.");

        return sb.toString();
    }
}
