{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-help.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./chat-help.component.scss?ngResource\";\n// chat-help.component.ts\nimport { Component, Output, EventEmitter } from '@angular/core';\nlet ChatHelpComponent = class ChatHelpComponent {\n  constructor() {\n    this.selectHelp = new EventEmitter();\n    this.helpCategories = [{\n      name: 'Statistiques de Ventes',\n      questions: ['Quels sont les produits les plus vendus ce mois-ci ?', 'Quelle est l’évolution des ventes cette année ?', 'Quels mois enregistrent les ventes les plus fortes ?']\n    }, {\n      name: 'Suivi des Achats',\n      questions: ['Quels produits ont été achetés en grande quantité cette année ?', 'Quels fournisseurs ont été les plus sollicités ?', 'Quelle est la tendance des achats par trimestre ?']\n    }, {\n      name: '<PERSON><PERSON> des Stocks',\n      questions: ['Quels sont les produits en rupture de stock ?', 'Quels produits ont un niveau de stock critique ?', 'Quels articles doivent être réapprovisionnés en priorité ?']\n    }];\n  }\n  selectQuestion(question) {\n    this.selectHelp.emit(question);\n  }\n  static {\n    this.propDecorators = {\n      selectHelp: [{\n        type: Output\n      }]\n    };\n  }\n};\nChatHelpComponent = __decorate([Component({\n  selector: 'app-chat-help',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ChatHelpComponent);\nexport { ChatHelpComponent };", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "ChatHelpComponent", "constructor", "selectHelp", "helpCategories", "name", "questions", "selectQuestion", "question", "emit", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-help\\chat-help.component.ts"], "sourcesContent": ["// chat-help.component.ts\r\nimport { Component, Output, EventEmitter, ViewEncapsulation } from '@angular/core';\r\n\r\ninterface HelpCategory {\r\n  name: string;\r\n  questions: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-chat-help',\r\n  templateUrl: './chat-help.component.html',\r\n  styleUrls: ['./chat-help.component.scss'],\r\n  // encapsulation: ViewEncapsulation.ShadowDom\r\n})\r\nexport class ChatHelpComponent {\r\n  @Output() selectHelp = new EventEmitter<string>();\r\n  \r\n  helpCategories: HelpCategory[] = [\r\n    {\r\n      name: 'Statistiques de Ventes',\r\n      questions: [\r\n        'Quels sont les produits les plus vendus ce mois-ci ?',\r\n        'Quelle est l’évolution des ventes cette année ?',\r\n        'Quels mois enregistrent les ventes les plus fortes ?'\r\n      ]\r\n    },\r\n    {\r\n      name: 'Suivi des Achats',\r\n      questions: [\r\n        'Quels produits ont été achetés en grande quantité cette année ?',\r\n        'Quels fournisseurs ont été les plus sollicités ?',\r\n        'Quelle est la tendance des achats par trimestre ?'\r\n      ]\r\n    },\r\n    {\r\n      name: '<PERSON><PERSON> des Stocks',\r\n      questions: [\r\n        'Quels sont les produits en rupture de stock ?',\r\n        'Quels produits ont un niveau de stock critique ?',\r\n        'Quels articles doivent être réapprovisionnés en priorité ?'\r\n      ]\r\n    }\r\n  ];\r\n  \r\n  selectQuestion(question: string) {\r\n    this.selectHelp.emit(question);\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA;AACA,SAASA,SAAS,EAAEC,MAAM,EAAEC,YAAY,QAA2B,eAAe;AAa3E,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAAvBC,YAAA;IACK,KAAAC,UAAU,GAAG,IAAIH,YAAY,EAAU;IAEjD,KAAAI,cAAc,GAAmB,CAC/B;MACEC,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE,CACT,sDAAsD,EACtD,iDAAiD,EACjD,sDAAsD;KAEzD,EACD;MACED,IAAI,EAAE,kBAAkB;MACxBC,SAAS,EAAE,CACT,iEAAiE,EACjE,kDAAkD,EAClD,mDAAmD;KAEtD,EACD;MACED,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAE,CACT,+CAA+C,EAC/C,kDAAkD,EAClD,4DAA4D;KAE/D,CACF;EAKH;EAHEC,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAACL,UAAU,CAACM,IAAI,CAACD,QAAQ,CAAC;EAChC;;;;cA/BCT;MAAM;;;;AADIE,iBAAiB,GAAAS,UAAA,EAN7BZ,SAAS,CAAC;EACTa,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;;CAG1C,CAAC,C,EACWZ,iBAAiB,CAiC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}