package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "clients")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Client {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "adr1")
    private String adr1;
    
    @Column(name = "adr2")
    private String adr2;
    
    @Column(name = "audited")
    private Boolean audited;
    
    @Column(name = "date_dernier_credit")
    private LocalDateTime dateDernierCredit;
    
    @Column(name = "date_dernier_reglement")
    private LocalDateTime dateDernierReglement;
    
    @Column(name = "dernier_credit", precision = 19, scale = 2)
    private BigDecimal dernierCredit;
    
    @Column(name = "dernier_reglement", precision = 19, scale = 2)
    private BigDecimal dernierReglement;
    
    @Column(name = "email")
    private String email;
    
    @Column(name = "est_actif")
    private Boolean estActif;
    
    @Column(name = "gsm")
    private String gsm;
    
    @Column(name = "nom")
    private String nom;
    
    @Column(name = "num_cin")
    private String numCin;
    
    @Column(name = "num_ice")
    private String numIce;
    
    @Column(name = "num_telephone")
    private String numTelephone;
    
    @Column(name = "plafond_credit", precision = 19, scale = 2)
    private BigDecimal plafondCredit;
    
    @Column(name = "prenom")
    private String prenom;
    
    @Column(name = "solde_client", precision = 19, scale = 2)
    private BigDecimal soldeClient;
    
    @Column(name = "taux_remise", precision = 5, scale = 2)
    private BigDecimal tauxRemise;
    
    @Column(name = "user_modifiable")
    private Boolean userModifiable;
    
    @Column(name = "ca_client", precision = 19, scale = 2)
    private BigDecimal caClient;
    
    @Column(name = "solde_client_depart", precision = 19, scale = 2)
    private BigDecimal soldeClientDepart;
    
    @Column(name = "code_client")
    private String codeClient;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // Relationships
    @OneToMany(mappedBy = "client", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<EnteteVente> ventes;
    
    @OneToMany(mappedBy = "client", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Beneficiaire> beneficiaires;
    
    // Computed property
    public String getNomPrenom() {
        return (nom != null ? nom : "") + " " + (prenom != null ? prenom : "");
    }
}
