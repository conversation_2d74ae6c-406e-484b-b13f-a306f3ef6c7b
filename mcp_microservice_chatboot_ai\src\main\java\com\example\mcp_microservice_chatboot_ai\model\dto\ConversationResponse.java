package com.example.mcp_microservice_chatboot_ai.model.dto;

import com.example.mcp_microservice_chatboot_ai.model.Conversation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for conversation responses.
 */

/**
 * Role: Data Transfer Object for conversation responses
    Purpose:
    Carries conversation data (ID, title, timestamps)
    Used to return conversation information to the client
    Includes static factory method to create from Conversation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationResponse {
    
    /**
     * The unique identifier of the conversation.
     */
    private String id;
    
    /**
     * The title of the conversation.
     */
    private String title;
    
    /**
     * The username of the user who owns the conversation.
     */
    private String username;
    
    /**
     * The timestamp when the conversation was created.
     */
    private LocalDateTime createdAt;
    
    /**
     * The timestamp when the conversation was last updated.
     */
    private LocalDateTime updatedAt;
    
    /**
     * Factory method to create a ConversationResponse from a Conversation.
     * 
     * @param conversation The Conversation to convert
     * @return A new ConversationResponse
     */
    public static ConversationResponse fromConversation(Conversation conversation) {
        return ConversationResponse.builder()
                .id(conversation.getId())
                .title(conversation.getTitle())
                .username(conversation.getUsername())
                .createdAt(conversation.getCreatedAt())
                .updatedAt(conversation.getUpdatedAt())
                .build();
    }
}
