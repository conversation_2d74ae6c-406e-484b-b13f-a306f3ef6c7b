package com.example.mcp_microservice_chatboot_ai.config;

import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Configuration class for MCP prompts.
 * This class sets up the prompt specifications for the MCP server.
 */

/**
 * Role: Configuration class for MCP prompts
 *        Purpose:
 *        Sets up prompt specifications for the MCP server
 *        Creates greeting and help prompts
 *        Provides template prompts for clients to use
 */
@Configuration
public class McpPromptConfig {

    /**
     * Creates a greeting prompt template.
     *
     * @return A greeting prompt template
     */
    @Bean
    public PromptTemplate greetingPromptTemplate() {
        String template = "Hello {name}! How can I assist you today?";
        return new PromptTemplate(template);
    }
    
    /**
     * Creates a help prompt template.
     *
     * @return A help prompt template
     */
    @Bean
    public PromptTemplate helpPromptTemplate() {
        String template = "I need help with using the chat system. What can I do?";
        return new PromptTemplate(template);
    }
    
    /**
     * Creates a French greeting prompt template.
     *
     * @return A French greeting prompt template
     */
    @Bean
    public PromptTemplate frenchGreetingPromptTemplate() {
        String template = "Bonjour {nom} ! Comment puis-je vous aider aujourd'hui ?";
        return new PromptTemplate(template);
    }
    
    /**
     * Creates a French help prompt template.
     *
     * @return A French help prompt template
     */
    @Bean
    public PromptTemplate frenchHelpPromptTemplate() {
        String template = "J'ai besoin d'aide pour utiliser le système de chat. Que puis-je faire ?";
        return new PromptTemplate(template);
    }
    
    /**
     * Creates a database query prompt template.
     *
     * @return A database query prompt template
     */
    @Bean
    public PromptTemplate databaseQueryPromptTemplate() {
        String template = "Je suis {username} et je voudrais savoir : {question}";
        return new PromptTemplate(template);
    }
}
