{"ast": null, "code": "import { __decorate } from \"tslib\";\n// openai.service.ts\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError, of } from 'rxjs';\nimport { switchMap, catchError, map, finalize } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nlet OpenaiService = class OpenaiService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = 'https://api.openai.com/v1';\n    this.assistantId = 'asst_LdD9JYN5V2Es6TF9DM59tA09';\n    this.threadCache = new Map(); // Pour réutiliser les threads\n  }\n  sendMessage(content, threadId) {\n    const apiKey = environment.openaiApiKey;\n    if (!apiKey) {\n      return throwError(() => new Error('API key not configured'));\n    }\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${apiKey}`,\n      'OpenAI-Beta': 'assistants=v2'\n    });\n    // Étape 1: C<PERSON>er un thread ou réutiliser un existant\n    const createOrReuseThread = threadId ? of({\n      id: threadId\n    }) : this.http.post(`${this.baseUrl}/threads`, {}, {\n      headers\n    });\n    return createOrReuseThread.pipe(switchMap(thread => {\n      const currentThreadId = thread.id;\n      console.log(`Using thread: ${currentThreadId}`);\n      // Étape 2: Ajouter le message de l'utilisateur au thread\n      return this.http.post(`${this.baseUrl}/threads/${currentThreadId}/messages`, {\n        role: 'user',\n        content: content\n      }, {\n        headers\n      }).pipe(switchMap(messageResponse => {\n        console.log('Message added:', messageResponse);\n        // Étape 3: Exécuter l'assistant\n        return this.http.post(`${this.baseUrl}/threads/${currentThreadId}/runs`, {\n          assistant_id: this.assistantId\n        }, {\n          headers\n        }).pipe(switchMap(run => {\n          const runId = run.id;\n          console.log(`Run created: ${runId}`);\n          // Étape 4: Attendre que l'exécution soit terminée\n          return this.pollRunStatus(currentThreadId, runId, headers).pipe(switchMap(completedRun => {\n            console.log('Run completed:', completedRun);\n            // Attendre 1 seconde pour s'assurer que tous les messages sont disponibles\n            return new Observable(observer => {\n              setTimeout(() => observer.next(true), 1000);\n            }).pipe(switchMap(() => {\n              // Étape 5: Récupérer les messages avec pagination pour s'assurer d'avoir tous les messages\n              return this.getAllMessages(currentThreadId, headers);\n            }));\n          }), map(allMessages => {\n            // Filtrer uniquement les messages de l'assistant\n            const assistantMessages = allMessages.filter(msg => msg.role === 'assistant');\n            // Trier par created_at (timestamp numérique)\n            assistantMessages.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\n            console.log(`Found ${assistantMessages.length} assistant messages`);\n            // Prendre uniquement le message le plus récent de l'assistant\n            const lastMessage = assistantMessages[0];\n            if (!lastMessage) {\n              return {\n                message: 'Aucune réponse reçue.',\n                threadId: currentThreadId\n              };\n            }\n            // Extraire le contenu textuel\n            let content = '';\n            if (lastMessage.content && lastMessage.content.length > 0) {\n              for (const item of lastMessage.content) {\n                if (item.type === 'text') {\n                  content += item.text.value;\n                }\n              }\n            }\n            return {\n              message: content.trim() || 'Réponse vide.',\n              threadId: currentThreadId // Retourner l'ID du thread pour réutilisation\n            };\n          }), finalize(() => {\n            console.log('Request completed');\n          }));\n        }));\n      }));\n    }), catchError(err => {\n      console.error('OpenAI Assistant API error:', err);\n      return throwError(() => new Error(err.message || 'Erreur inconnue côté serveur OpenAI'));\n    }));\n  }\n  // Récupérer tous les messages avec pagination\n  getAllMessages(threadId, headers) {\n    return new Observable(observer => {\n      const fetchMessages = (after, allMessages = []) => {\n        let url = `${this.baseUrl}/threads/${threadId}/messages?limit=100`;\n        if (after) {\n          url += `&after=${after}`;\n        }\n        this.http.get(url, {\n          headers\n        }).subscribe({\n          next: response => {\n            const messages = response.data || [];\n            const newAllMessages = [...allMessages, ...messages];\n            if (response.has_more && response.data.length > 0) {\n              // S'il y a plus de messages, récupérer la page suivante\n              const lastId = response.data[response.data.length - 1].id;\n              fetchMessages(lastId, newAllMessages);\n            } else {\n              // Tous les messages ont été récupérés\n              observer.next(newAllMessages);\n              observer.complete();\n            }\n          },\n          error: err => {\n            observer.error(err);\n          }\n        });\n      };\n      // Démarrer la récupération des messages\n      fetchMessages();\n    });\n  }\n  pollRunStatus(threadId, runId, headers) {\n    return new Observable(observer => {\n      const checkStatus = () => {\n        this.http.get(`${this.baseUrl}/threads/${threadId}/runs/${runId}`, {\n          headers\n        }).subscribe({\n          next: runStatus => {\n            console.log(`Run status: ${runStatus.status}`);\n            if (runStatus.status === 'completed') {\n              observer.next(runStatus);\n              observer.complete();\n            } else if (['failed', 'cancelled', 'expired'].includes(runStatus.status)) {\n              observer.error(new Error(`Run failed with status: ${runStatus.status}`));\n            } else {\n              // Continuer à vérifier toutes les secondes\n              setTimeout(checkStatus, 1000);\n            }\n          },\n          error: err => {\n            observer.error(err);\n          }\n        });\n      };\n      // Démarrer la vérification du statut\n      checkStatus();\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: HttpClient\n    }];\n  }\n};\nOpenaiService = __decorate([Injectable({\n  providedIn: 'root'\n})], OpenaiService);\nexport { OpenaiService };", "map": {"version": 3, "names": ["Injectable", "HttpClient", "HttpHeaders", "Observable", "throwError", "of", "switchMap", "catchError", "map", "finalize", "environment", "OpenaiService", "constructor", "http", "baseUrl", "assistantId", "threadCache", "Map", "sendMessage", "content", "threadId", "<PERSON><PERSON><PERSON><PERSON>", "openaiApiKey", "Error", "headers", "createOrReuseThread", "id", "post", "pipe", "thread", "currentThreadId", "console", "log", "role", "messageResponse", "assistant_id", "run", "runId", "pollRunStatus", "completedRun", "observer", "setTimeout", "next", "getAllMessages", "allMessages", "assistantMessages", "filter", "msg", "sort", "a", "b", "Date", "created_at", "getTime", "length", "lastMessage", "message", "item", "type", "text", "value", "trim", "err", "error", "fetchMessages", "after", "url", "get", "subscribe", "response", "messages", "data", "newAllMessages", "has_more", "lastId", "complete", "checkStatus", "runStatus", "status", "includes", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\services\\openai.service.ts"], "sourcesContent": ["// openai.service.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable, throwError, interval, of } from 'rxjs';\r\nimport { switchMap, catchError, map, takeWhile, finalize } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OpenaiService {\r\n  private baseUrl = 'https://api.openai.com/v1';\r\n  private assistantId = 'asst_LdD9JYN5V2Es6TF9DM59tA09';\r\n  private threadCache = new Map<string, string>(); // Pour réutiliser les threads\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  sendMessage(content: string, threadId?: string): Observable<any> {\r\n    const apiKey = environment.openaiApiKey;\r\n    if (!apiKey) {\r\n      return throwError(() => new Error('API key not configured'));\r\n    }\r\n\r\n    const headers = new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      'Authorization': `Bearer ${apiKey}`,\r\n      'OpenAI-Beta': 'assistants=v2'\r\n    });\r\n\r\n    // Étape 1: Créer un thread ou réutiliser un existant\r\n    const createOrReuseThread = threadId \r\n      ? of({ id: threadId }) \r\n      : this.http.post(`${this.baseUrl}/threads`, {}, { headers });\r\n\r\n    return createOrReuseThread.pipe(\r\n      switchMap((thread: any) => {\r\n        const currentThreadId = thread.id;\r\n        console.log(`Using thread: ${currentThreadId}`);\r\n\r\n        // Étape 2: Ajouter le message de l'utilisateur au thread\r\n        return this.http.post(`${this.baseUrl}/threads/${currentThreadId}/messages`, {\r\n          role: 'user',\r\n          content: content\r\n        }, { headers }).pipe(\r\n          switchMap((messageResponse) => {\r\n            console.log('Message added:', messageResponse);\r\n            \r\n            // Étape 3: Exécuter l'assistant\r\n            return this.http.post(`${this.baseUrl}/threads/${currentThreadId}/runs`, {\r\n              assistant_id: this.assistantId\r\n            }, { headers }).pipe(\r\n              switchMap((run: any) => {\r\n                const runId = run.id;\r\n                console.log(`Run created: ${runId}`);\r\n\r\n                // Étape 4: Attendre que l'exécution soit terminée\r\n                return this.pollRunStatus(currentThreadId, runId, headers).pipe(\r\n                  switchMap((completedRun) => {\r\n                    console.log('Run completed:', completedRun);\r\n                    \r\n                    // Attendre 1 seconde pour s'assurer que tous les messages sont disponibles\r\n                    return new Observable(observer => {\r\n                      setTimeout(() => observer.next(true), 1000);\r\n                    }).pipe(\r\n                      switchMap(() => {\r\n                        // Étape 5: Récupérer les messages avec pagination pour s'assurer d'avoir tous les messages\r\n                        return this.getAllMessages(currentThreadId, headers);\r\n                      })\r\n                    );\r\n                  }),\r\n                  map((allMessages: any[]) => {\r\n                    // Filtrer uniquement les messages de l'assistant\r\n                    const assistantMessages = allMessages.filter(msg => msg.role === 'assistant');\r\n                    \r\n                    // Trier par created_at (timestamp numérique)\r\n                    assistantMessages.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\r\n                    \r\n                    console.log(`Found ${assistantMessages.length} assistant messages`);\r\n                    \r\n                    // Prendre uniquement le message le plus récent de l'assistant\r\n                    const lastMessage = assistantMessages[0];\r\n                    \r\n                    if (!lastMessage) {\r\n                      return { message: 'Aucune réponse reçue.', threadId: currentThreadId };\r\n                    }\r\n                    \r\n                    // Extraire le contenu textuel\r\n                    let content = '';\r\n                    if (lastMessage.content && lastMessage.content.length > 0) {\r\n                      for (const item of lastMessage.content) {\r\n                        if (item.type === 'text') {\r\n                          content += item.text.value;\r\n                        }\r\n                      }\r\n                    }\r\n                    \r\n                    return { \r\n                      message: content.trim() || 'Réponse vide.', \r\n                      threadId: currentThreadId  // Retourner l'ID du thread pour réutilisation\r\n                    };\r\n                  }),\r\n                  finalize(() => {\r\n                    console.log('Request completed');\r\n                  })\r\n                );\r\n              })\r\n            );\r\n          })\r\n        );\r\n      }),\r\n      catchError(err => {\r\n        console.error('OpenAI Assistant API error:', err);\r\n        return throwError(() => new Error(err.message || 'Erreur inconnue côté serveur OpenAI'));\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer tous les messages avec pagination\r\n  private getAllMessages(threadId: string, headers: HttpHeaders): Observable<any[]> {\r\n    return new Observable(observer => {\r\n      const fetchMessages = (after?: string, allMessages: any[] = []) => {\r\n        let url = `${this.baseUrl}/threads/${threadId}/messages?limit=100`;\r\n        if (after) {\r\n          url += `&after=${after}`;\r\n        }\r\n        \r\n        this.http.get(url, { headers }).subscribe({\r\n          next: (response: any) => {\r\n            const messages = response.data || [];\r\n            const newAllMessages = [...allMessages, ...messages];\r\n            \r\n            if (response.has_more && response.data.length > 0) {\r\n              // S'il y a plus de messages, récupérer la page suivante\r\n              const lastId = response.data[response.data.length - 1].id;\r\n              fetchMessages(lastId, newAllMessages);\r\n            } else {\r\n              // Tous les messages ont été récupérés\r\n              observer.next(newAllMessages);\r\n              observer.complete();\r\n            }\r\n          },\r\n          error: (err) => {\r\n            observer.error(err);\r\n          }\r\n        });\r\n      };\r\n      \r\n      // Démarrer la récupération des messages\r\n      fetchMessages();\r\n    });\r\n  }\r\n\r\n  private pollRunStatus(threadId: string, runId: string, headers: HttpHeaders): Observable<any> {\r\n    return new Observable(observer => {\r\n      const checkStatus = () => {\r\n        this.http.get(`${this.baseUrl}/threads/${threadId}/runs/${runId}`, { headers }).subscribe({\r\n          next: (runStatus: any) => {\r\n            console.log(`Run status: ${runStatus.status}`);\r\n            \r\n            if (runStatus.status === 'completed') {\r\n              observer.next(runStatus);\r\n              observer.complete();\r\n            } else if (['failed', 'cancelled', 'expired'].includes(runStatus.status)) {\r\n              observer.error(new Error(`Run failed with status: ${runStatus.status}`));\r\n            } else {\r\n              // Continuer à vérifier toutes les secondes\r\n              setTimeout(checkStatus, 1000);\r\n            }\r\n          },\r\n          error: (err) => {\r\n            observer.error(err);\r\n          }\r\n        });\r\n      };\r\n      \r\n      // Démarrer la vérification du statut\r\n      checkStatus();\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";AAAA;AACA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,EAAEC,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,UAAU,EAAYC,EAAE,QAAQ,MAAM;AAC3D,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAaC,QAAQ,QAAQ,gBAAgB;AAChF,SAASC,WAAW,QAAQ,gCAAgC;AAKrD,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAKxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,OAAO,GAAG,2BAA2B;IACrC,KAAAC,WAAW,GAAG,+BAA+B;IAC7C,KAAAC,WAAW,GAAG,IAAIC,GAAG,EAAkB,CAAC,CAAC;EAEV;EAEvCC,WAAWA,CAACC,OAAe,EAAEC,QAAiB;IAC5C,MAAMC,MAAM,GAAGX,WAAW,CAACY,YAAY;IACvC,IAAI,CAACD,MAAM,EAAE;MACX,OAAOjB,UAAU,CAAC,MAAM,IAAImB,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D;IAEA,MAAMC,OAAO,GAAG,IAAItB,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUmB,MAAM,EAAE;MACnC,aAAa,EAAE;KAChB,CAAC;IAEF;IACA,MAAMI,mBAAmB,GAAGL,QAAQ,GAChCf,EAAE,CAAC;MAAEqB,EAAE,EAAEN;IAAQ,CAAE,CAAC,GACpB,IAAI,CAACP,IAAI,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,UAAU,EAAE,EAAE,EAAE;MAAEU;IAAO,CAAE,CAAC;IAE9D,OAAOC,mBAAmB,CAACG,IAAI,CAC7BtB,SAAS,CAAEuB,MAAW,IAAI;MACxB,MAAMC,eAAe,GAAGD,MAAM,CAACH,EAAE;MACjCK,OAAO,CAACC,GAAG,CAAC,iBAAiBF,eAAe,EAAE,CAAC;MAE/C;MACA,OAAO,IAAI,CAACjB,IAAI,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,YAAYgB,eAAe,WAAW,EAAE;QAC3EG,IAAI,EAAE,MAAM;QACZd,OAAO,EAAEA;OACV,EAAE;QAAEK;MAAO,CAAE,CAAC,CAACI,IAAI,CAClBtB,SAAS,CAAE4B,eAAe,IAAI;QAC5BH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,eAAe,CAAC;QAE9C;QACA,OAAO,IAAI,CAACrB,IAAI,CAACc,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,YAAYgB,eAAe,OAAO,EAAE;UACvEK,YAAY,EAAE,IAAI,CAACpB;SACpB,EAAE;UAAES;QAAO,CAAE,CAAC,CAACI,IAAI,CAClBtB,SAAS,CAAE8B,GAAQ,IAAI;UACrB,MAAMC,KAAK,GAAGD,GAAG,CAACV,EAAE;UACpBK,OAAO,CAACC,GAAG,CAAC,gBAAgBK,KAAK,EAAE,CAAC;UAEpC;UACA,OAAO,IAAI,CAACC,aAAa,CAACR,eAAe,EAAEO,KAAK,EAAEb,OAAO,CAAC,CAACI,IAAI,CAC7DtB,SAAS,CAAEiC,YAAY,IAAI;YACzBR,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEO,YAAY,CAAC;YAE3C;YACA,OAAO,IAAIpC,UAAU,CAACqC,QAAQ,IAAG;cAC/BC,UAAU,CAAC,MAAMD,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;YAC7C,CAAC,CAAC,CAACd,IAAI,CACLtB,SAAS,CAAC,MAAK;cACb;cACA,OAAO,IAAI,CAACqC,cAAc,CAACb,eAAe,EAAEN,OAAO,CAAC;YACtD,CAAC,CAAC,CACH;UACH,CAAC,CAAC,EACFhB,GAAG,CAAEoC,WAAkB,IAAI;YACzB;YACA,MAAMC,iBAAiB,GAAGD,WAAW,CAACE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACd,IAAI,KAAK,WAAW,CAAC;YAE7E;YACAY,iBAAiB,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,UAAU,CAAC,CAACC,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,UAAU,CAAC,CAACC,OAAO,EAAE,CAAC;YAErGtB,OAAO,CAACC,GAAG,CAAC,SAASa,iBAAiB,CAACS,MAAM,qBAAqB,CAAC;YAEnE;YACA,MAAMC,WAAW,GAAGV,iBAAiB,CAAC,CAAC,CAAC;YAExC,IAAI,CAACU,WAAW,EAAE;cAChB,OAAO;gBAAEC,OAAO,EAAE,uBAAuB;gBAAEpC,QAAQ,EAAEU;cAAe,CAAE;YACxE;YAEA;YACA,IAAIX,OAAO,GAAG,EAAE;YAChB,IAAIoC,WAAW,CAACpC,OAAO,IAAIoC,WAAW,CAACpC,OAAO,CAACmC,MAAM,GAAG,CAAC,EAAE;cACzD,KAAK,MAAMG,IAAI,IAAIF,WAAW,CAACpC,OAAO,EAAE;gBACtC,IAAIsC,IAAI,CAACC,IAAI,KAAK,MAAM,EAAE;kBACxBvC,OAAO,IAAIsC,IAAI,CAACE,IAAI,CAACC,KAAK;gBAC5B;cACF;YACF;YAEA,OAAO;cACLJ,OAAO,EAAErC,OAAO,CAAC0C,IAAI,EAAE,IAAI,eAAe;cAC1CzC,QAAQ,EAAEU,eAAe,CAAE;aAC5B;UACH,CAAC,CAAC,EACFrB,QAAQ,CAAC,MAAK;YACZsB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;UAClC,CAAC,CAAC,CACH;QACH,CAAC,CAAC,CACH;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFzB,UAAU,CAACuD,GAAG,IAAG;MACf/B,OAAO,CAACgC,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC;MACjD,OAAO1D,UAAU,CAAC,MAAM,IAAImB,KAAK,CAACuC,GAAG,CAACN,OAAO,IAAI,qCAAqC,CAAC,CAAC;IAC1F,CAAC,CAAC,CACH;EACH;EAEA;EACQb,cAAcA,CAACvB,QAAgB,EAAEI,OAAoB;IAC3D,OAAO,IAAIrB,UAAU,CAACqC,QAAQ,IAAG;MAC/B,MAAMwB,aAAa,GAAGA,CAACC,KAAc,EAAErB,WAAA,GAAqB,EAAE,KAAI;QAChE,IAAIsB,GAAG,GAAG,GAAG,IAAI,CAACpD,OAAO,YAAYM,QAAQ,qBAAqB;QAClE,IAAI6C,KAAK,EAAE;UACTC,GAAG,IAAI,UAAUD,KAAK,EAAE;QAC1B;QAEA,IAAI,CAACpD,IAAI,CAACsD,GAAG,CAACD,GAAG,EAAE;UAAE1C;QAAO,CAAE,CAAC,CAAC4C,SAAS,CAAC;UACxC1B,IAAI,EAAG2B,QAAa,IAAI;YACtB,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,IAAI,IAAI,EAAE;YACpC,MAAMC,cAAc,GAAG,CAAC,GAAG5B,WAAW,EAAE,GAAG0B,QAAQ,CAAC;YAEpD,IAAID,QAAQ,CAACI,QAAQ,IAAIJ,QAAQ,CAACE,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAE;cACjD;cACA,MAAMoB,MAAM,GAAGL,QAAQ,CAACE,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACjB,MAAM,GAAG,CAAC,CAAC,CAAC5B,EAAE;cACzDsC,aAAa,CAACU,MAAM,EAAEF,cAAc,CAAC;YACvC,CAAC,MAAM;cACL;cACAhC,QAAQ,CAACE,IAAI,CAAC8B,cAAc,CAAC;cAC7BhC,QAAQ,CAACmC,QAAQ,EAAE;YACrB;UACF,CAAC;UACDZ,KAAK,EAAGD,GAAG,IAAI;YACbtB,QAAQ,CAACuB,KAAK,CAACD,GAAG,CAAC;UACrB;SACD,CAAC;MACJ,CAAC;MAED;MACAE,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EAEQ1B,aAAaA,CAAClB,QAAgB,EAAEiB,KAAa,EAAEb,OAAoB;IACzE,OAAO,IAAIrB,UAAU,CAACqC,QAAQ,IAAG;MAC/B,MAAMoC,WAAW,GAAGA,CAAA,KAAK;QACvB,IAAI,CAAC/D,IAAI,CAACsD,GAAG,CAAC,GAAG,IAAI,CAACrD,OAAO,YAAYM,QAAQ,SAASiB,KAAK,EAAE,EAAE;UAAEb;QAAO,CAAE,CAAC,CAAC4C,SAAS,CAAC;UACxF1B,IAAI,EAAGmC,SAAc,IAAI;YACvB9C,OAAO,CAACC,GAAG,CAAC,eAAe6C,SAAS,CAACC,MAAM,EAAE,CAAC;YAE9C,IAAID,SAAS,CAACC,MAAM,KAAK,WAAW,EAAE;cACpCtC,QAAQ,CAACE,IAAI,CAACmC,SAAS,CAAC;cACxBrC,QAAQ,CAACmC,QAAQ,EAAE;YACrB,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAACI,QAAQ,CAACF,SAAS,CAACC,MAAM,CAAC,EAAE;cACxEtC,QAAQ,CAACuB,KAAK,CAAC,IAAIxC,KAAK,CAAC,2BAA2BsD,SAAS,CAACC,MAAM,EAAE,CAAC,CAAC;YAC1E,CAAC,MAAM;cACL;cACArC,UAAU,CAACmC,WAAW,EAAE,IAAI,CAAC;YAC/B;UACF,CAAC;UACDb,KAAK,EAAGD,GAAG,IAAI;YACbtB,QAAQ,CAACuB,KAAK,CAACD,GAAG,CAAC;UACrB;SACD,CAAC;MACJ,CAAC;MAED;MACAc,WAAW,EAAE;IACf,CAAC,CAAC;EACJ;;;;;;;AAxKWjE,aAAa,GAAAqE,UAAA,EAHzBhF,UAAU,CAAC;EACViF,UAAU,EAAE;CACb,CAAC,C,EACWtE,aAAa,CAyKzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}