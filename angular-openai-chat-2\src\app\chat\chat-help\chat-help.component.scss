// chat-help.component.scss

$primary-color: #4a6cfa;
$primary-light: #eef1ff;
$secondary-color: #f8f9fd;
$text-color: #333333;
$text-light: #6e7191;
$border-color: #e4e8f7;
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

.help-container {
  font-family: $font-family;
  
  .help-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 20px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 8px;
    }
    
    p {
      font-size: 14px;
      color: $text-light;
      line-height: 1.5;
    }
  }
  
  .help-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
    
    .help-section {
      margin-bottom: -0.5rem;
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
        margin-bottom: 12px;
      }
      
      .help-items {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .help-item {
          background-color: $secondary-color;
          border: 1px solid $border-color;
          border-radius: 8px;
          padding: 12px 16px;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: $primary-light;
            border-color: rgba($primary-color, 0.3);
            transform: translateY(-2px);
          }
          
          p {
            font-size: 14px;
            color: $text-color;
            margin: 0;
          }
        }
      }
    }
  }
}
