import { Statut } from "src/app/winpharm/enums/common/Statut.enum";
import { Operateur } from "../../common/operateur.model";
import { Fournisseur } from "../../tiers/fournisseur/fournisseur.model";
import { CategorieProduit } from "../base/categorieProduit.model";
import { FamilleTarifaire } from "../base/familleTarifaire.model";
import { FormeProduit } from "../base/formeProduit.model";
import { Produit } from "../base/produit.model";
import { Depot } from "./depot.model";



export interface InitStockDetails {

    id?: number;
    codeCtgr: string
    codeFrm: string
    codeFt: string
    codeLabo: string
    codePrd: string
    ctgr: CategorieProduit,
    datePeremption: any
    dsgnPrd: string
    frm: FormeProduit,
    ft: FamilleTarifaire
    labo: Fournisseur;
    mntLigneAchatTtc: number
    mntLigneVenteTtc: number
    numLot: string
    pbrH: number
    pbrP: number
    prixAchatStd: number
    prixAchatTtc: number
    prixFabHt: number
    prixHosp: number
    prixVenteStd: number
    prixVenteTtc: number
    produit: Produit;
    quantite: number
    tauxMarge: number
    tauxTva: number
    numLigne: number;
    codeBarre?: string;

    //** External Field  */ 
    codeBarreExtern?: string;
    codePrdExtern?: string;
    codePrdInternBackup?: string;
    datePeremptionExtern?: string;
    dsgnPrdExtern?: string;

    mntLigneAchatTtcExtern?: number;
    mntLigneVenteTtcExtern?: number;
    numLotExtern?: string;
    prixAchatTtcExtern?: number;
    prixVenteTtcExtern?: number;
    quantiteExtern?: number;

    // New field with default value
    valider?: boolean;
}



export class InitStock {

    id?: number;
    dateCreation: any;
    dateAnnulation?: any;
    dateValidation?: any;
    depot: Depot;
    mntPrixAchatTtc: number;
    mntPrixVenteTtc: number;
    details: InitStockDetails[]
    nbrLignes: number;
    operateur: Operateur;
    statut: Statut;
    totalQte: number;


    /// 
    isLoadedFromFile?: boolean = false;


}