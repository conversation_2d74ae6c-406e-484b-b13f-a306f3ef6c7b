{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abderrahmane_ouhna/Agent_ui/Agentic_ai_chatboot-mcp/angular-openai-chat-2/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-widget.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./chat-widget.component.scss?ngResource\";\n// chat-widget.component.ts\nimport { Component } from '@angular/core';\nimport { OpenaiService } from '../services/openai.service';\nimport { MessageStorageService } from '../services/message-storage.service';\nlet ChatWidgetComponent = class ChatWidgetComponent {\n  constructor(openaiService, messageStorage) {\n    this.openaiService = openaiService;\n    this.messageStorage = messageStorage;\n    this.isExpanded = false;\n    this.activeTab = 'home'; // Change default to 'home'\n    this.showConversation = false;\n    this.conversations = [];\n    this.currentConversationId = null;\n    this.currentMessages = [];\n    this.isLoading = false;\n    this.threadId = null;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.conversations = yield _this.messageStorage.getConversations();\n      if (_this.conversations.length === 0) {\n        // Create a default conversation if none exists\n        const id = yield _this.messageStorage.createConversation('Nouvelle Conversation');\n        _this.conversations = yield _this.messageStorage.getConversations();\n        _this.currentConversationId = id;\n      } else {\n        _this.currentConversationId = _this.conversations[0].id;\n      }\n    })();\n  }\n  toggleChat() {\n    this.isExpanded = !this.isExpanded;\n    if (!this.isExpanded) {\n      // Reset to home view when closing\n      this.activeTab = 'home';\n      this.showConversation = false;\n    }\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n    if (tab === 'chat') {\n      // Reset to conversation list when switching to chat tab\n      this.showConversation = false;\n    }\n  }\n  // Add this method to handle question from home screen\n  handleAskQuestion(question) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.activeTab = 'chat';\n      if (_this2.currentConversationId) {\n        yield _this2.loadConversation(_this2.currentConversationId);\n      } else {\n        yield _this2.startNewConversation();\n      }\n      _this2.showConversation = true;\n      if (question) {\n        _this2.sendMessage(question);\n      }\n    })();\n  }\n  // Add this method to handle article selection\n  handleSelectArticle(articleId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // Here you could navigate to article content\n      // For now, let's just start a conversation about the article\n      _this3.activeTab = 'chat';\n      yield _this3.startNewConversation();\n      _this3.showConversation = true;\n      let message = '';\n      switch (articleId) {\n        case 'spring25':\n          message = \"Tell me about the Built For You Spring 25 AI innovations\";\n          break;\n        case '2025report':\n          message = \"What's in the 2025 Customer Service Transformation Report?\";\n          break;\n        case 'community':\n          message = \"How can I join the Intercom Community?\";\n          break;\n        default:\n          message = \"I'd like to learn more about \" + articleId;\n      }\n      _this3.sendMessage(message);\n    })();\n  }\n  startNewConversation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const id = yield _this4.messageStorage.createConversation();\n      _this4.conversations = yield _this4.messageStorage.getConversations();\n      _this4.loadAndShowConversation(id);\n    })();\n  }\n  loadConversation(id) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      _this5.currentConversationId = id;\n      _this5.currentMessages = yield _this5.messageStorage.getMessages(id);\n    })();\n  }\n  loadAndShowConversation(id) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      yield _this6.loadConversation(id);\n      _this6.showConversation = true;\n    })();\n  }\n  showConversationList() {\n    this.showConversation = false;\n  }\n  deleteConversation(id, event) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      yield _this7.messageStorage.deleteConversation(id);\n      _this7.conversations = yield _this7.messageStorage.getConversations();\n      if (id === _this7.currentConversationId) {\n        if (_this7.conversations.length > 0) {\n          _this7.currentConversationId = _this7.conversations[0].id;\n        } else {\n          const newId = yield _this7.messageStorage.createConversation();\n          _this7.conversations = yield _this7.messageStorage.getConversations();\n          _this7.currentConversationId = newId;\n        }\n      }\n    })();\n  }\n  sendMessage(content) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (!content.trim() || !_this8.currentConversationId) return;\n      // Sauvegarder le message utilisateur (comme avant)\n      const userMessage = {\n        conversationId: _this8.currentConversationId,\n        role: 'user',\n        content: content,\n        timestamp: new Date()\n      };\n      yield _this8.messageStorage.saveMessage(userMessage);\n      _this8.currentMessages = [..._this8.currentMessages, userMessage];\n      // Mise à jour du titre si nécessaire (comme avant)\n      const messages = yield _this8.messageStorage.getMessages(_this8.currentConversationId);\n      if (messages.length === 1) {\n        const title = content.split(' ').slice(0, 4).join(' ') + '...';\n        yield _this8.updateConversationTitle(_this8.currentConversationId, title);\n      }\n      // Appel à l'API avec réutilisation du thread\n      _this8.isLoading = true;\n      _this8.openaiService.sendMessage(content, _this8.threadId || undefined).subscribe({\n        next: function () {\n          var _ref = _asyncToGenerator(function* (response) {\n            if (response && response.message) {\n              // Stocker l'ID du thread pour la prochaine utilisation\n              _this8.threadId = response.threadId;\n              const assistantMessage = {\n                conversationId: _this8.currentConversationId,\n                role: 'assistant',\n                content: response.message,\n                timestamp: new Date()\n              };\n              yield _this8.messageStorage.saveMessage(assistantMessage);\n              _this8.currentMessages = [..._this8.currentMessages, assistantMessage];\n            } else {\n              console.error('Unexpected API response format:', response);\n              _this8.handleApiError('Unexpected response format from API');\n            }\n            _this8.isLoading = false;\n          });\n          return function next(_x) {\n            return _ref.apply(this, arguments);\n          };\n        }(),\n        error: error => {\n          _this8.handleApiError(error);\n          _this8.threadId = null; // Réinitialiser en cas d'erreur\n        }\n      });\n    })();\n  }\n  handleApiError(error) {\n    console.error('Error getting response:', error);\n    // Add an error message to the conversation\n    const errorMessage = {\n      conversationId: this.currentConversationId,\n      role: 'assistant',\n      content: \"I'm sorry, I encountered an error processing your request. Please check your API key configuration or try again later.\",\n      timestamp: new Date()\n    };\n    this.messageStorage.saveMessage(errorMessage);\n    this.currentMessages = [...this.currentMessages, errorMessage];\n    this.isLoading = false;\n  }\n  updateConversationTitle(id, title) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      // Find the conversation and update its title\n      const conversation = _this9.conversations.find(c => c.id === id);\n      if (conversation) {\n        conversation.title = title;\n        // Assuming you have a method to update a conversation\n        // If not, you'll need to add this to your MessageStorageService\n        yield _this9.messageStorage.updateConversation(conversation);\n        _this9.conversations = yield _this9.messageStorage.getConversations();\n      }\n    })();\n  }\n  handleHelpSelection(question) {\n    this.activeTab = 'chat';\n    this.loadAndShowConversation(this.currentConversationId);\n    this.sendMessage(question);\n  }\n  getCurrentConversationTitle() {\n    const conversation = this.conversations.find(c => c.id === this.currentConversationId);\n    return conversation ? conversation.title : 'Chat';\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    const now = new Date();\n    // If today, show time only\n    if (d.toDateString() === now.toDateString()) {\n      return d.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    // If this year, show month and day\n    if (d.getFullYear() === now.getFullYear()) {\n      return d.toLocaleDateString([], {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n    // Otherwise show date\n    return d.toLocaleDateString([], {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: OpenaiService\n    }, {\n      type: MessageStorageService\n    }];\n  }\n};\nChatWidgetComponent = __decorate([Component({\n  selector: 'app-chat-widget',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ChatWidgetComponent);\nexport { ChatWidgetComponent };", "map": {"version": 3, "names": ["Component", "OpenaiService", "MessageStorageService", "ChatWidgetComponent", "constructor", "openaiService", "messageStorage", "isExpanded", "activeTab", "showConversation", "conversations", "currentConversationId", "currentMessages", "isLoading", "threadId", "ngOnInit", "_this", "_asyncToGenerator", "getConversations", "length", "id", "createConversation", "toggleChat", "setActiveTab", "tab", "handleAskQuestion", "question", "_this2", "loadConversation", "startNewConversation", "sendMessage", "handleSelectArticle", "articleId", "_this3", "message", "_this4", "loadAndShowConversation", "_this5", "getMessages", "_this6", "showConversationList", "deleteConversation", "event", "_this7", "stopPropagation", "newId", "content", "_this8", "trim", "userMessage", "conversationId", "role", "timestamp", "Date", "saveMessage", "messages", "title", "split", "slice", "join", "updateConversationTitle", "undefined", "subscribe", "next", "_ref", "response", "assistant<PERSON><PERSON><PERSON>", "console", "error", "handleApiError", "_x", "apply", "arguments", "errorMessage", "_this9", "conversation", "find", "c", "updateConversation", "handleHelpSelection", "getCurrentConversationTitle", "formatDate", "date", "d", "now", "toDateString", "toLocaleTimeString", "hour", "minute", "getFullYear", "toLocaleDateString", "month", "day", "year", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\Agent_ui\\Agentic_ai_chatboot-mcp\\angular-openai-chat-2\\src\\app\\chat\\chat-widget\\chat-widget.component.ts"], "sourcesContent": ["// chat-widget.component.ts\r\nimport { Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { OpenaiService } from '../services/openai.service';\r\nimport { MessageStorageService, ChatMessage, Conversation } from '../services/message-storage.service';\r\n\r\n@Component({\r\n  selector: 'app-chat-widget',\r\n  templateUrl: './chat-widget.component.html',\r\n  styleUrls: ['./chat-widget.component.scss'],\r\n  // encapsulation: ViewEncapsulation.ShadowDom\r\n})\r\nexport class ChatWidgetComponent implements OnInit {\r\n  isExpanded = false;\r\n  activeTab = 'home'; // Change default to 'home'\r\n  showConversation = false;\r\n  conversations: Conversation[] = [];\r\n  currentConversationId: string | null = null;\r\n  currentMessages: ChatMessage[] = [];\r\n  isLoading = false;\r\n  threadId: string | null = null;\r\n\r\n  constructor(\r\n    private openaiService: OpenaiService,\r\n    private messageStorage: MessageStorageService\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    this.conversations = await this.messageStorage.getConversations();\r\n    \r\n    if (this.conversations.length === 0) {\r\n      // Create a default conversation if none exists\r\n      const id = await this.messageStorage.createConversation('Nouvelle Conversation');\r\n      this.conversations = await this.messageStorage.getConversations();\r\n      this.currentConversationId = id;\r\n    } else {\r\n      this.currentConversationId = this.conversations[0].id!;\r\n    }\r\n  }\r\n\r\n  toggleChat() {\r\n    this.isExpanded = !this.isExpanded;\r\n    if (!this.isExpanded) {\r\n      // Reset to home view when closing\r\n      this.activeTab = 'home';\r\n      this.showConversation = false;\r\n    }\r\n  }\r\n\r\n  setActiveTab(tab: string) {\r\n    this.activeTab = tab;\r\n    if (tab === 'chat') {\r\n      // Reset to conversation list when switching to chat tab\r\n      this.showConversation = false;\r\n    }\r\n  }\r\n\r\n\r\n  // Add this method to handle question from home screen\r\n  async handleAskQuestion(question: string) {\r\n    this.activeTab = 'chat';\r\n    \r\n    if (this.currentConversationId) {\r\n      await this.loadConversation(this.currentConversationId);\r\n    } else {\r\n      await this.startNewConversation();\r\n    }\r\n    \r\n    this.showConversation = true;\r\n    \r\n    if (question) {\r\n      this.sendMessage(question);\r\n    }\r\n  }\r\n  \r\n  // Add this method to handle article selection\r\n  async handleSelectArticle(articleId: string) {\r\n    // Here you could navigate to article content\r\n    // For now, let's just start a conversation about the article\r\n    this.activeTab = 'chat';\r\n    await this.startNewConversation();\r\n    this.showConversation = true;\r\n    \r\n    let message = '';\r\n    switch(articleId) {\r\n      case 'spring25':\r\n        message = \"Tell me about the Built For You Spring 25 AI innovations\";\r\n        break;\r\n      case '2025report':\r\n        message = \"What's in the 2025 Customer Service Transformation Report?\";\r\n        break;\r\n      case 'community':\r\n        message = \"How can I join the Intercom Community?\";\r\n        break;\r\n      default:\r\n        message = \"I'd like to learn more about \" + articleId;\r\n    }\r\n    \r\n    this.sendMessage(message);\r\n  }\r\n\r\n  async startNewConversation() {\r\n    const id = await this.messageStorage.createConversation();\r\n    this.conversations = await this.messageStorage.getConversations();\r\n    this.loadAndShowConversation(id);\r\n  }\r\n\r\n  async loadConversation(id: string) {\r\n    this.currentConversationId = id;\r\n    this.currentMessages = await this.messageStorage.getMessages(id);\r\n  }\r\n\r\n  async loadAndShowConversation(id: string) {\r\n    await this.loadConversation(id);\r\n    this.showConversation = true;\r\n  }\r\n\r\n  showConversationList() {\r\n    this.showConversation = false;\r\n  }\r\n\r\n  async deleteConversation(id: string, event: Event) {\r\n    event.stopPropagation();\r\n    await this.messageStorage.deleteConversation(id);\r\n    this.conversations = await this.messageStorage.getConversations();\r\n    \r\n    if (id === this.currentConversationId) {\r\n      if (this.conversations.length > 0) {\r\n        this.currentConversationId = this.conversations[0].id!;\r\n      } else {\r\n        const newId = await this.messageStorage.createConversation();\r\n        this.conversations = await this.messageStorage.getConversations();\r\n        this.currentConversationId = newId;\r\n      }\r\n    }\r\n  }\r\n\r\n  async sendMessage(content: string) {\r\n  if (!content.trim() || !this.currentConversationId) return;\r\n\r\n  // Sauvegarder le message utilisateur (comme avant)\r\n  const userMessage: ChatMessage = {\r\n    conversationId: this.currentConversationId,\r\n    role: 'user',\r\n    content: content,\r\n    timestamp: new Date()\r\n  };\r\n  \r\n  await this.messageStorage.saveMessage(userMessage);\r\n  this.currentMessages = [...this.currentMessages, userMessage];\r\n\r\n  // Mise à jour du titre si nécessaire (comme avant)\r\n  const messages = await this.messageStorage.getMessages(this.currentConversationId);\r\n  if (messages.length === 1) {\r\n    const title = content.split(' ').slice(0, 4).join(' ') + '...';\r\n    await this.updateConversationTitle(this.currentConversationId, title);\r\n  }\r\n\r\n  // Appel à l'API avec réutilisation du thread\r\n  this.isLoading = true;\r\n  this.openaiService.sendMessage(content, this.threadId || undefined).subscribe({\r\n    next: async (response) => {\r\n      if (response && response.message) {\r\n        // Stocker l'ID du thread pour la prochaine utilisation\r\n        this.threadId = response.threadId;\r\n        \r\n        const assistantMessage: ChatMessage = {\r\n          conversationId: this.currentConversationId!,\r\n          role: 'assistant',\r\n          content: response.message,\r\n          timestamp: new Date()\r\n        };\r\n        \r\n        await this.messageStorage.saveMessage(assistantMessage);\r\n        this.currentMessages = [...this.currentMessages, assistantMessage];\r\n      } else {\r\n        console.error('Unexpected API response format:', response);\r\n        this.handleApiError('Unexpected response format from API');\r\n      }\r\n      this.isLoading = false;\r\n    },\r\n    error: (error) => {\r\n      this.handleApiError(error);\r\n      this.threadId = null; // Réinitialiser en cas d'erreur\r\n    }\r\n  });\r\n}\r\n\r\n  handleApiError(error: any) {\r\n    console.error('Error getting response:', error);\r\n    // Add an error message to the conversation\r\n    const errorMessage: ChatMessage = {\r\n      conversationId: this.currentConversationId!,\r\n      role: 'assistant',\r\n      content: \"I'm sorry, I encountered an error processing your request. Please check your API key configuration or try again later.\",\r\n      timestamp: new Date()\r\n    };\r\n    \r\n    this.messageStorage.saveMessage(errorMessage);\r\n    this.currentMessages = [...this.currentMessages, errorMessage];\r\n    this.isLoading = false;\r\n  }\r\n\r\n  async updateConversationTitle(id: string, title: string) {\r\n    // Find the conversation and update its title\r\n    const conversation = this.conversations.find(c => c.id === id);\r\n    if (conversation) {\r\n      conversation.title = title;\r\n      // Assuming you have a method to update a conversation\r\n      // If not, you'll need to add this to your MessageStorageService\r\n      await this.messageStorage.updateConversation(conversation);\r\n      this.conversations = await this.messageStorage.getConversations();\r\n    }\r\n  }\r\n\r\n  handleHelpSelection(question: string) {\r\n    this.activeTab = 'chat';\r\n    this.loadAndShowConversation(this.currentConversationId!);\r\n    this.sendMessage(question);\r\n  }\r\n\r\n  getCurrentConversationTitle(): string {\r\n    const conversation = this.conversations.find(c => c.id === this.currentConversationId);\r\n    return conversation ? conversation.title : 'Chat';\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    const d = new Date(date);\r\n    const now = new Date();\r\n    \r\n    // If today, show time only\r\n    if (d.toDateString() === now.toDateString()) {\r\n      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n    \r\n    // If this year, show month and day\r\n    if (d.getFullYear() === now.getFullYear()) {\r\n      return d.toLocaleDateString([], { month: 'short', day: 'numeric' });\r\n    }\r\n    \r\n    // Otherwise show date\r\n    return d.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' });\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA;AACA,SAASA,SAAS,QAAmC,eAAe;AACpE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,qBAAqB,QAAmC,qCAAqC;AAQ/F,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAU9BC,YACUC,aAA4B,EAC5BC,cAAqC;IADrC,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAXxB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,MAAM,CAAC,CAAC;IACpB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,qBAAqB,GAAkB,IAAI;IAC3C,KAAAC,eAAe,GAAkB,EAAE;IACnC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,QAAQ,GAAkB,IAAI;EAK3B;EAEGC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACN,aAAa,SAASM,KAAI,CAACV,cAAc,CAACY,gBAAgB,EAAE;MAEjE,IAAIF,KAAI,CAACN,aAAa,CAACS,MAAM,KAAK,CAAC,EAAE;QACnC;QACA,MAAMC,EAAE,SAASJ,KAAI,CAACV,cAAc,CAACe,kBAAkB,CAAC,uBAAuB,CAAC;QAChFL,KAAI,CAACN,aAAa,SAASM,KAAI,CAACV,cAAc,CAACY,gBAAgB,EAAE;QACjEF,KAAI,CAACL,qBAAqB,GAAGS,EAAE;MACjC,CAAC,MAAM;QACLJ,KAAI,CAACL,qBAAqB,GAAGK,KAAI,CAACN,aAAa,CAAC,CAAC,CAAC,CAACU,EAAG;MACxD;IAAC;EACH;EAEAE,UAAUA,CAAA;IACR,IAAI,CAACf,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB;MACA,IAAI,CAACC,SAAS,GAAG,MAAM;MACvB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEAc,YAAYA,CAACC,GAAW;IACtB,IAAI,CAAChB,SAAS,GAAGgB,GAAG;IACpB,IAAIA,GAAG,KAAK,MAAM,EAAE;MAClB;MACA,IAAI,CAACf,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAGA;EACMgB,iBAAiBA,CAACC,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAV,iBAAA;MACtCU,MAAI,CAACnB,SAAS,GAAG,MAAM;MAEvB,IAAImB,MAAI,CAAChB,qBAAqB,EAAE;QAC9B,MAAMgB,MAAI,CAACC,gBAAgB,CAACD,MAAI,CAAChB,qBAAqB,CAAC;MACzD,CAAC,MAAM;QACL,MAAMgB,MAAI,CAACE,oBAAoB,EAAE;MACnC;MAEAF,MAAI,CAAClB,gBAAgB,GAAG,IAAI;MAE5B,IAAIiB,QAAQ,EAAE;QACZC,MAAI,CAACG,WAAW,CAACJ,QAAQ,CAAC;MAC5B;IAAC;EACH;EAEA;EACMK,mBAAmBA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MACzC;MACA;MACAgB,MAAI,CAACzB,SAAS,GAAG,MAAM;MACvB,MAAMyB,MAAI,CAACJ,oBAAoB,EAAE;MACjCI,MAAI,CAACxB,gBAAgB,GAAG,IAAI;MAE5B,IAAIyB,OAAO,GAAG,EAAE;MAChB,QAAOF,SAAS;QACd,KAAK,UAAU;UACbE,OAAO,GAAG,0DAA0D;UACpE;QACF,KAAK,YAAY;UACfA,OAAO,GAAG,4DAA4D;UACtE;QACF,KAAK,WAAW;UACdA,OAAO,GAAG,wCAAwC;UAClD;QACF;UACEA,OAAO,GAAG,+BAA+B,GAAGF,SAAS;MACzD;MAEAC,MAAI,CAACH,WAAW,CAACI,OAAO,CAAC;IAAC;EAC5B;EAEML,oBAAoBA,CAAA;IAAA,IAAAM,MAAA;IAAA,OAAAlB,iBAAA;MACxB,MAAMG,EAAE,SAASe,MAAI,CAAC7B,cAAc,CAACe,kBAAkB,EAAE;MACzDc,MAAI,CAACzB,aAAa,SAASyB,MAAI,CAAC7B,cAAc,CAACY,gBAAgB,EAAE;MACjEiB,MAAI,CAACC,uBAAuB,CAAChB,EAAE,CAAC;IAAC;EACnC;EAEMQ,gBAAgBA,CAACR,EAAU;IAAA,IAAAiB,MAAA;IAAA,OAAApB,iBAAA;MAC/BoB,MAAI,CAAC1B,qBAAqB,GAAGS,EAAE;MAC/BiB,MAAI,CAACzB,eAAe,SAASyB,MAAI,CAAC/B,cAAc,CAACgC,WAAW,CAAClB,EAAE,CAAC;IAAC;EACnE;EAEMgB,uBAAuBA,CAAChB,EAAU;IAAA,IAAAmB,MAAA;IAAA,OAAAtB,iBAAA;MACtC,MAAMsB,MAAI,CAACX,gBAAgB,CAACR,EAAE,CAAC;MAC/BmB,MAAI,CAAC9B,gBAAgB,GAAG,IAAI;IAAC;EAC/B;EAEA+B,oBAAoBA,CAAA;IAClB,IAAI,CAAC/B,gBAAgB,GAAG,KAAK;EAC/B;EAEMgC,kBAAkBA,CAACrB,EAAU,EAAEsB,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MAC/CyB,KAAK,CAACE,eAAe,EAAE;MACvB,MAAMD,MAAI,CAACrC,cAAc,CAACmC,kBAAkB,CAACrB,EAAE,CAAC;MAChDuB,MAAI,CAACjC,aAAa,SAASiC,MAAI,CAACrC,cAAc,CAACY,gBAAgB,EAAE;MAEjE,IAAIE,EAAE,KAAKuB,MAAI,CAAChC,qBAAqB,EAAE;QACrC,IAAIgC,MAAI,CAACjC,aAAa,CAACS,MAAM,GAAG,CAAC,EAAE;UACjCwB,MAAI,CAAChC,qBAAqB,GAAGgC,MAAI,CAACjC,aAAa,CAAC,CAAC,CAAC,CAACU,EAAG;QACxD,CAAC,MAAM;UACL,MAAMyB,KAAK,SAASF,MAAI,CAACrC,cAAc,CAACe,kBAAkB,EAAE;UAC5DsB,MAAI,CAACjC,aAAa,SAASiC,MAAI,CAACrC,cAAc,CAACY,gBAAgB,EAAE;UACjEyB,MAAI,CAAChC,qBAAqB,GAAGkC,KAAK;QACpC;MACF;IAAC;EACH;EAEMf,WAAWA,CAACgB,OAAe;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MACjC,IAAI,CAAC6B,OAAO,CAACE,IAAI,EAAE,IAAI,CAACD,MAAI,CAACpC,qBAAqB,EAAE;MAEpD;MACA,MAAMsC,WAAW,GAAgB;QAC/BC,cAAc,EAAEH,MAAI,CAACpC,qBAAqB;QAC1CwC,IAAI,EAAE,MAAM;QACZL,OAAO,EAAEA,OAAO;QAChBM,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED,MAAMN,MAAI,CAACzC,cAAc,CAACgD,WAAW,CAACL,WAAW,CAAC;MAClDF,MAAI,CAACnC,eAAe,GAAG,CAAC,GAAGmC,MAAI,CAACnC,eAAe,EAAEqC,WAAW,CAAC;MAE7D;MACA,MAAMM,QAAQ,SAASR,MAAI,CAACzC,cAAc,CAACgC,WAAW,CAACS,MAAI,CAACpC,qBAAqB,CAAC;MAClF,IAAI4C,QAAQ,CAACpC,MAAM,KAAK,CAAC,EAAE;QACzB,MAAMqC,KAAK,GAAGV,OAAO,CAACW,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK;QAC9D,MAAMZ,MAAI,CAACa,uBAAuB,CAACb,MAAI,CAACpC,qBAAqB,EAAE6C,KAAK,CAAC;MACvE;MAEA;MACAT,MAAI,CAAClC,SAAS,GAAG,IAAI;MACrBkC,MAAI,CAAC1C,aAAa,CAACyB,WAAW,CAACgB,OAAO,EAAEC,MAAI,CAACjC,QAAQ,IAAI+C,SAAS,CAAC,CAACC,SAAS,CAAC;QAC5EC,IAAI;UAAA,IAAAC,IAAA,GAAA/C,iBAAA,CAAE,WAAOgD,QAAQ,EAAI;YACvB,IAAIA,QAAQ,IAAIA,QAAQ,CAAC/B,OAAO,EAAE;cAChC;cACAa,MAAI,CAACjC,QAAQ,GAAGmD,QAAQ,CAACnD,QAAQ;cAEjC,MAAMoD,gBAAgB,GAAgB;gBACpChB,cAAc,EAAEH,MAAI,CAACpC,qBAAsB;gBAC3CwC,IAAI,EAAE,WAAW;gBACjBL,OAAO,EAAEmB,QAAQ,CAAC/B,OAAO;gBACzBkB,SAAS,EAAE,IAAIC,IAAI;eACpB;cAED,MAAMN,MAAI,CAACzC,cAAc,CAACgD,WAAW,CAACY,gBAAgB,CAAC;cACvDnB,MAAI,CAACnC,eAAe,GAAG,CAAC,GAAGmC,MAAI,CAACnC,eAAe,EAAEsD,gBAAgB,CAAC;YACpE,CAAC,MAAM;cACLC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEH,QAAQ,CAAC;cAC1DlB,MAAI,CAACsB,cAAc,CAAC,qCAAqC,CAAC;YAC5D;YACAtB,MAAI,CAAClC,SAAS,GAAG,KAAK;UACxB,CAAC;UAAA,gBAnBDkD,IAAIA,CAAAO,EAAA;YAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;UAAA;QAAA,GAmBH;QACDJ,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAI,CAACsB,cAAc,CAACD,KAAK,CAAC;UAC1BrB,MAAI,CAACjC,QAAQ,GAAG,IAAI,CAAC,CAAC;QACxB;OACD,CAAC;IAAC;EACL;EAEEuD,cAAcA,CAACD,KAAU;IACvBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C;IACA,MAAMK,YAAY,GAAgB;MAChCvB,cAAc,EAAE,IAAI,CAACvC,qBAAsB;MAC3CwC,IAAI,EAAE,WAAW;MACjBL,OAAO,EAAE,wHAAwH;MACjIM,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED,IAAI,CAAC/C,cAAc,CAACgD,WAAW,CAACmB,YAAY,CAAC;IAC7C,IAAI,CAAC7D,eAAe,GAAG,CAAC,GAAG,IAAI,CAACA,eAAe,EAAE6D,YAAY,CAAC;IAC9D,IAAI,CAAC5D,SAAS,GAAG,KAAK;EACxB;EAEM+C,uBAAuBA,CAACxC,EAAU,EAAEoC,KAAa;IAAA,IAAAkB,MAAA;IAAA,OAAAzD,iBAAA;MACrD;MACA,MAAM0D,YAAY,GAAGD,MAAI,CAAChE,aAAa,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,EAAE,KAAKA,EAAE,CAAC;MAC9D,IAAIuD,YAAY,EAAE;QAChBA,YAAY,CAACnB,KAAK,GAAGA,KAAK;QAC1B;QACA;QACA,MAAMkB,MAAI,CAACpE,cAAc,CAACwE,kBAAkB,CAACH,YAAY,CAAC;QAC1DD,MAAI,CAAChE,aAAa,SAASgE,MAAI,CAACpE,cAAc,CAACY,gBAAgB,EAAE;MACnE;IAAC;EACH;EAEA6D,mBAAmBA,CAACrD,QAAgB;IAClC,IAAI,CAAClB,SAAS,GAAG,MAAM;IACvB,IAAI,CAAC4B,uBAAuB,CAAC,IAAI,CAACzB,qBAAsB,CAAC;IACzD,IAAI,CAACmB,WAAW,CAACJ,QAAQ,CAAC;EAC5B;EAEAsD,2BAA2BA,CAAA;IACzB,MAAML,YAAY,GAAG,IAAI,CAACjE,aAAa,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,EAAE,KAAK,IAAI,CAACT,qBAAqB,CAAC;IACtF,OAAOgE,YAAY,GAAGA,YAAY,CAACnB,KAAK,GAAG,MAAM;EACnD;EAEAyB,UAAUA,CAACC,IAAU;IACnB,MAAMC,CAAC,GAAG,IAAI9B,IAAI,CAAC6B,IAAI,CAAC;IACxB,MAAME,GAAG,GAAG,IAAI/B,IAAI,EAAE;IAEtB;IACA,IAAI8B,CAAC,CAACE,YAAY,EAAE,KAAKD,GAAG,CAACC,YAAY,EAAE,EAAE;MAC3C,OAAOF,CAAC,CAACG,kBAAkB,CAAC,EAAE,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;IACzE;IAEA;IACA,IAAIL,CAAC,CAACM,WAAW,EAAE,KAAKL,GAAG,CAACK,WAAW,EAAE,EAAE;MACzC,OAAON,CAAC,CAACO,kBAAkB,CAAC,EAAE,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAS,CAAE,CAAC;IACrE;IAEA;IACA,OAAOT,CAAC,CAACO,kBAAkB,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAS,CAAE,CAAC;EACtF;;;;;;;;;AAtOW1F,mBAAmB,GAAA2F,UAAA,EAN/B9F,SAAS,CAAC;EACT+F,QAAQ,EAAE,iBAAiB;EAC3BC,QAAA,EAAAC,oBAA2C;;CAG5C,CAAC,C,EACW9F,mBAAmB,CAuO/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}