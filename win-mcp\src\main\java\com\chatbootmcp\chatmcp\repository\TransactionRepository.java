package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Transaction;
import com.chatbootmcp.chatmcp.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {
    List<Transaction> findByUserOrderByCreatedAtDesc(User user);
    List<Transaction> findByUserAndStatusOrderByCreatedAtDesc(User user, String status);
    List<Transaction> findByUserAndTransactionTypeOrderByCreatedAtDesc(User user, String transactionType);

    @Query("SELECT t FROM Transaction t WHERE t.user = :user AND t.createdAt >= :startDate ORDER BY t.createdAt DESC")
    List<Transaction> findByUserAndCreatedAtAfterOrderByCreatedAtDesc(@Param("user") User user, @Param("startDate") LocalDateTime startDate);

    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.user = :user AND t.status = 'COMPLETED' AND t.transactionType = 'CREDIT'")
    Double getTotalCreditsForUser(@Param("user") User user);

    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.user = :user AND t.status = 'COMPLETED' AND t.transactionType = 'DEBIT'")
    Double getTotalDebitsForUser(@Param("user") User user);

    List<Transaction> findTop10ByUserOrderByCreatedAtDesc(User user);
}
