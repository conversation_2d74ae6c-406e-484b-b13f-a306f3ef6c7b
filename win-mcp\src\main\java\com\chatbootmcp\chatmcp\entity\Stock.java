package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Stock entity representing product stock information
 */
@Entity
@Table(name = "stock")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Stock {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "qte_unit", precision = 15, scale = 2)
    private BigDecimal qteUnit;

    @Column(name = "qte_delta", precision = 15, scale = 2)
    private BigDecimal qteDelta;

    @Column(name = "prix_achat_ttc", precision = 15, scale = 2)
    private BigDecimal prixAchatTtc;

    @Column(name = "prix_vente_ttc", precision = 15, scale = 2)
    private BigDecimal prixVenteTtc;

    @Column(name = "prix_valo_ttc", precision = 15, scale = 2)
    private BigDecimal prixValoTtc;

    @Column(name = "date_peremption")
    private LocalDate datePeremption;

    @Column(name = "numero_lot", length = 100)
    private String numeroLot;

    @Column(name = "stock_indicateur", length = 50)
    private String stockIndicateur;

    @Column(name = "flag_empl", length = 50)
    private String flagEmpl;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "depot_id")
    private Depot depot;
}
