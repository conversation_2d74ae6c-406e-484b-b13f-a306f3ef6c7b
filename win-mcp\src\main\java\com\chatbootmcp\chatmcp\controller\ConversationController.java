package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.dto.request.ConversationRequest;
import com.chatbootmcp.chatmcp.dto.response.ConversationResponse;
import com.chatbootmcp.chatmcp.service.ConversationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/conversations")
public class ConversationController {

    @Autowired
    private ConversationService conversationService;

    @GetMapping
    public ResponseEntity<List<ConversationResponse>> getUserConversations(
            @AuthenticationPrincipal UserDetails userDetails) {
        return ResponseEntity.ok(conversationService.getUserConversations(userDetails.getUsername()));
    }

    @PostMapping
    public ResponseEntity<ConversationResponse> createConversation(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestBody ConversationRequest request) {
        return ResponseEntity.ok(conversationService.createConversation(userDetails.getUsername(), request));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ConversationResponse> getConversation(
            @PathVariable Long id,
            @AuthenticationPrincipal UserDetails userDetails) {
        return ResponseEntity.ok(conversationService.getConversation(id, userDetails.getUsername()));
    }
}
