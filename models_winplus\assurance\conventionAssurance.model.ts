import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { TypeRembPrix } from 'src/app/winpharm/enums/assurance/TypeRembPrix.enum';
import { TypeRembTaux } from 'src/app/winpharm/enums/assurance/TypeRembTaux.enum';
import { TypeFacturationTPA } from 'src/app/winpharm/enums/vente/TypeFacturationTPA.enum';

// import { Moment } from 'moment';

import { IndicateurConvention } from './indicateurConvention.model';
import { OrganismeAssurance } from 'src/app/winpharm/models/tiers/organisme/organismeAssurance.model';


export class ConventionAssurance { 
    audited?: boolean;
    dateCreation?: any;
    estActif?: boolean;
    id?: number;
    indicateur?: IndicateurConvention;
    nomConvention?: string;
    organismeAssurance?: OrganismeAssurance;
    plafond?: number;
    statut?: Statut;
    tauxRemb?: number;
    typeFacturation?: TypeFacturationTPA;
    typeRembPrix?: TypeRembPrix;
    typeRembTaux?: TypeRembTaux;
    userModifiable?: boolean;
}
