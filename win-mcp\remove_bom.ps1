# PowerShell script to remove BOM from Java files

Write-Host "Checking for BOM in Java files..."

$javaFiles = Get-ChildItem -Path "src" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    # Read the first few bytes of the file
    $bytes = Get-Content -Path $file.FullName -Encoding Byte -TotalCount 3
    
    # Check for UTF-8 BOM (EF BB BF)
    if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
        Write-Host "BOM found in: $($file.FullName)"
        
        # Read the file content
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Remove the BOM character
        $content = $content.TrimStart([char]0xFEFF)
        
        # Write the content back to a new file
        $content | Out-File -FilePath "$($file.FullName).new" -Encoding UTF8NoBOM
        
        # Replace the original file
        Remove-Item -Path $file.FullName
        Move-Item -Path "$($file.FullName).new" -Destination $file.FullName
        
        Write-Host "BOM removed from: $($file.FullName)"
    }
}

Write-Host "BOM removal completed!"
