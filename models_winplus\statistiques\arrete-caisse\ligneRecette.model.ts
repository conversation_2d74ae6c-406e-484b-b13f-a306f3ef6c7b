// Define the structure for SyntheseArreteCaisseVente
export interface SyntheseArreteCaisseVente {
    totalMontantReglementCredit: number;
    totalMontantReglementCreditDiffere: number;
    totalMontantReglementDiffere: number;
    totalMontantRemiseVente: number;
    totalMontantRemiseVenteNetTtcComptant: number;
    totalMontantRemiseVenteNetTtcCredit: number;
    totalMontantRemiseVenteNetTtcDiffere: number;
    totalMontantRemisesReglement: number;
    totalMontantTpa: number;
    totalMontantTpaPartClient: number;
    totalMontantTpaPartOrganisme: number;
    totalMontantTpaReglements: number;
    totalMontantTva: number;
    totalMontantVenteBrutTtc: number;
    totalMontantVenteBrutTtcComptant: number;
    totalMontantVenteBrutTtcCredit: number;
    totalMontantVenteBrutTtcDiffere: number;
    totalMontantVenteNetTtc: number;
    totalMontantVenteNetTtcComptant: number;
    totalMontantVenteNetTtcCredit: number;
    totalMontantVenteNetTtcDiffere: number;
    totalNombreVentes: number; // Integer type should map to number in TypeScript
    totalRecetteCaisse: number;
}

// Define the structure for LigneRecette
export interface LigneRecette {
    syntheseArreteCaisse: SyntheseArreteCaisseVente;
    timeGroup: string; // Date-Time string
}

