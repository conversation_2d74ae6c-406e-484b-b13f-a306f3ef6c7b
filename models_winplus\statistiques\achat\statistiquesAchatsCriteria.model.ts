import { TypeBla } from 'src/app/winpharm/enums/achat-avoir/TypeBla.enum';
import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { TypeStatutReglement } from 'src/app/winpharm/enums/achat-avoir/TypeStatutReglement.enum';

// import { Moment } from 'moment';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Operateur } from 'src/app/winpharm/models/common/operateur.model';
import { Produit } from '../../produit/base/produit.model';


export class StatistiquesAchatsCriteria {
    categorieProduit?: CategorieProduit;
    dateDebut?: any;
    dateFin?: any;
    etatReglementFacture?: TypeStatutReglement;
    familleTarifaire?: FamilleTarifaire;
    forme?: FormeProduit;
    fournisseur?: Fournisseur;
    operateur?: Operateur;
    statut?: Statut;
    typeBla?: TypeBla;
    produit?: Produit;

    produitIdExistsDetail?: number
}

