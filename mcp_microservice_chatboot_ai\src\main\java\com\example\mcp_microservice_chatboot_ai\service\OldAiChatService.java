package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.ChatMessage;
import com.example.mcp_microservice_chatboot_ai.model.ChatMessage.MessageRole;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatResponse;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
//import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.service.OpenAiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.context.annotation.Description;
import java.util.function.Function;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Service for handling AI chat functionality.
 */

/**
 * Role: Service for handling AI chat functionality
    Purpose:
    Processes chat requests using the OpenAI model
    Implements tool callbacks for the MCP server
    Provides methods for getting weather and user information
    Creates system prompts to guide AI behavior
 */
@Service
public class AiChatService {

    private final OpenAiService openAiService;
    private final ChatMcpApiService chatMcpApiService;

    @Value("${openai.model}")
    private String model;

    @Value("${openai.temperature}")
    private float temperature;

    @Value("${openai.max-tokens}")
    private int maxTokens;

    @Autowired
    public AiChatService(OpenAiService openAiService, ChatMcpApiService chatMcpApiService) {
        this.openAiService = openAiService;
        this.chatMcpApiService = chatMcpApiService;
    }

    // Map to track conversation state for each user
    private final Map<String, ConversationState> conversationStates = new HashMap<>();

    // Map to store the last question for each conversation
    private final Map<String, String> lastQuestions = new HashMap<>();

    // Enum to track the state of a conversation
    private enum ConversationState {
        INITIAL,           // Initial state
        AWAITING_SOURCE,   // Waiting for user to choose a source (1 or 2)
        USING_DATABASE,    // Using database information
        USING_GENERAL      // Using general knowledge
    }

    // We'll let the AI decide if a message is a greeting based on the system prompt

    /**
     * Processes a chat request and generates a response using the AI model.
     * Always asks for the source for every non-friendly message.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response
     */
    public Mono<ChatResponse> processChat(ChatRequest chatRequest) {
        String userMessage = chatRequest.getContent();
        String conversationId = chatRequest.getConversationId();
        String username = chatRequest.getUsername();

        // Get or create conversation state
        ConversationState state = conversationStates.getOrDefault(conversationId, ConversationState.INITIAL);

        // Create the system prompt based on the current state
        String systemPromptText = createSystemPrompt();

        // Debug logging
        System.out.println("Processing chat: conversationId=" + conversationId + ", state=" + state + ", message=" + userMessage);

        // Handle different conversation states
        if (state == ConversationState.AWAITING_SOURCE) {
            if (userMessage.trim().equals("1")) {
                // User chose database source
                // Reset the conversation state to INITIAL after answering
                // This will force the AI to ask for the source again for the next question
                conversationStates.put(conversationId, ConversationState.INITIAL);
                // Get the last question from the map
                String lastQuestion = lastQuestions.getOrDefault(conversationId, "");
                System.out.println("User chose database source for question: " + lastQuestion);
                return generateDatabaseResponse(lastQuestion, conversationId, username, systemPromptText);
            } else if (userMessage.trim().equals("2")) {
                // User chose general knowledge
                // Reset the conversation state to INITIAL after answering
                // This will force the AI to ask for the source again for the next question
                conversationStates.put(conversationId, ConversationState.INITIAL);
                // Get the last question from the map
                String lastQuestion = lastQuestions.getOrDefault(conversationId, "");
                System.out.println("User chose general knowledge for question: " + lastQuestion);
                return generateGeneralKnowledgeResponse(lastQuestion, conversationId, username, systemPromptText);
            }
        }

        // For all other cases, let the AI decide if this is a greeting or a question
        return determineMessageTypeAndRespond(userMessage, conversationId, username, systemPromptText);
    }

    /**
     * Creates the system prompt for the AI.
     *
     * @return The system prompt template
     */
    private String createSystemPrompt() {
        return """
                Tu es un assistant intelligent pour une application de gestion de pharmacie appelée WinPlusPharma.
                Tu réponds toujours en français, de manière professionnelle, claire et concise.

                🎯 Ton rôle est d'aider l'utilisateur à retrouver des informations soit depuis :
                1. La base de données MCP (pour les informations personnelles)
                2. Tes connaissances générales issues de GPT

                ---

                🧠 Si l'utilisateur envoie un message **amical ou introductif** (ex. : "Bonjour", "Salut", "Hi", "Comment ça va ?"),
                et que ce message **ne contient pas de question ni de demande explicite**, réponds simplement de façon amicale
                et naturelle **sans interroger la base de données MCP**.

                ---

                ❓ Si l'utilisateur pose une **véritable question**, alors avant de répondre :
                Demande-lui de **choisir une source de réponse** en lui proposant une réponse simple sous forme de choix :

                > "Souhaitez-vous que je vous réponde en utilisant :
                > 1️⃣ La base de données MCP (pour vos informations personnelles)
                > 2️⃣ Mes connaissances générales publiques ?
                > Répondez simplement par **1** ou **2**."

                ---

                📂 Si l'utilisateur répond **1** (base de données MCP) :
                - Recherche uniquement dans la base de données MCP
                - Si aucune information ne peut être trouvée dans la base de données, réponds uniquement par :
                  > "Je n'ai pas trouvé la réponse dans la base de données MCP."

                🌍 Si l'utilisateur répond **2** (connaissances générales) :
                - Utilise uniquement ton propre savoir GPT
                - Ne fais **aucune supposition** à partir de la base de données MCP

                🚫 Tu ne dois jamais combiner les deux sources (MCP + GPT) dans une même réponse.
                Sois toujours clair sur la source utilisée.
                """;
    }

    /**
     * Determines if a message is a greeting or a question and responds accordingly.
     * Always asks for the source for non-greeting messages.
     *
     * @param userMessage The user's message
     * @param conversationId The conversation ID
     * @param username The username
     * @param systemMessage The system message
     * @return A Mono containing the chat response
     */
    private Mono<ChatResponse> determineMessageTypeAndRespond(String userMessage, String conversationId,
                                                           String username, String systemMessage) {
        // First, check for common greeting patterns
        String lowerCaseMessage = userMessage.toLowerCase().trim();
        if (isGreeting(lowerCaseMessage)) {
            System.out.println("Detected greeting message: " + userMessage);
            return generateGreetingResponse(userMessage, conversationId, username, systemMessage);
        }

        // If not a common greeting, use the AI to determine if it's a greeting or a question
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system",
            "Tu dois déterminer si le message de l'utilisateur est un simple message amical/introductif " +
            "ou s'il contient une véritable question ou demande d'information. " +
            "Les messages comme 'bonjour', 'salut', 'hello', 'hi', 'hey', 'comment ça va', 'comment vas-tu', " +
            "'how are you', 'ça va', 'bonsoir', 'je vais bien', 'i am fine', 'i am good', 'i am great', " +
            "'je suis bien', 'merci', 'thank you', etc. sont des messages amicaux. " +
            "Réponds uniquement par 'GREETING' pour un message amical sans question, " +
            "ou 'QUESTION' pour une question ou demande d'information."));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0) // Use low temperature for deterministic results
                .maxTokens(10) // We only need a short response
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent().trim().toUpperCase();
        System.out.println("AI message type detection: " + responseContent + " for message: " + userMessage);

        if (responseContent.contains("GREETING")) {
            // For greeting messages, respond in a friendly way without asking for source
            return generateGreetingResponse(userMessage, conversationId, username, systemMessage);
        } else {
            // For questions, store the question and ask the user to choose a source
            lastQuestions.put(conversationId, userMessage);
            conversationStates.put(conversationId, ConversationState.AWAITING_SOURCE);
            return generateSourceSelectionResponse(conversationId, username);
        }
    }

    /**
     * Checks if a message is a common greeting.
     *
     * @param message The message to check (lowercase)
     * @return True if the message is a greeting, false otherwise
     */
    private boolean isGreeting(String message) {
        // Common greetings in English and French
        String[] greetings = {
            // English greetings
            "hello", "hi", "hey", "how are you", "how's it going", "what's up", "good morning", "good afternoon", "good evening",
            "i'm fine", "i am fine", "i'm good", "i am good", "i'm great", "i am great", "fine thanks", "good thanks",
            "thank you", "thanks", "nice to meet you", "pleased to meet you", "good to see you",

            // French greetings
            "bonjour", "salut", "coucou", "comment ça va", "comment vas-tu", "ça va", "bonsoir", "je vais bien",
            "je suis bien", "bien merci", "merci", "enchanté", "ravi de vous rencontrer", "content de vous voir"
        };

        for (String greeting : greetings) {
            if (message.equals(greeting) ||
                message.startsWith(greeting + " ") ||
                message.endsWith(" " + greeting) ||
                message.contains(" " + greeting + " ")) {
                return true;
            }
        }

        // Check for common greeting patterns
        if (message.contains("i'm") && (message.contains("fine") || message.contains("good") || message.contains("great"))) {
            return true;
        }

        if (message.contains("je suis") && (message.contains("bien") || message.contains("bon"))) {
            return true;
        }

        return false;
    }

    /**
     * Generates a response for greeting messages.
     */
    private Mono<ChatResponse> generateGreetingResponse(String userMessage, String conversationId,
                                                       String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a response asking the user to select a source.
     */
    private Mono<ChatResponse> generateSourceSelectionResponse(String conversationId, String username) {
        String responseContent = """
                Souhaitez-vous que je vous réponde en utilisant :
                1️⃣ La base de données MCP (pour vos informations personnelles)
                2️⃣ Mes connaissances générales publiques ?
                Répondez simplement par **1** ou **2**.
                """;

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a response using the database.
     */
    private Mono<ChatResponse> generateDatabaseResponse(String userMessage, String conversationId,
                                                      String username, String systemMessage) {
        // Detect what type of data the user is asking for
        String dataType = detectDataType(userMessage);

        // Get the appropriate data based on the detected type
        return fetchSpecificData(username, dataType, userMessage)
                .doOnError(error -> {
                    System.out.println("Error fetching user data: " + error.getMessage());
                    error.printStackTrace();
                })
                .onErrorReturn("Erreur lors de la récupération des données utilisateur depuis la base de données MCP.")
                .flatMap(userData -> {
                    // Debug logging
                    System.out.println("User message: " + userMessage);
                    System.out.println("Data type detected: " + dataType);
                    System.out.println("User data: " + userData);

                    return generateOpenAIResponse(userMessage, conversationId, userData, systemMessage);
                });
    }

    /**
     * Detects what type of data the user is asking for based on keywords
     */
    private String detectDataType(String userMessage) {
        String message = userMessage.toLowerCase();

        // Invoice-related keywords
        if (message.contains("facture") || message.contains("invoice") ||
            message.contains("bill") || message.contains("facturation") ||
            message.contains("impayé") || message.contains("unpaid") ||
            message.contains("payé") || message.contains("paid") ||
            message.contains("due") || message.contains("échéance")) {
            return "invoices";
        }

        // Transaction-related keywords
        if (message.contains("transaction") || message.contains("paiement") ||
            message.contains("payment") || message.contains("achat") ||
            message.contains("purchase") || message.contains("dépense") ||
            message.contains("expense") || message.contains("historique") ||
            message.contains("history")) {
            return "transactions";
        }

        // Order-related keywords
        if (message.contains("commande") || message.contains("order") ||
            message.contains("livraison") || message.contains("delivery") ||
            message.contains("shipping") || message.contains("produit") ||
            message.contains("product")) {
            return "orders";
        }

        // Default to profile data for personal information
        return "profile";
    }

    /**
     * Fetches specific data based on the detected type
     */
    private Mono<String> fetchSpecificData(String username, String dataType, String userMessage) {
        switch (dataType) {
            case "invoices":
                return fetchInvoiceData(username);
            case "transactions":
                return fetchTransactionData(username);
            case "orders":
                return fetchOrderData(username);
            default:
                return chatMcpApiService.getUserData(username).map(this::formatUserData);
        }
    }

    /**
     * Fetches invoice data from the real API
     */
    private Mono<String> fetchInvoiceData(String username) {
        return chatMcpApiService.authenticateUser(username)
                .flatMap(token -> {
                    WebClient webClient = WebClient.builder()
                            .baseUrl("http://localhost:8080")
                            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                            .build();

                    return webClient.get()
                            .uri("/api/real-data/invoices?limit=10")
                            .header("Authorization", "Bearer " + token)
                            .retrieve()
                            .bodyToMono(List.class)
                            .map(this::formatInvoiceList);
                })
                .onErrorReturn("Erreur lors de la récupération des factures depuis la base de données MCP.");
    }

    /**
     * Fetches transaction data from the real API
     */
    private Mono<String> fetchTransactionData(String username) {
        return chatMcpApiService.authenticateUser(username)
                .flatMap(token -> {
                    WebClient webClient = WebClient.builder()
                            .baseUrl("http://localhost:8080")
                            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                            .build();

                    return webClient.get()
                            .uri("/api/real-data/transactions?limit=10")
                            .header("Authorization", "Bearer " + token)
                            .retrieve()
                            .bodyToMono(List.class)
                            .map(this::formatTransactionList);
                })
                .onErrorReturn("Erreur lors de la récupération des transactions depuis la base de données MCP.");
    }

    /**
     * Fetches order data from the real API
     */
    private Mono<String> fetchOrderData(String username) {
        return chatMcpApiService.authenticateUser(username)
                .flatMap(token -> {
                    WebClient webClient = WebClient.builder()
                            .baseUrl("http://localhost:8080")
                            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                            .build();

                    return webClient.get()
                            .uri("/api/real-data/orders?limit=10")
                            .header("Authorization", "Bearer " + token)
                            .retrieve()
                            .bodyToMono(List.class)
                            .map(this::formatOrderList);
                })
                .onErrorReturn("Erreur lors de la récupération des commandes depuis la base de données MCP.");
    }

    /**
     * Generates OpenAI response with user data
     */
    private Mono<ChatResponse> generateOpenAIResponse(String userMessage, String conversationId,
                                                     String userData, String systemMessage) {

        // Create a specific system message for database mode
        String databaseSystemMessage = "Tu es un assistant intelligent qui aide les utilisateurs à accéder à leurs informations personnelles " +
                "depuis la base de données MCP. Réponds toujours en français de manière claire et concise. " +
                "Tu as accès aux informations suivantes sur l'utilisateur: nom complet, adresse email, rôle, " +
                "pharmacie, adresse, numéro de téléphone, statut d'abonnement, date d'expiration de l'abonnement, " +
                "date de début d'abonnement, type d'abonnement, prix d'abonnement, dernière commande, historique des commandes, " +
                "nombre total de commandes, valeur moyenne des commandes, date de création du compte, dernière connexion, " +
                "statut du compte, type de compte, solde actuel, prochaine échéance de paiement, méthode de paiement, " +
                "date d'expiration de la carte, historique des factures. " +
                "Si tu ne peux pas trouver l'information demandée, dis simplement: " +
                "\"Je n'ai pas trouvé cette information dans la base de données MCP.\"";

        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", databaseSystemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Voici les informations de l'utilisateur depuis la base de données MCP:\n" + userData + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT ces informations. " +
                "Sois précis et concis dans ta réponse. " +
                "Si la question est 'Quel est mon nom complet?', réponds avec le nom complet de l'utilisateur. " +
                "Si la question est 'Quelle est mon adresse email?', réponds avec l'adresse email de l'utilisateur. " +
                "Si la question est 'Quel est mon rôle?', réponds avec le rôle de l'utilisateur. " +
                "Si la question est 'Quelle est ma pharmacie?', réponds avec la pharmacie de l'utilisateur. " +
                "Si la question est 'Quelle est mon adresse?', réponds avec l'adresse de l'utilisateur. " +
                "Si la question est 'Quel est mon statut d'abonnement?', réponds avec le statut d'abonnement de l'utilisateur. " +
                "Si la question est 'Quand expire mon abonnement?', réponds avec la date d'expiration de l'abonnement de l'utilisateur. " +
                "Si la question est 'Quelle est ma dernière commande?', réponds avec la dernière commande de l'utilisateur. " +
                "Si la question est 'Quelles sont mes commandes précédentes?', réponds avec l'historique des commandes de l'utilisateur. " +
                "Si tu ne trouves pas la réponse dans ces données, dis simplement: " +
                "\"Je n'ai pas trouvé la réponse dans la base de données MCP.\""));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0) // Use a deterministic temperature for database queries
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a response using general knowledge.
     */
    private Mono<ChatResponse> generateGeneralKnowledgeResponse(String userMessage, String conversationId,
                                                              String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT tes connaissances générales. " +
                "Ne fais AUCUNE supposition à partir de la base de données MCP."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates a prompt response for general knowledge mode.
     */
    private Mono<ChatResponse> generateGeneralKnowledgePromptResponse(String conversationId, String username, String systemMessage) {
        String responseContent = "Bien sûr, je suis prêt à répondre en utilisant uniquement mes connaissances générales. Quelle question aimeriez-vous poser ?";

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Gets weather information for a location.
     *
     * @param location The location to get weather for
     * @return The weather information
     */
    public String getWeather(String location) {
        // This is a mock implementation
        return "The weather in " + location + " is currently sunny with a temperature of 25°C.";
    }

    /**
     * Gets user information.
     * This tool fetches user information from the MCP database.
     *
     * @param username The username to get information for
     * @return The user information
     */
    public String getUserInfo(String username) {
        try {
            return chatMcpApiService.getUserData(username)
                    .map(response -> formatUserData(response))
                    .doOnError(error -> {
                        System.out.println("Error in getUserInfo: " + error.getMessage());
                        error.printStackTrace();
                    })
                    .onErrorReturn("Erreur lors de la récupération des informations de l'utilisateur depuis la base de données MCP: " + username)
                    .subscribeOn(Schedulers.boundedElastic())
                    .blockOptional()
                    .orElse("Aucune information trouvée pour l'utilisateur " + username);
        } catch (Exception e) {
            System.out.println("Exception in getUserInfo: " + e.getMessage());
            e.printStackTrace();
            return "Erreur lors de la récupération des informations de l'utilisateur: " + e.getMessage();
        }
    }



    /**
     * Formats user data for display.
     *
     * @param userData The user data to format
     * @return The formatted user data
     */
    @SuppressWarnings("unchecked")
    private String formatUserData(Object userData) {
        if (userData == null) {
            System.out.println("formatUserData: userData is null");
            return "Aucune donnée utilisateur disponible";
        }

        System.out.println("formatUserData: userData class = " + userData.getClass().getName());

        try {
            // Try to cast to Map if it's a map
            if (userData instanceof Map) {
                Map<String, Object> userDataMap = (Map<String, Object>) userData;
                System.out.println("formatUserData: userDataMap keys = " + userDataMap.keySet());
                StringBuilder sb = new StringBuilder();

                // Format user data in a readable way
                sb.append("Informations de l'utilisateur:\n\n");

                // Process basic user info first
                String[] priorityFields = {"username", "fullName", "email", "role", "pharmacy", "address", "phoneNumber"};
                for (String field : priorityFields) {
                    if (userDataMap.containsKey(field)) {
                        sb.append("- ").append(formatFieldName(field)).append(": ")
                          .append(userDataMap.get(field)).append("\n");
                    }
                }

                sb.append("\n");

                // Process subscription info
                if (userDataMap.containsKey("subscriptionStatus")) {
                    sb.append("Informations d'abonnement:\n");
                    sb.append("- Statut: ").append(userDataMap.get("subscriptionStatus")).append("\n");

                    if (userDataMap.containsKey("subscriptionExpiry")) {
                        sb.append("- Date d'expiration: ").append(userDataMap.get("subscriptionExpiry")).append("\n");
                    }

                    if (userDataMap.containsKey("subscriptionStartDate")) {
                        sb.append("- Date de début: ").append(userDataMap.get("subscriptionStartDate")).append("\n");
                    }

                    if (userDataMap.containsKey("subscriptionType")) {
                        sb.append("- Type d'abonnement: ").append(userDataMap.get("subscriptionType")).append("\n");
                    }

                    if (userDataMap.containsKey("subscriptionPrice")) {
                        sb.append("- Prix: ").append(userDataMap.get("subscriptionPrice")).append("\n");
                    }

                    sb.append("\n");
                }

                // Process order history
                if (userDataMap.containsKey("lastOrder") || userDataMap.containsKey("orderHistory")) {
                    sb.append("Historique des commandes:\n");

                    if (userDataMap.containsKey("lastOrder")) {
                        sb.append("- Dernière commande: ").append(userDataMap.get("lastOrder")).append("\n");
                    }

                    if (userDataMap.containsKey("orderHistory")) {
                        sb.append("- Commandes précédentes:\n");
                        List<String> orderHistory = (List<String>) userDataMap.get("orderHistory");
                        for (String order : orderHistory) {
                            sb.append("  * ").append(order).append("\n");
                        }
                    }

                    if (userDataMap.containsKey("totalOrders")) {
                        sb.append("- Nombre total de commandes: ").append(userDataMap.get("totalOrders")).append("\n");
                    }

                    if (userDataMap.containsKey("averageOrderValue")) {
                        sb.append("- Valeur moyenne des commandes: ").append(userDataMap.get("averageOrderValue")).append("\n");
                    }

                    sb.append("\n");
                }

                // Process account information
                if (userDataMap.containsKey("accountCreationDate") || userDataMap.containsKey("accountStatus") ||
                    userDataMap.containsKey("accountType") || userDataMap.containsKey("lastLogin")) {
                    sb.append("Informations du compte:\n");

                    if (userDataMap.containsKey("accountCreationDate")) {
                        sb.append("- Date de création: ").append(userDataMap.get("accountCreationDate")).append("\n");
                    }

                    if (userDataMap.containsKey("accountStatus")) {
                        sb.append("- Statut du compte: ").append(userDataMap.get("accountStatus")).append("\n");
                    }

                    if (userDataMap.containsKey("accountType")) {
                        sb.append("- Type de compte: ").append(userDataMap.get("accountType")).append("\n");
                    }

                    if (userDataMap.containsKey("lastLogin")) {
                        sb.append("- Dernière connexion: ").append(userDataMap.get("lastLogin")).append("\n");
                    }

                    sb.append("\n");
                }

                // Process financial information
                if (userDataMap.containsKey("currentBalance") || userDataMap.containsKey("nextPaymentDue") ||
                    userDataMap.containsKey("paymentMethod") || userDataMap.containsKey("cardExpiryDate") ||
                    userDataMap.containsKey("invoiceHistory")) {
                    sb.append("Informations financières:\n");

                    if (userDataMap.containsKey("currentBalance")) {
                        sb.append("- Solde actuel: ").append(userDataMap.get("currentBalance")).append("\n");
                    }

                    if (userDataMap.containsKey("nextPaymentDue")) {
                        sb.append("- Prochaine échéance: ").append(userDataMap.get("nextPaymentDue")).append("\n");
                    }

                    if (userDataMap.containsKey("paymentMethod")) {
                        sb.append("- Méthode de paiement: ").append(userDataMap.get("paymentMethod")).append("\n");
                    }

                    if (userDataMap.containsKey("cardExpiryDate")) {
                        sb.append("- Date d'expiration de la carte: ").append(userDataMap.get("cardExpiryDate")).append("\n");
                    }

                    if (userDataMap.containsKey("invoiceHistory")) {
                        sb.append("- Historique des factures:\n");
                        List<String> invoiceHistory = (List<String>) userDataMap.get("invoiceHistory");
                        for (String invoice : invoiceHistory) {
                            sb.append("  * ").append(invoice).append("\n");
                        }
                    }

                    sb.append("\n");
                }

                // Process permissions for admin users
                if (userDataMap.containsKey("permissions")) {
                    sb.append("Permissions:\n");
                    List<String> permissions = (List<String>) userDataMap.get("permissions");
                    for (String permission : permissions) {
                        sb.append("- ").append(permission).append("\n");
                    }

                    sb.append("\n");
                }

                // Process any remaining fields
                sb.append("Autres informations:\n");
                for (Map.Entry<String, Object> entry : userDataMap.entrySet()) {
                    String key = entry.getKey();

                    // Skip fields we've already processed
                    if (contains(priorityFields, key) ||
                        key.equals("subscriptionStatus") ||
                        key.equals("subscriptionExpiry") ||
                        key.equals("subscriptionStartDate") ||
                        key.equals("subscriptionType") ||
                        key.equals("subscriptionPrice") ||
                        key.equals("lastOrder") ||
                        key.equals("orderHistory") ||
                        key.equals("totalOrders") ||
                        key.equals("averageOrderValue") ||
                        key.equals("accountCreationDate") ||
                        key.equals("accountStatus") ||
                        key.equals("accountType") ||
                        key.equals("lastLogin") ||
                        key.equals("currentBalance") ||
                        key.equals("nextPaymentDue") ||
                        key.equals("paymentMethod") ||
                        key.equals("cardExpiryDate") ||
                        key.equals("invoiceHistory") ||
                        key.equals("permissions")) {
                        continue;
                    }

                    Object value = entry.getValue();
                    if (!(value instanceof List)) {
                        sb.append("- ").append(formatFieldName(key)).append(": ")
                          .append(value).append("\n");
                    }
                }

                return sb.toString();
            } else {
                // If it's not a map, just return the string representation
                return userData.toString();
            }
        } catch (Exception e) {
            return "Erreur lors du formatage des données utilisateur: " + e.getMessage();
        }
    }

    /**
     * Formats a field name for display.
     *
     * @param fieldName The field name to format
     * @return The formatted field name
     */
    private String formatFieldName(String fieldName) {
        // Convert camelCase to Title Case with spaces
        String result = fieldName.replaceAll("([a-z])([A-Z])", "$1 $2");
        // Capitalize first letter
        return Character.toUpperCase(result.charAt(0)) + result.substring(1);
    }

    /**
     * Checks if an array contains a value.
     *
     * @param array The array to check
     * @param value The value to look for
     * @return True if the array contains the value, false otherwise
     */
    private boolean contains(String[] array, String value) {
        for (String item : array) {
            if (item.equals(value)) {
                return true;
            }
        }
        return false;
    }

    // ========== SPRING AI TOOL FUNCTIONS ==========

    /**
     * Tool function to get real user profile data
     */
    @Description("Get the real user profile information from the database")
    public Function<UserProfileRequest, String> getUserProfileTool() {
        return request -> {
            try {
                // Create WebClient for calling the real data API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first
                String token = chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (token == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Call the real data API
                Map<String, Object> profileData = webClient.get()
                        .uri("/real-data/profile")
                        .header("Authorization", "Bearer " + token)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (profileData == null) {
                    return "Aucune donnée de profil trouvée pour l'utilisateur: " + request.username();
                }

                return formatUserProfileData(profileData);

            } catch (Exception e) {
                System.out.println("Error in getUserProfileTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération du profil utilisateur: " + e.getMessage();
            }
        };
    }

    /**
     * Tool function to get real user invoices
     */
    @Description("Get the real user invoice history from the database")
    public Function<UserInvoicesRequest, String> getUserInvoicesTool() {
        return request -> {
            try {
                // Create WebClient for calling the real data API
                WebClient webClient = WebClient.builder()
                        .baseUrl("http://localhost:8080")
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .build();

                // Authenticate first
                String token = chatMcpApiService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (token == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Call the real data API
                List<Map<String, Object>> invoices = webClient.get()
                        .uri("/real-data/invoices?limit=" + request.limit())
                        .header("Authorization", "Bearer " + token)
                        .retrieve()
                        .bodyToMono(List.class)
                        .subscribeOn(Schedulers.boundedElastic())
                        .block();

                if (invoices == null || invoices.isEmpty()) {
                    return "Aucune facture trouvée pour l'utilisateur: " + request.username();
                }

                return formatInvoiceData(invoices);

            } catch (Exception e) {
                System.out.println("Error in getUserInvoicesTool: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des factures: " + e.getMessage();
            }
        };
    }

    // Helper methods for formatting data
    private String formatUserProfileData(Map<String, Object> profileData) {
        StringBuilder formatted = new StringBuilder();

        if (profileData.containsKey("fullName")) {
            formatted.append("Nom complet: ").append(profileData.get("fullName")).append("\n");
        }
        if (profileData.containsKey("email")) {
            formatted.append("Email: ").append(profileData.get("email")).append("\n");
        }
        if (profileData.containsKey("currentBalance")) {
            formatted.append("Solde actuel: ").append(profileData.get("currentBalance")).append("€\n");
        }
        if (profileData.containsKey("company")) {
            formatted.append("Entreprise: ").append(profileData.get("company")).append("\n");
        }
        if (profileData.containsKey("jobTitle")) {
            formatted.append("Poste: ").append(profileData.get("jobTitle")).append("\n");
        }
        if (profileData.containsKey("address")) {
            formatted.append("Adresse: ").append(profileData.get("address")).append("\n");
        }
        if (profileData.containsKey("subscriptionStatus")) {
            formatted.append("Statut d'abonnement: ").append(profileData.get("subscriptionStatus")).append("\n");
        }

        return formatted.toString();
    }

    private String formatInvoiceData(List<Map<String, Object>> invoices) {
        StringBuilder formatted = new StringBuilder("Historique des factures:\n");

        for (Map<String, Object> invoice : invoices) {
            formatted.append("- ");
            if (invoice.containsKey("invoiceNumber")) {
                formatted.append("Facture ").append(invoice.get("invoiceNumber"));
            }
            if (invoice.containsKey("totalAmount")) {
                formatted.append(" - ").append(invoice.get("totalAmount")).append("€");
            }
            if (invoice.containsKey("invoiceDate")) {
                formatted.append(" - ").append(invoice.get("invoiceDate"));
            }
            if (invoice.containsKey("status")) {
                formatted.append(" - ").append(invoice.get("status"));
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    /**
     * Formats invoice list for display
     */
    @SuppressWarnings("unchecked")
    private String formatInvoiceList(List<Map<String, Object>> invoices) {
        if (invoices == null || invoices.isEmpty()) {
            return "Aucune facture trouvée.";
        }

        StringBuilder formatted = new StringBuilder("Vos factures:\n\n");

        for (Map<String, Object> invoice : invoices) {
            formatted.append("📄 Facture ");
            if (invoice.containsKey("invoiceNumber")) {
                formatted.append(invoice.get("invoiceNumber"));
            }
            formatted.append("\n");

            if (invoice.containsKey("invoiceDate")) {
                formatted.append("   Date: ").append(invoice.get("invoiceDate")).append("\n");
            }
            if (invoice.containsKey("totalAmount")) {
                formatted.append("   Montant: ").append(invoice.get("totalAmount")).append("€\n");
            }
            if (invoice.containsKey("status")) {
                formatted.append("   Statut: ").append(invoice.get("status")).append("\n");
            }
            if (invoice.containsKey("paymentStatus")) {
                formatted.append("   Paiement: ").append(invoice.get("paymentStatus")).append("\n");
            }
            if (invoice.containsKey("dueDate")) {
                formatted.append("   Échéance: ").append(invoice.get("dueDate")).append("\n");
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    /**
     * Formats transaction list for display
     */
    @SuppressWarnings("unchecked")
    private String formatTransactionList(List<Map<String, Object>> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            return "Aucune transaction trouvée.";
        }

        StringBuilder formatted = new StringBuilder("Vos transactions récentes:\n\n");

        for (Map<String, Object> transaction : transactions) {
            formatted.append("💳 Transaction ");
            if (transaction.containsKey("transactionId")) {
                formatted.append(transaction.get("transactionId"));
            }
            formatted.append("\n");

            if (transaction.containsKey("createdAt")) {
                formatted.append("   Date: ").append(transaction.get("createdAt")).append("\n");
            }
            if (transaction.containsKey("amount")) {
                formatted.append("   Montant: ").append(transaction.get("amount")).append("€\n");
            }
            if (transaction.containsKey("transactionType")) {
                formatted.append("   Type: ").append(transaction.get("transactionType")).append("\n");
            }
            if (transaction.containsKey("description")) {
                formatted.append("   Description: ").append(transaction.get("description")).append("\n");
            }
            if (transaction.containsKey("merchantName")) {
                formatted.append("   Marchand: ").append(transaction.get("merchantName")).append("\n");
            }
            if (transaction.containsKey("status")) {
                formatted.append("   Statut: ").append(transaction.get("status")).append("\n");
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    /**
     * Formats order list for display
     */
    @SuppressWarnings("unchecked")
    private String formatOrderList(List<Map<String, Object>> orders) {
        if (orders == null || orders.isEmpty()) {
            return "Aucune commande trouvée.";
        }

        StringBuilder formatted = new StringBuilder("Vos commandes:\n\n");

        for (Map<String, Object> order : orders) {
            formatted.append("📦 Commande ");
            if (order.containsKey("orderNumber")) {
                formatted.append(order.get("orderNumber"));
            }
            formatted.append("\n");

            if (order.containsKey("orderDate")) {
                formatted.append("   Date: ").append(order.get("orderDate")).append("\n");
            }
            if (order.containsKey("totalAmount")) {
                formatted.append("   Montant: ").append(order.get("totalAmount")).append("€\n");
            }
            if (order.containsKey("status")) {
                formatted.append("   Statut: ").append(order.get("status")).append("\n");
            }
            if (order.containsKey("itemsDescription")) {
                formatted.append("   Articles: ").append(order.get("itemsDescription")).append("\n");
            }
            if (order.containsKey("trackingNumber")) {
                formatted.append("   Suivi: ").append(order.get("trackingNumber")).append("\n");
            }
            formatted.append("\n");
        }

        return formatted.toString();
    }

    // Request record classes for tool functions
    public record UserProfileRequest(String username) {}
    public record UserInvoicesRequest(String username, int limit) {}
}
