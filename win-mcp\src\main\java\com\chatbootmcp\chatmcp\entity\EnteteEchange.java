package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * EnteteEchange entity representing exchange headers between pharmacies
 */
@Entity
@Table(name = "entete_echange")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnteteEchange {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "num_echange", length = 50)
    private String numEchange;

    @Column(name = "date_echange")
    private LocalDateTime dateEchange;

    @Column(name = "nom_pharmacien")
    private String nomPharmacien;

    @Column(name = "raison_sociale")
    private String raisonSociale;

    @Column(name = "sens_echange", length = 50)
    private String sensEchange;

    @Column(name = "type_prix", length = 50)
    private String typePrix;

    @Column(name = "taux_remise", precision = 5, scale = 2)
    private BigDecimal tauxRemise;

    @Column(name = "total_qt_echange", precision = 15, scale = 2)
    private BigDecimal totalQtEchange;

    @Column(name = "total_qt_ug", precision = 15, scale = 2)
    private BigDecimal totalQtUg;

    @Column(name = "total_mnt_echange_ht", precision = 15, scale = 2)
    private BigDecimal totalMntEchangeHt;

    @Column(name = "total_mnt_echange_ttc", precision = 15, scale = 2)
    private BigDecimal totalMntEchangeTtc;

    @Column(name = "total_mnt_remise_ht", precision = 15, scale = 2)
    private BigDecimal totalMntRemiseHt;

    @Column(name = "total_mnt_remise_ttc", precision = 15, scale = 2)
    private BigDecimal totalMntRemiseTtc;

    @Column(name = "total_prix_achat_std", precision = 15, scale = 2)
    private BigDecimal totalPrixAchatStd;

    @Column(name = "total_prix_vente_std", precision = 15, scale = 2)
    private BigDecimal totalPrixVenteStd;

    @Column(name = "reste_solde", precision = 15, scale = 2)
    private BigDecimal resteSolde;

    @Column(name = "statut", length = 50)
    private String statut;

    @Column(name = "sous_statut", length = 50)
    private String sousStatut;

    @Column(name = "user_modifiable")
    private Boolean userModifiable;

    @Column(name = "audited")
    private Boolean audited;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "confrere_id")
    private Confrere confrere;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "operateur_id")
    private Operateur operateur;
}
