#!/usr/bin/env python3
"""
Test script for Win-MCP APIs
"""

import requests
import json

def test_winmcp_apis():
    base_url = "http://localhost:8082"

    print("🧪 Testing Win-MCP APIs...")
    print(f"Base URL: {base_url}")

    # First authenticate to get token
    print("\n🔐 Authenticating...")
    try:
        auth_data = {
            "username": "user1",
            "password": "password"
        }
        auth_response = requests.post(f"{base_url}/auth/login", json=auth_data)
        print(f"Auth Status Code: {auth_response.status_code}")

        if auth_response.status_code == 200:
            token = auth_response.json()['token']
            print("✅ Authentication successful")
            headers = {"Authorization": f"Bearer {token}"}
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            headers = {}
    except Exception as e:
        print(f"❌ Auth Error: {e}")
        headers = {}

    # Test 1: Get user1 client data
    print("\n1️⃣ Testing user1 client data...")
    try:
        response = requests.get(f"{base_url}/api/winplus/clients/code/user1", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            client_data = response.json()
            print(f"✅ Client found: {client_data['nom']} {client_data['prenom']}")
            print(f"   Email: {client_data['email']}")
            print(f"   Balance: {client_data['soldeClient']} EUR")
            print(f"   Credit Limit: {client_data['plafondCredit']} EUR")
            print(f"   CA: {client_data['caClient']} EUR")
        else:
            print(f"❌ Failed to get client data: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 2: Get user1 sales data
    print("\n2️⃣ Testing user1 sales data...")
    try:
        # First get client ID
        client_response = requests.get(f"{base_url}/api/winplus/clients/code/user1", headers=headers)
        if client_response.status_code == 200:
            client_id = client_response.json()['id']

            # Get sales for this client
            sales_response = requests.get(f"{base_url}/api/winplus/ventes/client/{client_id}", headers=headers)
            print(f"Status Code: {sales_response.status_code}")
            if sales_response.status_code == 200:
                sales_data = sales_response.json()
                print(f"✅ Found {sales_data['totalItems']} sales for user1")
                for sale in sales_data['ventes'][:3]:  # Show first 3 sales
                    print(f"   Sale #{sale['numVente']}: {sale['mntNetTtc']} EUR on {sale['dateVente'][:10]}")
            else:
                print(f"❌ Failed to get sales data: {sales_response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 3: Get comprehensive user data
    print("\n3️⃣ Testing comprehensive user data...")
    try:
        response = requests.get(f"{base_url}/api/winplus/user-data/user1", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ User data retrieved successfully")
            print(f"   User: {user_data['user']['fullName']}")
            if 'client' in user_data:
                print(f"   Client: {user_data['client']['nom']} {user_data['client']['prenom']}")
            if 'clientStatistics' in user_data:
                stats = user_data['clientStatistics']
                print(f"   Total Sales: {stats['totalSales']}")
                print(f"   Total Amount: {stats['totalAmount']} EUR")
                print(f"   Current Balance: {stats['currentBalance']} EUR")
        else:
            print(f"❌ Failed to get user data: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 4: Get products
    print("\n4️⃣ Testing products data...")
    try:
        response = requests.get(f"{base_url}/api/winplus/produits?size=5", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            products_data = response.json()
            print(f"✅ Found {products_data['totalItems']} products")
            for product in products_data['produits']:
                print(f"   {product['codePrd']}: {product['designation']} - {product['prixVenteStd']} EUR")
        else:
            print(f"❌ Failed to get products: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 5: Get sales statistics
    print("\n5️⃣ Testing sales statistics...")
    try:
        response = requests.get(f"{base_url}/api/winplus/ventes/statistics", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Sales statistics retrieved")
            print(f"   Total Sales: {stats['totalSales']} EUR")
            print(f"   Number of Sales: {stats['numberOfSales']}")
            print(f"   Average Sale: {stats['averageSale']} EUR")
        else:
            print(f"❌ Failed to get statistics: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_winmcp_apis()
