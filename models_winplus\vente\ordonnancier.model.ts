// import { Moment } from 'moment';

import { Medecin } from "../common/medecin.model";
import { CategorieProduit } from "../produit/base/categorieProduit.model";
import { FamilleTarifaire } from "../produit/base/familleTarifaire.model";
import { FormeProduit } from "../produit/base/formeProduit.model";
import { Produit } from "../produit/base/produit.model";
import { Beneficiaire } from "../tiers/client/beneficiaire.model";
import { Client } from "../tiers/client/client.model";
import { Fournisseur } from "../tiers/fournisseur/fournisseur.model";
import { DetailVente } from "./detailVente.model";
import { EnteteVente } from "./enteteVente.model";



export class Ordonnancier {
    audited?: boolean;
    id?: number;
    client?: Client;
    codeCtgr?: string
    codeFrm?: string
    codeFt?: string
    codeLabo?: string
    codePrd?: string
    ctgr?: CategorieProduit
    dateVente?: string
    detailVente?: DetailVente
    dsgnPrd?: string
    dureeTraitementJ?: number
    etat?: string
    frm?: FormeProduit
    ft?: FamilleTarifaire
    labo?: Fournisseur
    medcin?: Medecin
    nomClient?: string
    nomMedecin?: string
    nomPatient?: string
    numCin?: string
    numOrdonnancier?: number
    patient?: Beneficiaire
    pbrH?: number
    pbrP?: number
    prixAchatStd?: number
    prixFabHt?: number
    prixHosp?: number
    prixVenteStd?: number
    produit?: Produit
    qte?: number
    tauxMarge?: number
    tauxRembPrd?: number
    tauxTva?: number
    tenantId?: number
    vente?: EnteteVente
    // TODO?: Ordonnancier model
    userModifiable?: boolean;
}
