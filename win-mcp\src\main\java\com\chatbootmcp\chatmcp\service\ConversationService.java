package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.dto.request.ConversationRequest;
import com.chatbootmcp.chatmcp.dto.response.ConversationResponse;
import com.chatbootmcp.chatmcp.entity.Conversation;
import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.exception.ResourceNotFoundException;
import com.chatbootmcp.chatmcp.repository.ConversationRepository;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConversationService {

    @Autowired
    private ConversationRepository conversationRepository;

    @Autowired
    private UserRepository userRepository;

    public List<ConversationResponse> getUserConversations(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        return conversationRepository.findByUserOrderByUpdatedAtDesc(user).stream()
                .map(ConversationResponse::new)
                .collect(Collectors.toList());
    }

    public ConversationResponse createConversation(String username, ConversationRequest request) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Conversation conversation = new Conversation();
        conversation.setTitle(request.getTitle() != null ? request.getTitle() : "New Conversation");
        conversation.setUser(user);
        conversation.setCreatedAt(LocalDateTime.now());
        conversation.setUpdatedAt(LocalDateTime.now());
        
        conversationRepository.save(conversation);
        
        return new ConversationResponse(conversation);
    }

    public ConversationResponse getConversation(Long id, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Conversation conversation = conversationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Conversation not found"));
        
        if (!conversation.getUser().getId().equals(user.getId())) {
            throw new ResourceNotFoundException("Conversation not found for this user");
        }
        
        return new ConversationResponse(conversation);
    }

    public void updateConversationTimestamp(Long conversationId) {
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new ResourceNotFoundException("Conversation not found"));
        
        conversation.setUpdatedAt(LocalDateTime.now());
        conversationRepository.save(conversation);
    }
}
