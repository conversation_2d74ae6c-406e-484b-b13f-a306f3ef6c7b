
import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Taxe } from 'src/app/winpharm/models/common/taxe.model';
import { Rayon } from '../../produit/base/rayon.model';


export class StatistiqueBlAchatPartieProduit {
    categorie?: CategorieProduit;
    codeProduit?: string;
    datePeremption?: string;
    designationProduit?: string;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    numeroLot?: string;
    pph?: number;
    ppv?: number;
    stock?: number;
    tva?: Taxe;
    rayon?: Rayon;
}
