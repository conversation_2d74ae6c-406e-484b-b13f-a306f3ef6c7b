C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\response\UserDataResponse.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\DataInitializationService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\MessageService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\UserDataRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\exception\UnauthorizedException.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\controller\MessageController.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\controller\UserDataController.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\exception\GlobalExceptionHandler.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\Conversation.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\UserProfileRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\response\AuthResponse.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\config\SecurityConfig.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\UserProfile.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\Message.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\controller\AuthController.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\UserRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\User.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\UserData.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\EnhancedUserDataService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\Order.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\ConversationRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\security\JwtAuthenticationFilter.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\response\MessageResponse.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\AuthService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\AIService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\response\ConversationResponse.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\UserDetailsServiceImpl.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\config\WebSocketConfig.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\controller\MockDataController.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\OrderRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\request\MessageRequest.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\exception\ResourceNotFoundException.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\ConversationService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\MessageRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\ChatMcpApplication.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\config\CorsConfig.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\util\JwtUtil.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\config\DataInitializer.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\request\ConversationRequest.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\request\LoginRequest.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\InvoiceRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\util\MockDataGenerator.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\dto\request\TestLoginRequest.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\repository\TransactionRepository.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\Invoice.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\service\UserDataService.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\controller\ConversationController.java
C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\Agent_ui\Agentic_ai_chatboot-mcp\chat-mcp\src\main\java\com\chatbootmcp\chatmcp\entity\Transaction.java
