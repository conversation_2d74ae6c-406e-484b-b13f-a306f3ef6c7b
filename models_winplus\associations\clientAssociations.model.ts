// Define types for the match object
interface MatchAssociation {
    code_client_groupe: string;
    raison_sociale: string;
    nom_pharmacien: string;
    adresse1: string;
    adresse2: string | null;
    ice: string;
    localite: string;
    nom_pharmacien_normalized?: string;
    raison_sociale_normalized?: string;
    patente: string;
    tag: string | null;
    telephone: string;
    ville: string;
    type: string | null;
}

// Define types for the client object
export interface ClientAssociation {
    code_site?: number;
    code_client?: string;
    raison_sociale: string;
    nom_pharmacien: string;
    ville: string;
}

type AssociationType = "association_classique" | "association_floue";


// Define types for the association object
export interface AssociationClient {
    client: ClientAssociation;
    type: AssociationType;
    matches: MatchAssociation[];
}

// Define types for the root object containing associations
export interface ClientAssociationsResponse {
    Associations: AssociationClient[];
}




export interface ClientAssociationsRepresentation {
    client: ClientAssociation;
    type: AssociationType | null;
    matches: MatchAssociation[];
    selectedAssociation?: Partial<MatchAssociation>;
    validated: boolean;
    idHash?: string | number;
    associateOrigin?: "FILE" | "API" | "INDEXED_DB";
    shouldCreate?: boolean;
}

export interface ClientAssociationsRepresentationIndexedDB{
    associations: ClientAssociationsRepresentation[];
    origin: string;
}