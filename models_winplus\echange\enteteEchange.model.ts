import { TypePrixEchange } from 'src/app/winpharm/enums/echange/TypePrixEchange.enum';
import { SousStatutEchange } from 'src/app/winpharm/enums/echange/SousStatutEchange.enum';
import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';
import { SensEchange } from 'src/app/winpharm/enums/echange/SensEchange.enum';

// import { Moment } from 'moment';

import { Confrere } from 'src/app/winpharm/models/tiers/confrere/confrere.model';
import { DetailEchangeFt } from './detailEchangeFt.model';
import { DetailEchangeProduit } from './detailEchangeProduit.model';
import { Operateur } from '../common/operateur.model';


export class EnteteEchange {
    audited?: boolean;
    confrere?: Confrere;
    dateEchange?: any;
    detailEchangeFts?: DetailEchangeFt[];
    detailEchangeProduits?: DetailEchangeProduit[];
    id?: number;
    nomPharmacien?: string;
    numEchange?: string;
    raisonSociale?: string;
    resteSolde?: number;
    sensEchange?: SensEchange;
    sousStatut?: SousStatutEchange;
    statut?: Statut;
    tauxRemise?: number;
    totalMntEchangeHt?: number;
    totalMntEchangeTtc?: number;
    totalMntRemiseHt?: number;
    totalMntRemiseTtc?: number;
    totalPrixAchatStd?: number;
    totalPrixVenteStd?: number;
    totalQtEchange?: number;
    totalQtUg?: number;
    typePrix?: TypePrixEchange;
    userModifiable?: boolean;


    /// 
    operateur?: Operateur
}

