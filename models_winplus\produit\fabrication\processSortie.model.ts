
import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Produit } from 'src/app/winpharm/models/produit/base/produit.model';


export class ProcessSortie { 
    audited?: boolean;
    codeCtgr?: string;
    codeFrm?: string;
    codeFt?: string;
    codeLabo?: string;
    codePrd?: string;
    ctgr?: CategorieProduit;
    datePeremption?: string;
    dsgnPrd?: string;
    frm?: FormeProduit;
    ft?: FamilleTarifaire;
    id?: number;
    labo?: Fournisseur;
    numLot?: string;
    pbrH?: number;
    pbrP?: number;
    prixAchatStd?: number;
    prixAchatTtc?: number;
    prixFabHt?: number;
    prixHosp?: number;
    prixVenteStd?: number;
    prixVenteTtc?: number;
    produit?: Produit;
    qte?: number;
    tauxMarge?: number;
    tauxTva?: number;
    userModifiable?: boolean;
}
