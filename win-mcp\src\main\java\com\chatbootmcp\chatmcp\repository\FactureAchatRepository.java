package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.FactureAchat;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface FactureAchatRepository extends JpaRepository<FactureAchat, Long> {
    
    List<FactureAchat> findByFournisseurId(Long fournisseurId);
    
    Page<FactureAchat> findByFournisseurId(Long fournisseurId, Pageable pageable);
    
    @Query("SELECT f FROM FactureAchat f WHERE f.dateFacture BETWEEN :startDate AND :endDate")
    List<FactureAchat> findFacturesByDateRange(@Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT f FROM FactureAchat f WHERE f.estReglee = false")
    List<FactureAchat> findUnpaidInvoices();
    
    @Query("SELECT f FROM FactureAchat f WHERE f.estReglee = false")
    Page<FactureAchat> findUnpaidInvoices(Pageable pageable);
    
    @Query("SELECT f FROM FactureAchat f WHERE f.dateEcheance < :currentDate AND f.estReglee = false")
    List<FactureAchat> findOverdueInvoices(@Param("currentDate") LocalDateTime currentDate);
    
    @Query("SELECT SUM(f.montantTtc) FROM FactureAchat f WHERE f.dateFacture BETWEEN :startDate AND :endDate")
    java.math.BigDecimal calculateTotalPurchasesByDateRange(@Param("startDate") LocalDateTime startDate,
                                                           @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT SUM(f.resteAPayer) FROM FactureAchat f WHERE f.estReglee = false")
    java.math.BigDecimal calculateTotalOutstandingAmount();
}
