import { Alphabet } from 'src/app/winpharm/enums/inventaire/Alphabet.enum';

import { CategorieProduit } from 'src/app/winpharm/models/produit/base/categorieProduit.model';
import { Depot } from 'src/app/winpharm/models/produit/stock/depot.model';
import { FamilleTarifaire } from 'src/app/winpharm/models/produit/base/familleTarifaire.model';
import { FormeProduit } from 'src/app/winpharm/models/produit/base/formeProduit.model';
import { Fournisseur } from 'src/app/winpharm/models/tiers/fournisseur/fournisseur.model';
import { Rayon } from 'src/app/winpharm/models/produit/base/rayon.model';
import { ProduitSearchTypeEnum } from '../../../enums/vente/ProdutSearchType.enum';
import { Dci } from '../medical/dci.model';
import { Statut } from 'src/app/winpharm/enums/common/Statut.enum';


export class ProduitCriteria {
    categorie?: CategorieProduit;
    listCodesPrd?: string[];
    listCodesGroupe?: string[];
    codePrd?: string;
    codebarre?: string;
    conditionnementOnly?: boolean;
    depot?: Depot;
    dsgnPrd?: string;
    estActif?: boolean;
    estVendable?: boolean;
    forme?: FormeProduit;
    ft?: FamilleTarifaire;
    labo?: Fournisseur;
    nouveauPrd?: boolean;
    plageAlphaDeb?: Alphabet;
    plageAlphaFin?: Alphabet;
    rayon?: Rayon;
    searchType?: ProduitSearchTypeEnum;
    stockMandatory?: boolean;

    dci?: Dci;
    dciLibelle?: String;

    ///

    nomCommercial?: string
    prix1?: number
    prix2?: number
    myFrom?: string
    // libelleWithPrixVente?: boolean;



    listPrdIds?: number[];
    firtExpiredFirstOut?: boolean;


    signStockExpression?: string

    produitInventaireStatut?: ProduitInventaireStatutEnum

    // statut?: Statut;      TODO: HTO   remove this


    // TODO: HTO   use this
    dateCreationDebut?: any;
    dateCreationFin?: any;
    listeStatuts?: Statut[] = [];

    listCategories?: CategorieProduit[]

    onlyFavoris?: boolean


}


export enum ProduitInventaireStatutEnum {
    NON_INVENTORIE = "N",
    INVENTORIE = "I"
}

