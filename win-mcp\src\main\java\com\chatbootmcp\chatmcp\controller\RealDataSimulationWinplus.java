package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.entity.*;
import com.chatbootmcp.chatmcp.repository.*;
import com.chatbootmcp.chatmcp.entity.PersonalMedicalProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Controller for WinPlus Real Data Simulation APIs
 * Provides comprehensive APIs for user data, sales, purchases, products, and clients
 */
@RestController
@RequestMapping("/api/winplus")
@CrossOrigin(origins = "*")
public class RealDataSimulationWinplus {

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProduitRepository produitRepository;

    @Autowired
    private EnteteVenteRepository enteteVenteRepository;

    @Autowired
    private FactureAchatRepository factureAchatRepository;

    @Autowired
    private PersonalMedicalProfileRepository personalMedicalProfileRepository;

    @Autowired
    private UserRepository userRepository;

    // ==================== CLIENT APIs ====================

    @GetMapping("/clients")
    public ResponseEntity<Map<String, Object>> getAllClients(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("nom").ascending());
        Page<Client> clientPage;

        if (search != null && !search.trim().isEmpty()) {
            clientPage = clientRepository.findActiveClientsBySearchTerm(search, pageable);
        } else {
            clientPage = clientRepository.findByEstActifTrue(pageable);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("clients", clientPage.getContent());
        response.put("currentPage", clientPage.getNumber());
        response.put("totalItems", clientPage.getTotalElements());
        response.put("totalPages", clientPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/clients/{id}")
    public ResponseEntity<Client> getClientById(@PathVariable Long id) {
        Optional<Client> client = clientRepository.findById(id);
        return client.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/clients/code/{codeClient}")
    public ResponseEntity<Client> getClientByCode(@PathVariable String codeClient) {
        Optional<Client> client = clientRepository.findByCodeClient(codeClient);
        return client.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/clients/balance/positive")
    public ResponseEntity<List<Client>> getClientsWithPositiveBalance() {
        List<Client> clients = clientRepository.findClientsWithPositiveBalance();
        return ResponseEntity.ok(clients);
    }

    @GetMapping("/clients/balance/negative")
    public ResponseEntity<List<Client>> getClientsWithNegativeBalance() {
        List<Client> clients = clientRepository.findClientsWithNegativeBalance();
        return ResponseEntity.ok(clients);
    }

    @GetMapping("/clients/top-ca")
    public ResponseEntity<List<Client>> getTopClientsByCA(
            @RequestParam(defaultValue = "1000") String minCa) {
        java.math.BigDecimal minCaValue = new java.math.BigDecimal(minCa);
        List<Client> clients = clientRepository.findTopClientsByCA(minCaValue);
        return ResponseEntity.ok(clients);
    }

    // ==================== PRODUCT APIs ====================

    @GetMapping("/produits")
    public ResponseEntity<Map<String, Object>> getAllProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("designation").ascending());
        Page<Produit> produitPage;

        if (search != null && !search.trim().isEmpty()) {
            produitPage = produitRepository.findVendableProductsBySearchTerm(search, pageable);
        } else {
            produitPage = produitRepository.findByEstVendableTrue(pageable);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("produits", produitPage.getContent());
        response.put("currentPage", produitPage.getNumber());
        response.put("totalItems", produitPage.getTotalElements());
        response.put("totalPages", produitPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/produits/{id}")
    public ResponseEntity<Produit> getProductById(@PathVariable Long id) {
        Optional<Produit> produit = produitRepository.findById(id);
        return produit.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/produits/code/{codePrd}")
    public ResponseEntity<Produit> getProductByCode(@PathVariable String codePrd) {
        Optional<Produit> produit = produitRepository.findByCodePrd(codePrd);
        return produit.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/produits/barcode/{codeBarre}")
    public ResponseEntity<Produit> getProductByBarcode(@PathVariable String codeBarre) {
        Optional<Produit> produit = produitRepository.findByCodeBarre(codeBarre);
        return produit.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/produits/low-stock")
    public ResponseEntity<List<Produit>> getProductsWithLowStock(
            @RequestParam(defaultValue = "10") String seuil) {
        java.math.BigDecimal seuilValue = new java.math.BigDecimal(seuil);
        List<Produit> produits = produitRepository.findProductsWithLowStock(seuilValue);
        return ResponseEntity.ok(produits);
    }

    @GetMapping("/produits/psychotropic")
    public ResponseEntity<List<Produit>> getPsychotropicProducts() {
        List<Produit> produits = produitRepository.findPsychotropicProducts();
        return ResponseEntity.ok(produits);
    }

    @GetMapping("/produits/prescription-required")
    public ResponseEntity<List<Produit>> getPrescriptionRequiredProducts() {
        List<Produit> produits = produitRepository.findPrescriptionRequiredProducts();
        return ResponseEntity.ok(produits);
    }

    // ==================== SALES APIs ====================

    @GetMapping("/ventes")
    public ResponseEntity<Map<String, Object>> getAllSales(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateVente").descending());
        Page<EnteteVente> ventePage;

        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
            LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
            ventePage = enteteVenteRepository.findVentesByDateRange(start, end, pageable);
        } else {
            ventePage = enteteVenteRepository.findRecentSales(pageable);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("ventes", ventePage.getContent());
        response.put("currentPage", ventePage.getNumber());
        response.put("totalItems", ventePage.getTotalElements());
        response.put("totalPages", ventePage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/ventes/{id}")
    public ResponseEntity<EnteteVente> getSaleById(@PathVariable Long id) {
        Optional<EnteteVente> vente = enteteVenteRepository.findById(id);
        return vente.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/ventes/client/{clientId}")
    public ResponseEntity<Map<String, Object>> getSalesByClient(
            @PathVariable Long clientId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateVente").descending());
        Page<EnteteVente> ventePage = enteteVenteRepository.findByClientId(clientId, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("ventes", ventePage.getContent());
        response.put("currentPage", ventePage.getNumber());
        response.put("totalItems", ventePage.getTotalElements());
        response.put("totalPages", ventePage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/ventes/high-value")
    public ResponseEntity<List<EnteteVente>> getHighValueSales(
            @RequestParam(defaultValue = "1000") String minAmount) {
        java.math.BigDecimal minAmountValue = new java.math.BigDecimal(minAmount);
        List<EnteteVente> ventes = enteteVenteRepository.findHighValueSales(minAmountValue);
        return ResponseEntity.ok(ventes);
    }

    @GetMapping("/ventes/statistics")
    public ResponseEntity<Map<String, Object>> getSalesStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        LocalDateTime start = startDate != null ?
            LocalDateTime.parse(startDate + "T00:00:00") :
            LocalDateTime.now().minusDays(30);
        LocalDateTime end = endDate != null ?
            LocalDateTime.parse(endDate + "T23:59:59") :
            LocalDateTime.now();

        java.math.BigDecimal totalSales = enteteVenteRepository.calculateTotalSalesByDateRange(start, end);
        List<EnteteVente> salesInPeriod = enteteVenteRepository.findVentesByDateRange(start, end);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalSales", totalSales != null ? totalSales : java.math.BigDecimal.ZERO);
        statistics.put("numberOfSales", salesInPeriod.size());
        statistics.put("averageSale", salesInPeriod.size() > 0 ?
            totalSales.divide(new java.math.BigDecimal(salesInPeriod.size()), 2, java.math.RoundingMode.HALF_UP) :
            java.math.BigDecimal.ZERO);
        statistics.put("period", Map.of("start", start.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                       "end", end.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)));

        return ResponseEntity.ok(statistics);
    }

    // ==================== PURCHASE APIs ====================

    @GetMapping("/achats")
    public ResponseEntity<Map<String, Object>> getAllPurchases(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateFacture").descending());
        Page<FactureAchat> achatPage;

        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
            LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
            List<FactureAchat> achats = factureAchatRepository.findFacturesByDateRange(start, end);
            // Convert to Page manually for consistency
            int startIndex = (int) pageable.getOffset();
            int endIndex = Math.min(startIndex + pageable.getPageSize(), achats.size());
            List<FactureAchat> pageContent = achats.subList(startIndex, endIndex);
            achatPage = new org.springframework.data.domain.PageImpl<>(pageContent, pageable, achats.size());
        } else {
            achatPage = factureAchatRepository.findAll(pageable);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("achats", achatPage.getContent());
        response.put("currentPage", achatPage.getNumber());
        response.put("totalItems", achatPage.getTotalElements());
        response.put("totalPages", achatPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/achats/{id}")
    public ResponseEntity<FactureAchat> getPurchaseById(@PathVariable Long id) {
        Optional<FactureAchat> achat = factureAchatRepository.findById(id);
        return achat.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/achats/fournisseur/{fournisseurId}")
    public ResponseEntity<Map<String, Object>> getPurchasesBySupplier(
            @PathVariable Long fournisseurId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateFacture").descending());
        Page<FactureAchat> achatPage = factureAchatRepository.findByFournisseurId(fournisseurId, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("achats", achatPage.getContent());
        response.put("currentPage", achatPage.getNumber());
        response.put("totalItems", achatPage.getTotalElements());
        response.put("totalPages", achatPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/achats/unpaid")
    public ResponseEntity<Map<String, Object>> getUnpaidPurchases(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateEcheance").ascending());
        Page<FactureAchat> achatPage = factureAchatRepository.findUnpaidInvoices(pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("achats", achatPage.getContent());
        response.put("currentPage", achatPage.getNumber());
        response.put("totalItems", achatPage.getTotalElements());
        response.put("totalPages", achatPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/achats/overdue")
    public ResponseEntity<List<FactureAchat>> getOverduePurchases() {
        List<FactureAchat> achats = factureAchatRepository.findOverdueInvoices(LocalDateTime.now());
        return ResponseEntity.ok(achats);
    }

    @GetMapping("/achats/statistics")
    public ResponseEntity<Map<String, Object>> getPurchaseStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        LocalDateTime start = startDate != null ?
            LocalDateTime.parse(startDate + "T00:00:00") :
            LocalDateTime.now().minusDays(30);
        LocalDateTime end = endDate != null ?
            LocalDateTime.parse(endDate + "T23:59:59") :
            LocalDateTime.now();

        java.math.BigDecimal totalPurchases = factureAchatRepository.calculateTotalPurchasesByDateRange(start, end);
        java.math.BigDecimal totalOutstanding = factureAchatRepository.calculateTotalOutstandingAmount();
        List<FactureAchat> purchasesInPeriod = factureAchatRepository.findFacturesByDateRange(start, end);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPurchases", totalPurchases != null ? totalPurchases : java.math.BigDecimal.ZERO);
        statistics.put("totalOutstanding", totalOutstanding != null ? totalOutstanding : java.math.BigDecimal.ZERO);
        statistics.put("numberOfPurchases", purchasesInPeriod.size());
        statistics.put("averagePurchase", purchasesInPeriod.size() > 0 ?
            totalPurchases.divide(new java.math.BigDecimal(purchasesInPeriod.size()), 2, java.math.RoundingMode.HALF_UP) :
            java.math.BigDecimal.ZERO);
        statistics.put("period", Map.of("start", start.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                       "end", end.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)));

        return ResponseEntity.ok(statistics);
    }

    // ==================== USER DATA APIs ====================

    @GetMapping("/user-data/{username}")
    public ResponseEntity<Map<String, Object>> getUserData(@PathVariable String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);

        if (!userOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }

        User user = userOpt.get();

        // Get user's client data if exists
        Optional<Client> clientOpt = clientRepository.findByCodeClient(username);

        Map<String, Object> userData = new HashMap<>();
        userData.put("user", user);

        if (clientOpt.isPresent()) {
            Client client = clientOpt.get();
            userData.put("client", client);

            // Get recent sales for this client
            List<EnteteVente> recentSales = enteteVenteRepository.findByClientId(client.getId());
            userData.put("recentSales", recentSales.stream().limit(5).toArray());

            // Calculate client statistics
            Map<String, Object> clientStats = new HashMap<>();
            clientStats.put("totalSales", recentSales.size());
            clientStats.put("totalAmount", recentSales.stream()
                .map(EnteteVente::getMntNetTtc)
                .filter(amount -> amount != null)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add));
            clientStats.put("currentBalance", client.getSoldeClient());
            clientStats.put("creditLimit", client.getPlafondCredit());

            userData.put("clientStatistics", clientStats);
        }

        return ResponseEntity.ok(userData);
    }

    @GetMapping("/dashboard/summary")
    public ResponseEntity<Map<String, Object>> getDashboardSummary() {
        Map<String, Object> summary = new HashMap<>();

        // Client statistics
        long totalClients = clientRepository.count();
        List<Client> activeClients = clientRepository.findByEstActifTrue();

        // Product statistics
        long totalProducts = produitRepository.count();
        List<Produit> vendableProducts = produitRepository.findByEstVendableTrue();

        // Sales statistics (last 30 days)
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        LocalDateTime now = LocalDateTime.now();
        java.math.BigDecimal totalSalesLast30Days = enteteVenteRepository.calculateTotalSalesByDateRange(thirtyDaysAgo, now);

        // Purchase statistics (last 30 days)
        java.math.BigDecimal totalPurchasesLast30Days = factureAchatRepository.calculateTotalPurchasesByDateRange(thirtyDaysAgo, now);
        java.math.BigDecimal totalOutstanding = factureAchatRepository.calculateTotalOutstandingAmount();

        summary.put("clients", Map.of(
            "total", totalClients,
            "active", activeClients.size()
        ));

        summary.put("products", Map.of(
            "total", totalProducts,
            "vendable", vendableProducts.size()
        ));

        summary.put("sales", Map.of(
            "last30Days", totalSalesLast30Days != null ? totalSalesLast30Days : java.math.BigDecimal.ZERO
        ));

        summary.put("purchases", Map.of(
            "last30Days", totalPurchasesLast30Days != null ? totalPurchasesLast30Days : java.math.BigDecimal.ZERO,
            "outstanding", totalOutstanding != null ? totalOutstanding : java.math.BigDecimal.ZERO
        ));

        return ResponseEntity.ok(summary);
    }

    /**
     * Get personal medical profile for a user
     */
    @GetMapping("/api/winplus/medical-profile/{username}")
    public ResponseEntity<Map<String, Object>> getMedicalProfile(@PathVariable String username) {
        try {
            Optional<PersonalMedicalProfile> profileOpt = personalMedicalProfileRepository.findByUsername(username);

            Map<String, Object> response = new HashMap<>();
            if (profileOpt.isPresent()) {
                PersonalMedicalProfile profile = profileOpt.get();

                // Create detailed medical profile response
                Map<String, Object> medicalData = new HashMap<>();
                medicalData.put("id", profile.getId());
                medicalData.put("username", profile.getUsername());
                medicalData.put("bloodType", profile.getBloodType());
                medicalData.put("allergies", profile.getAllergies());
                medicalData.put("chronicConditions", profile.getChronicConditions());
                medicalData.put("currentMedications", profile.getCurrentMedications());
                medicalData.put("emergencyContact", profile.getEmergencyContact());
                medicalData.put("emergencyPhone", profile.getEmergencyPhone());
                medicalData.put("insuranceNumber", profile.getInsuranceNumber());
                medicalData.put("doctorName", profile.getDoctorName());
                medicalData.put("doctorPhone", profile.getDoctorPhone());
                medicalData.put("birthDate", profile.getBirthDate());
                medicalData.put("heightCm", profile.getHeightCm());
                medicalData.put("weightKg", profile.getWeightKg());
                medicalData.put("lastCheckupDate", profile.getLastCheckupDate());
                medicalData.put("medicalNotes", profile.getMedicalNotes());

                response.put("medicalProfile", medicalData);
                response.put("status", "found");
                response.put("message", "Medical profile retrieved successfully");

                System.out.println("✅ Medical profile found for user: " + username);
            } else {
                response.put("medicalProfile", null);
                response.put("status", "not_found");
                response.put("message", "No medical profile found for user: " + username);

                System.out.println("❌ No medical profile found for user: " + username);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.out.println("❌ Error retrieving medical profile for " + username + ": " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to fetch medical profile: " + e.getMessage());
            errorResponse.put("status", "error");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Get medical profile statistics
     */
    @GetMapping("/api/winplus/medical-profile/statistics")
    public ResponseEntity<Map<String, Object>> getMedicalProfileStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();

            long totalProfiles = personalMedicalProfileRepository.countTotalProfiles();
            List<PersonalMedicalProfile> incompleteProfiles = personalMedicalProfileRepository.findIncompleteProfiles();

            stats.put("totalProfiles", totalProfiles);
            stats.put("incompleteProfiles", incompleteProfiles.size());
            stats.put("completeProfiles", totalProfiles - incompleteProfiles.size());

            Map<String, Object> response = new HashMap<>();
            response.put("statistics", stats);
            response.put("status", "success");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.out.println("❌ Error retrieving medical profile statistics: " + e.getMessage());

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to fetch medical profile statistics: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
