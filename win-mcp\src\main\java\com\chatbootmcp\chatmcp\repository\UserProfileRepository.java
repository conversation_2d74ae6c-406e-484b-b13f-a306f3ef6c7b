package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.entity.UserProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {
    Optional<UserProfile> findByUser(User user);
    Optional<UserProfile> findByUserUsername(String username);
    boolean existsByUser(User user);
}
