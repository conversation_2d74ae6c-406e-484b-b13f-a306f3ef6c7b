import { TypeAxeEnum } from "src/app/winpharm/enums/Compta/TypeAxe.enum";
import { TypeComptaEnum } from "src/app/winpharm/enums/Compta/TypeCompta.enum";
import { Statut } from "src/app/winpharm/enums/common/Statut.enum";

export interface ComptaAnalytiqueCriteria {
    annee?: number; 
    categorie?: string; 
    codeFt?: string; 
    dateVente?: string; 
    moisDebut?: number; 
    moisExact?: number; 
    moisFin?: number; 
    statut?: Statut; 
    typeAxe?: TypeAxeEnum; 
    typeCompta?: TypeComptaEnum; 
  }