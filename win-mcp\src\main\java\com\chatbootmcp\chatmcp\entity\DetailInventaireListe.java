package com.chatbootmcp.chatmcp.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * DetailInventaireListe entity representing inventory line items
 */
@Entity
@Table(name = "detail_inventaire_liste")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailInventaireListe {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "qte_theorique", precision = 15, scale = 2)
    private BigDecimal qteTheorique;

    @Column(name = "qte_reelle", precision = 15, scale = 2)
    private BigDecimal qteReelle;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "entete_inventaire_id")
    private EnteteInventaire enteteInventaire;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "produit_id")
    private Produit produit;
}
