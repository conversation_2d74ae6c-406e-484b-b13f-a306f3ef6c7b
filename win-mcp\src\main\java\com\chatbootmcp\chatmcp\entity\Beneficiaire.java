package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.time.LocalDate;

@Entity
@Table(name = "beneficiaires")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Beneficiaire {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "nom")
    private String nom;
    
    @Column(name = "prenom")
    private String prenom;
    
    @Column(name = "date_naissance")
    private LocalDate dateNaissance;
    
    @Column(name = "sexe")
    private String sexe;
    
    @Column(name = "num_cin")
    private String numCin;
    
    @Column(name = "num_carte_assurance")
    private String numCarteAssurance;
    
    @Column(name = "relation_avec_client")
    private String relationAvecClient;
    
    @Column(name = "est_actif")
    private Boolean estActif;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Client client;
    
    // Computed property
    public String getNomPrenom() {
        return (nom != null ? nom : "") + " " + (prenom != null ? prenom : "");
    }
}
