import { TypeFacturationTPA } from 'src/app/winpharm/enums/vente/TypeFacturationTPA.enum';
import { StatutProcessFactureTPA } from 'src/app/winpharm/enums/vente/StatutProcessFactureTPA.enum';

// import { Moment } from 'moment';

import { ConventionAssurance } from 'src/app/winpharm/models/assurance/conventionAssurance.model';
import { EnteteVente } from './enteteVente.model';
import { OrganismeAssurance } from 'src/app/winpharm/models/tiers/organisme/organismeAssurance.model';


export class FactureTpa { 
    audited?: boolean;
    convention?: ConventionAssurance;
    dateAnnulation?: any;
    dateEnvoi?: any;
    dateFacture?: any;
    dateReglement?: any;
    id?: number;
    mntTtcTpa?: number;
    mntTtcTpaPartAss?: number;
    mntTtcTpaPartClient?: number;
    nomConvention?: string;
    nomOrganisme?: string;
    numFacture?: number;
    oneVente?: EnteteVente;
    organisme?: OrganismeAssurance;
    statutProcess?: StatutProcessFactureTPA;
    typeFacturationTpa?: TypeFacturationTPA;
    userModifiable?: boolean;
    ventes?: EnteteVente[];
}

