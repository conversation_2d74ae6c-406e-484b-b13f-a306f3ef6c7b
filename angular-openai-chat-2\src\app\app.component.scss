.app-container {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;

  h1 {
    margin: 0;
    color: #333;
  }

  .mode-selector {
    display: flex;
    align-items: center;

    span {
      margin-right: 10px;
      font-weight: 500;
    }

    select {
      padding: 8px 12px;
      border: 1px solid #ccc;
      border-radius: 4px;
      background-color: white;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #0078d4;
      }
    }
  }
}

.content {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;

  p {
    line-height: 1.6;
    color: #333;
  }

  ul {
    padding-left: 20px;

    li {
      margin-bottom: 10px;
      line-height: 1.6;
    }
  }

  strong {
    color: #0078d4;
  }
}