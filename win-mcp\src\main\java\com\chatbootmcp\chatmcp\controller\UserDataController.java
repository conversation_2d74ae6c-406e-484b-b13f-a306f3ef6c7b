package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.dto.response.UserDataResponse;
import com.chatbootmcp.chatmcp.service.UserDataService;
import com.chatbootmcp.chatmcp.service.EnhancedUserDataService;
import com.chatbootmcp.chatmcp.service.DataInitializationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/users/data")
public class UserDataController {

    @Autowired
    private UserDataService userDataService;

    @Autowired
    private EnhancedUserDataService enhancedUserDataService;

    @Autowired
    private DataInitializationService dataInitializationService;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getUserData(
            @AuthenticationPrincipal UserDetails userDetails) {
        // Initialize user data if not exists
        dataInitializationService.initializeUserData(userDetails.getUsername());

        // Return comprehensive user data
        Map<String, Object> userData = enhancedUserDataService.getComprehensiveUserData(userDetails.getUsername());
        return ResponseEntity.ok(userData);
    }

    @GetMapping("/legacy")
    public ResponseEntity<List<UserDataResponse>> getLegacyUserData(
            @AuthenticationPrincipal UserDetails userDetails) {
        return ResponseEntity.ok(userDataService.getUserData(userDetails.getUsername()));
    }

    @GetMapping("/{dataType}")
    public ResponseEntity<UserDataResponse> getUserDataByType(
            @PathVariable String dataType,
            @AuthenticationPrincipal UserDetails userDetails) {
        return ResponseEntity.ok(userDataService.getUserDataByType(userDetails.getUsername(), dataType));
    }

    @PostMapping("/initialize")
    public ResponseEntity<String> initializeUserData(
            @AuthenticationPrincipal UserDetails userDetails) {
        dataInitializationService.initializeUserData(userDetails.getUsername());
        return ResponseEntity.ok("User data initialized successfully");
    }
}
