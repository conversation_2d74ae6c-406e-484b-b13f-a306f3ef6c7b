import { TypeFournisseur } from 'src/app/winpharm/enums/tiers/TypeFournisseur.enum';

import { Ville } from 'src/app/winpharm/models/common/ville.model';


export class FournisseurCriteria {
    audited?: boolean;
    codeFournisseur?: string;
    estActif?: boolean;
    raisonSociale?: string;
    typeFrn?: TypeFournisseur;
    userModifiable?: boolean;
    ville?: Ville;
    enableEnvoiHub?: boolean;
    enableImportHub?: boolean
}

